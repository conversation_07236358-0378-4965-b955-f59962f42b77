'use strict';
/**
 * Activity Controller
 *
 * This controller handles all activity-related operations including:
 * - Daily goals
 * - Check-in/out tracking
 * - Break management
 * - Idle time tracking
 * - Productivity metrics
 */
const Activity = require("../services/activity.service");
const Scheduler = require("../services/scheduler.service");
/**
 * Get all activities for a specific user
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Activity history for the user
 */
const getAllActivities = async (req, res) => {
    const { params } = req;
    const userId = params.id;
    try {
        const result = await Activity.getAllActivities(userId);
        return res.status(200).send(result);
    } catch (error) {
        return res.status(500).json({
            message: "Failed to retrieve activities",
            error: error.message
        });
    }
};
/**
 * Register a daily goal for the user
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Created goal object or error
 */
const registerGoal = async (req, res) => {
    try {
        const result = await Activity.createTodayGoal(req);
        if (result) {
            return res.status(200).send(result);
        } else {
            return res.status(500).json({ message: "Failed to create goal" });
        }
    } catch (error) {
        return res.status(500).json({
            message: "Error creating goal",
            error: error.message
        });
    }
};
/**
 * Update checkout status and synchronize with attendance record
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Success message or error with work hours status
 */
const checkOutStatusUpdate = async (req, res) => {
    try {
        const { _id, user } = req.body;
        // Get the latest attendance record to ensure timestamp synchronization
        const Attendance = require('../models').db.attendance;
        const attendance = await Attendance.findOne({
            user: user,
            checkIn: { $exists: true },
            checkOut: { $exists: true }
        }).sort({ updatedAt: -1 });
        // If we found an attendance record with checkout time, use that timestamp
        let checkoutTime = null;
        if (attendance && attendance.checkOut) {
            checkoutTime = attendance.checkOut;
        }
        // Update the activity record with synchronized checkout time
        const result = await Activity.checkOutStatusUpdate(_id, checkoutTime);
        if (result) {
            // Return enhanced status information
            return res.status(200).json({
                msg: "Checkout status updated successfully",
                data: {
                    earlyCheckOut: result.earlyCheckOutStatus,
                    overtime: result.overtimeStatus,
                    halfDay: result.halfDayStatus,
                    actualHours: result.actualHours,
                    assignedHours: result.assignedHours,
                    overtimeHours: result.overtimeHours || 0,
                    autoCheckout: result.autoCheckout || false
                }
            });
        } else {
            return res.status(500).json({ message: "Failed to update checkout status" });
        }
    } catch (error) {
        return res.status(500).json({
            message: "Error updating checkout status",
            error: error.message
        });
    }
};
/**
 * Update break start status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated break status
 */
const breakInUpdate = async (req, res) => {
    try {
        const result = await Activity.breakInStatusUpdate(req.body);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error updating break start status",
            error: error.message
        });
    }
};
/**
 * Update break end status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated break status
 */
const breakOutUpdate = async (req, res) => {
    try {
        const result = await Activity.breakOutStatusUpdate(req.body);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error updating break end status",
            error: error.message
        });
    }
};
/**
 * Update today's activity status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated status
 */
const todayStatusUpdate = async (req, res) => {
    try {
        const result = await Activity.todayStausUpdate(req.body);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error updating today's status",
            error: error.message
        });
    }
};
/**
 * Update early checkout status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Success message
 */
const earlyCheckOutUpdate = async (req, res) => {
    try {
        const result = await Activity.earlyCheckOutStatus(req.body);
        return res.status(200).json({ msg: "Early checkout recorded successfully" });
    } catch (error) {
        return res.status(500).json({
            message: "Error recording early checkout",
            error: error.message
        });
    }
};
/**
 * Update late check-in status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Success message
 */
const lateCheckInUpdate = async (req, res) => {
    try {
        const result = await Activity.lateCheckInStatus(req.body);
        return res.status(200).json({ msg: "Late check-in recorded successfully" });
    } catch (error) {
        return res.status(500).json({
            message: "Error recording late check-in",
            error: error.message
        });
    }
};
/**
 * Update idle start status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated idle status
 */
const idelStartUpdate = async (req, res) => {
    try {
        const result = await Activity.idelStartStatus(req.body);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error updating idle start status",
            error: error.message
        });
    }
};
/**
 * Update idle end status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated idle status
 */
const idelEndUpdate = async (req, res) => {
    try {
        const result = await Activity.idelEndStatus(req.body);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error updating idle end status",
            error: error.message
        });
    }
};
/**
 * Update productivity status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated productivity status
 */
const productivityUpdate = async (req, res) => {
    try {
        const result = await Activity.productivityStatus(req.body);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error updating productivity status",
            error: error.message
        });
    }
};
/**
 * Update over-limit break status
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated break status
 */
const overLimitBreakUpdate = async (req, res) => {
    try {
        const result = await Activity.overLimitBreakUpdate(req.body);
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error updating over-limit break status",
            error: error.message
        });
    }
};
/**
 * Run auto-checkout process for users who forgot to check out
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Result of auto-checkout process
 */
const runAutoCheckout = async (req, res) => {
    try {
        const result = await Scheduler.autoCheckoutUsers();
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error running auto-checkout process",
            error: error.message
        });
    }
};
/**
 * Mark users as absent who didn't clock in
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Result of absent marking process
 */
const markAbsentUsers = async (req, res) => {
    try {
        const result = await Scheduler.markAbsentUsers();
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Error marking absent users",
            error: error.message
        });
    }
};
const todayActivity = async (req,res) => {
    const result = await Activity.todayActivity(req.params)
    return res.status(200).json(result)
}
/**
 * Update activity when task starts/stops
 */
const updateTaskActivity = async (req, res) => {
    try {
        const { userId, taskId, action, elapsedTime } = req.body;
        // Get today's activity for the user
        const todayActivityResult = await Activity.todayActivity({ id: userId });
        if (todayActivityResult) {
            // Update productivity tracking based on task action
            if (action === 'start') {
                // Start productivity tracking
                await Activity.productivityStatus({
                    _id: todayActivityResult._id,
                    totalSlot: 1,
                    filledSlot: 1
                });
            } else if (action === 'stop' && elapsedTime) {
                // Update total working time with task time
                const minutes = Math.floor(elapsedTime / 60);
                await Activity.productivityStatus({
                    _id: todayActivityResult._id,
                    totalSlot: minutes,
                    filledSlot: minutes
                });
            }
        }
        return res.status(200).json({ message: 'Activity updated successfully' });
    } catch (error) {
        return res.status(500).json({
            message: "Error updating task activity",
            error: error.message
        });
    }
};
const deleteCheckOutTime = async (req,res) => {
    const {id} = req.body
    const result = await Activity.deleteCheckOutTime(id)
    return res.status(200).send(result)
}
/**
 * Get activity data for all users within a date range
 *
 * @param {Object} req - Express request object with query parameters
 * @param {Object} res - Express response object
 * @returns {Object} Activity data for all users
 */
const getAllUsersActivity = async (req, res) => {
    try {
        const { startDate, endDate, view } = req.query;
        if (!startDate || !endDate) {
            return res.status(400).json({
                message: "startDate and endDate are required"
            });
        }
        const result = await Activity.getAllUsersActivity({
            startDate,
            endDate,
            view
        });
        return res.status(200).json(result);
    } catch (error) {
        return res.status(500).json({
            message: "Failed to retrieve users activity",
            error: error.message
        });
    }
};
module.exports = {
    registerGoal,
    checkOutStatusUpdate,
    breakInUpdate,
    breakOutUpdate,
    todayStatusUpdate,
    earlyCheckOutUpdate, // Fixed typo in function name
    lateCheckInUpdate,
    idelStartUpdate,
    idelEndUpdate,
    productivityUpdate,
    getAllActivities,
    overLimitBreakUpdate,
    runAutoCheckout,
    markAbsentUsers,
    todayActivity,
    deleteCheckOutTime,
    getAllUsersActivity,
    updateTaskActivity
};
