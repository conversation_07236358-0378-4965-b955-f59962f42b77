{"ast": null, "code": "import { all, call, put, takeLatest } from 'redux-saga/effects';\nimport { LeaveService } from \"../services\";\nimport { LeaveActions, GeneralActions } from \"../slices/actions\";\nfunction* getLeaves({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(LeaveService.GetLeaves, payload);\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response, _err$response$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error\n    }));\n  }\n}\nfunction* getAllLeaves({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(LeaveService.GetAllLeaves);\n    yield put(LeaveActions.getLeavesSuccess(result.data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response2, _err$response2$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error\n    }));\n  }\n}\nfunction* getLeaveById({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(LeaveService.GetLeaveById, payload);\n    yield put(LeaveActions.getLeaveByIdSuccess(result.data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response3, _err$response3$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error\n    }));\n  }\n}\nfunction* createLeave({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    payload.user = payload.user._id;\n    const result = yield call(LeaveService.CreateLeave, payload);\n    yield put(GeneralActions.addSuccess({\n      action: type,\n      message: result.data.message\n    }));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response4, _err$response4$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error\n    }));\n  }\n}\nfunction* updateLeave({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    payload.user = payload.user._id;\n    const result = yield call(LeaveService.UpdateLeave, payload.id, payload);\n    yield put(GeneralActions.addSuccess({\n      action: type,\n      message: result.data.message\n    }));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response5, _err$response5$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error\n    }));\n  }\n}\nfunction* deleteLeave({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(LeaveService.DeleteLeave, payload);\n    yield put(GeneralActions.addSuccess({\n      action: type,\n      message: result.data.message\n    }));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response6, _err$response6$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response6 = err.response) === null || _err$response6 === void 0 ? void 0 : (_err$response6$data = _err$response6.data) === null || _err$response6$data === void 0 ? void 0 : _err$response6$data.error\n    }));\n  }\n}\nexport function* LeaveWatcher() {\n  yield all([yield takeLatest(LeaveActions.getLeaves.type, getLeaves), yield takeLatest(LeaveActions.getLeaveById.type, getLeaveById), yield takeLatest(LeaveActions.createLeave.type, createLeave), yield takeLatest(LeaveActions.updateLeave.type, updateLeave), yield takeLatest(LeaveActions.deleteLeave.type, deleteLeave), yield takeLatest(LeaveActions.getAllLeaves.type, getAllLeaves)]);\n}\n_c = LeaveWatcher;\nvar _c;\n$RefreshReg$(_c, \"LeaveWatcher\");", "map": {"version": 3, "names": ["all", "call", "put", "take<PERSON><PERSON>t", "LeaveService", "LeaveActions", "GeneralActions", "getLeaves", "type", "payload", "removeError", "startLoading", "result", "GetLeaves", "stopLoading", "err", "_err$response", "_err$response$data", "addError", "action", "message", "response", "data", "error", "getAllLeaves", "GetAllLeaves", "getLeavesSuccess", "_err$response2", "_err$response2$data", "getLeaveById", "GetLeaveById", "getLeaveByIdSuccess", "_err$response3", "_err$response3$data", "createLeave", "user", "_id", "CreateLeave", "addSuccess", "_err$response4", "_err$response4$data", "updateLeave", "UpdateLeave", "id", "_err$response5", "_err$response5$data", "deleteLeave", "DeleteLeave", "_err$response6", "_err$response6$data", "LeaveWatcher", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/LeaveSaga.js"], "sourcesContent": ["import {all, call, put, takeLatest} from 'redux-saga/effects'\r\nimport {LeaveService} from \"../services\";\r\nimport {LeaveActions, GeneralActions} from \"../slices/actions\";\r\nfunction *getLeaves({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(LeaveService.GetLeaves, payload);\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\nfunction *getAllLeaves({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(LeaveService.GetAllLeaves);\r\n\r\n        yield put(LeaveActions.getLeavesSuccess(result.data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\nfunction *getLeaveById({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(LeaveService.GetLeaveById, payload);\r\n        yield put(LeaveActions.getLeaveByIdSuccess(result.data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\nfunction *createLeave({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        payload.user = payload.user._id;\r\n        const result = yield call(LeaveService.CreateLeave, payload);\r\n        yield put(GeneralActions.addSuccess({\r\n            action: type,\r\n            message: result.data.message\r\n        }));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\nfunction *updateLeave({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        payload.user = payload.user._id;\r\n        const result = yield call(LeaveService.UpdateLeave, payload.id, payload);\r\n        yield put(GeneralActions.addSuccess({\r\n            action: type,\r\n            message: result.data.message\r\n        }));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\nfunction *deleteLeave({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n        const result = yield call(LeaveService.DeleteLeave, payload);\r\n        yield put(GeneralActions.addSuccess({\r\n            action: type,\r\n            message: result.data.message\r\n        }));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\nexport function *LeaveWatcher() {\r\n    yield all([\r\n        yield takeLatest(LeaveActions.getLeaves.type, getLeaves),\r\n        yield takeLatest(LeaveActions.getLeaveById.type, getLeaveById),\r\n        yield takeLatest(LeaveActions.createLeave.type, createLeave),\r\n        yield takeLatest(LeaveActions.updateLeave.type, updateLeave),\r\n        yield takeLatest(LeaveActions.deleteLeave.type, deleteLeave),\r\n        yield takeLatest(LeaveActions.getAllLeaves.type, getAllLeaves)\r\n    ]);\r\n}"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAO,oBAAoB;AAC7D,SAAQC,YAAY,QAAO,aAAa;AACxC,SAAQC,YAAY,EAAEC,cAAc,QAAO,mBAAmB;AAC9D,UAAUC,SAASA,CAAC;EAACC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACjC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,YAAY,CAACS,SAAS,EAAEJ,OAAO,CAAC;IAC1D,MAAMP,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOO,GAAG,EAAE;IAAA,IAAAC,aAAA,EAAAC,kBAAA;IACV,MAAMf,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACY,QAAQ,CAAC;MAC9BC,MAAM,EAAEX,IAAI;MACZY,OAAO,GAAAJ,aAAA,GAAED,GAAG,CAACM,QAAQ,cAAAL,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcM,IAAI,cAAAL,kBAAA,uBAAlBA,kBAAA,CAAoBM;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AACA,UAAUC,YAAYA,CAAC;EAAChB,IAAI;EAAEC;AAAO,CAAC,EAAE;EACpC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,YAAY,CAACqB,YAAY,CAAC;IAEpD,MAAMvB,GAAG,CAACG,YAAY,CAACqB,gBAAgB,CAACd,MAAM,CAACU,IAAI,CAAC,CAAC;IACrD,MAAMpB,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOO,GAAG,EAAE;IAAA,IAAAY,cAAA,EAAAC,mBAAA;IACV,MAAM1B,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACY,QAAQ,CAAC;MAC9BC,MAAM,EAAEX,IAAI;MACZY,OAAO,GAAAO,cAAA,GAAEZ,GAAG,CAACM,QAAQ,cAAAM,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcL,IAAI,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBL;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AACA,UAAUM,YAAYA,CAAC;EAACrB,IAAI;EAAEC;AAAO,CAAC,EAAE;EACpC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,YAAY,CAAC0B,YAAY,EAAErB,OAAO,CAAC;IAC7D,MAAMP,GAAG,CAACG,YAAY,CAAC0B,mBAAmB,CAACnB,MAAM,CAACU,IAAI,CAAC,CAAC;IACxD,MAAMpB,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOO,GAAG,EAAE;IAAA,IAAAiB,cAAA,EAAAC,mBAAA;IACV,MAAM/B,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACY,QAAQ,CAAC;MAC9BC,MAAM,EAAEX,IAAI;MACZY,OAAO,GAAAY,cAAA,GAAEjB,GAAG,CAACM,QAAQ,cAAAW,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcV,IAAI,cAAAW,mBAAA,uBAAlBA,mBAAA,CAAoBV;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AACA,UAAUW,WAAWA,CAAC;EAAC1B,IAAI;EAAEC;AAAO,CAAC,EAAE;EACnC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CC,OAAO,CAAC0B,IAAI,GAAG1B,OAAO,CAAC0B,IAAI,CAACC,GAAG;IAC/B,MAAMxB,MAAM,GAAG,MAAMX,IAAI,CAACG,YAAY,CAACiC,WAAW,EAAE5B,OAAO,CAAC;IAC5D,MAAMP,GAAG,CAACI,cAAc,CAACgC,UAAU,CAAC;MAChCnB,MAAM,EAAEX,IAAI;MACZY,OAAO,EAAER,MAAM,CAACU,IAAI,CAACF;IACzB,CAAC,CAAC,CAAC;IACH,MAAMlB,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOO,GAAG,EAAE;IAAA,IAAAwB,cAAA,EAAAC,mBAAA;IACV,MAAMtC,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACY,QAAQ,CAAC;MAC9BC,MAAM,EAAEX,IAAI;MACZY,OAAO,GAAAmB,cAAA,GAAExB,GAAG,CAACM,QAAQ,cAAAkB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjB,IAAI,cAAAkB,mBAAA,uBAAlBA,mBAAA,CAAoBjB;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AACA,UAAUkB,WAAWA,CAAC;EAACjC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACnC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5CC,OAAO,CAAC0B,IAAI,GAAG1B,OAAO,CAAC0B,IAAI,CAACC,GAAG;IAC/B,MAAMxB,MAAM,GAAG,MAAMX,IAAI,CAACG,YAAY,CAACsC,WAAW,EAAEjC,OAAO,CAACkC,EAAE,EAAElC,OAAO,CAAC;IACxE,MAAMP,GAAG,CAACI,cAAc,CAACgC,UAAU,CAAC;MAChCnB,MAAM,EAAEX,IAAI;MACZY,OAAO,EAAER,MAAM,CAACU,IAAI,CAACF;IACzB,CAAC,CAAC,CAAC;IACH,MAAMlB,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOO,GAAG,EAAE;IAAA,IAAA6B,cAAA,EAAAC,mBAAA;IACV,MAAM3C,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACY,QAAQ,CAAC;MAC9BC,MAAM,EAAEX,IAAI;MACZY,OAAO,GAAAwB,cAAA,GAAE7B,GAAG,CAACM,QAAQ,cAAAuB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAActB,IAAI,cAAAuB,mBAAA,uBAAlBA,mBAAA,CAAoBtB;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AACA,UAAUuB,WAAWA,CAAC;EAACtC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACnC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAC5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,YAAY,CAAC2C,WAAW,EAAEtC,OAAO,CAAC;IAC5D,MAAMP,GAAG,CAACI,cAAc,CAACgC,UAAU,CAAC;MAChCnB,MAAM,EAAEX,IAAI;MACZY,OAAO,EAAER,MAAM,CAACU,IAAI,CAACF;IACzB,CAAC,CAAC,CAAC;IACH,MAAMlB,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOO,GAAG,EAAE;IAAA,IAAAiC,cAAA,EAAAC,mBAAA;IACV,MAAM/C,GAAG,CAACI,cAAc,CAACQ,WAAW,CAACN,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACY,QAAQ,CAAC;MAC9BC,MAAM,EAAEX,IAAI;MACZY,OAAO,GAAA4B,cAAA,GAAEjC,GAAG,CAACM,QAAQ,cAAA2B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc1B,IAAI,cAAA2B,mBAAA,uBAAlBA,mBAAA,CAAoB1B;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AACA,OAAO,UAAU2B,YAAYA,CAAA,EAAG;EAC5B,MAAMlD,GAAG,CAAC,CACN,MAAMG,UAAU,CAACE,YAAY,CAACE,SAAS,CAACC,IAAI,EAAED,SAAS,CAAC,EACxD,MAAMJ,UAAU,CAACE,YAAY,CAACwB,YAAY,CAACrB,IAAI,EAAEqB,YAAY,CAAC,EAC9D,MAAM1B,UAAU,CAACE,YAAY,CAAC6B,WAAW,CAAC1B,IAAI,EAAE0B,WAAW,CAAC,EAC5D,MAAM/B,UAAU,CAACE,YAAY,CAACoC,WAAW,CAACjC,IAAI,EAAEiC,WAAW,CAAC,EAC5D,MAAMtC,UAAU,CAACE,YAAY,CAACyC,WAAW,CAACtC,IAAI,EAAEsC,WAAW,CAAC,EAC5D,MAAM3C,UAAU,CAACE,YAAY,CAACmB,YAAY,CAAChB,IAAI,EAAEgB,YAAY,CAAC,CACjE,CAAC;AACN;AAAC2B,EAAA,GATgBD,YAAY;AAAA,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}