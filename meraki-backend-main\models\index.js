'use strict';

const mongoose = require("mongoose");
const db = {};

// Store mongoose instance for potential direct access
db.mongoose = mongoose;

// User and Authentication models
db.user = require("./user.model");

// Organization Structure models
db.department = require("./department.model");
db.designation = require("./designation.model");

// HR Management models
db.attendance = require("./attendance.model");
db.expenses = require("./expenses.model");
db.leave = require("./leave.model");

// Activity Tracking models
db.activity = require("./activity.model");
db.timeline = require("./timeline.model");

// Project Management models
db.product = require("./product.model");
db.client = require("./client.model");
db.screenshot = require("./screenshot.model")
db.sprint = require("./sprint.model");

// System Configuration models
db.setting = require("./setting.model");

// Export the db object with all models
exports.db = db;
