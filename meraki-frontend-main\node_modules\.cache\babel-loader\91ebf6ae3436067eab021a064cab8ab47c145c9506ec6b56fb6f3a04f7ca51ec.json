{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Dashboard\\\\components\\\\Activity.js\",\n  _s = $RefreshSig$();\n/**\r\n * Activity Component - User's daily activity tracking interface\r\n */\n\nimport React, { useEffect, useState, createContext } from \"react\";\nimport Can from \"../../../utils/can\";\nimport { actions, features } from \"../../../constants/permission\";\nimport { Box, Button, Card, Grid, Typography, MenuItem, Select, FormControl, CircularProgress } from \"@mui/material\";\nimport { Timer, AssignmentIndOutlined } from \"@mui/icons-material\";\nimport moment from \"moment\";\nimport { ActivityActions, AttendanceActions, GeneralActions, ProductActions } from \"../../../slices/actions\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { AttendanceSelector, UserSelector } from \"../../../selectors\";\nimport { ActivitySelector } from \"selectors/ActivitySelector\";\nimport { LeaveSelector } from \"selectors/LeaveSelector\";\nimport { SettingSelector } from \"selectors/SettingSelector\";\nimport { styled, useTheme } from \"@mui/material/styles\";\nimport TodayGoal from \"./TodayGoal\";\nimport OtherBreak from \"./BreakReasone\";\nimport EarlyLate from \"./EarlyLate\";\nimport OverLimitBreak from \"./OverLimitBreak\";\nimport WorkHoursStatus from \"./WorkHoursStatus\";\nimport ProductivityChart from \"./ProductivityChart\";\nimport TaskProgressBar from \"./TaskProgressBar\";\nimport AttendanceBarChart from \"./AttendanceBarChart\";\nimport { ProductSelector } from \"selectors/ProductSelector\";\nimport Widget from \"./Widget\";\nimport { getGlobalTimerState, setGlobalTimerState, stopGlobalTimer, startGlobalTimer } from \"../../../utils/timerUtils\";\n\n// Context for sharing activity data with child components\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const activityContext = /*#__PURE__*/createContext();\nconst InfoLog = styled(Box)(() => ({\n  display: \"flex\",\n  justifyContent: \"space-between\"\n}));\n_c = InfoLog;\nexport default function Activity() {\n  _s();\n  var _todayActivity$6, _setting$leaveLimit;\n  const theme = useTheme();\n  const dispatch = useDispatch();\n  const profile = useSelector(UserSelector.profile());\n  const attendances = useSelector(AttendanceSelector.getAttendances());\n  const activities = useSelector(ActivitySelector.getActivityHistory());\n  const countLeave = useSelector(LeaveSelector.countLeave());\n  const setting = useSelector(SettingSelector.getSetting());\n  const product = useSelector(ProductSelector.getProducts());\n\n  // State variables\n  const [controller, setController] = useState(false);\n  const [lunchController, setLunch] = useState(false);\n  const [lunchType, setLunchType] = useState(localStorage.getItem(\"lunchType\") || \"Break In\");\n  const [showBreakPop, setShowBreakPop] = useState(false);\n  const [lateCheckIn, setLateCheckIn] = useState(false);\n  const [earlyCheckOut, setEarlyCheckOut] = useState(false);\n  const [todayStatus, setTodayStatus] = useState(false);\n  const [overLimitBreak, setOverLimitBreak] = useState(false);\n  const [slotController, setSlotController] = useState(true);\n  const [todayActivity, setActivity] = useState([]);\n  const [attendance, setAttendance] = useState({});\n  const [showGoalPopup, setShowGoalPopup] = useState(false);\n  const [workHoursStatus, setWorkHoursStatus] = useState(null);\n  const [showWorkHoursStatus, setShowWorkHoursStatus] = useState(false);\n  const [checkingIn, setCheckingIn] = useState(false);\n  const [checkingOut, setCheckingOut] = useState(false);\n  const [totalCoveredTime, setTotalCoveredTime] = useState(0);\n  const [realTimeTimer, setRealTimeTimer] = useState(null);\n\n  // Helper function to get currently running task\n  const getCurrentRunningTask = () => {\n    const globalTimerState = getGlobalTimerState();\n    return globalTimerState.runningTask;\n  };\n\n  // Helper function to pause currently running task\n  const pauseCurrentTask = () => {\n    const runningTask = getCurrentRunningTask();\n    if (runningTask && product && product.length > 0) {\n      var _product$;\n      const currentElapsedTime = getGlobalTimerState().elapsedTime || 0;\n      console.log(\"🔄 Pausing task due to break:\", {\n        taskId: runningTask.taskId || runningTask._id,\n        elapsedTime: currentElapsedTime\n      });\n      dispatch(ProductActions.pauseTask({\n        taskId: runningTask.taskId || runningTask._id,\n        projectId: runningTask.projectId || ((_product$ = product[0]) === null || _product$ === void 0 ? void 0 : _product$._id),\n        elapsedTime: currentElapsedTime,\n        pauseTime: new Date().toISOString(),\n        date: new Date().toISOString().split(\"T\")[0],\n        startTime: runningTask.startTime || new Date().toISOString()\n      }));\n\n      // Stop the global timer\n      stopGlobalTimer();\n    } else {\n      console.log(\"⚠️ No running task to pause or no products available\");\n    }\n  };\n\n  // Helper function to resume currently paused task\n  const resumeCurrentTask = () => {\n    const runningTask = getCurrentRunningTask();\n    if (runningTask && product && product.length > 0) {\n      var _product$2;\n      console.log(\"🔄 Resuming task after break:\", {\n        taskId: runningTask.taskId || runningTask._id\n      });\n      dispatch(ProductActions.startTask({\n        taskId: runningTask.taskId || runningTask._id,\n        projectId: runningTask.projectId || ((_product$2 = product[0]) === null || _product$2 === void 0 ? void 0 : _product$2._id),\n        date: new Date().toISOString().split(\"T\")[0]\n      }));\n    } else {\n      console.log(\"⚠️ No paused task to resume or no products available\");\n    }\n  };\n\n  // Handle break actions based on lunch type selection\n  useEffect(() => {\n    var _todayActivity$;\n    if (todayActivity.length > 0 && (_todayActivity$ = todayActivity[0]) !== null && _todayActivity$ !== void 0 && _todayActivity$._id && profile && profile._id && attendance && attendance._id) {\n      switch (lunchType) {\n        case \"lunchBreak\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.createLunchBreak({\n            id: attendance._id,\n            lunchIn: new Date()\n          }));\n          dispatch(ActivityActions.breakStartRed({\n            _id: todayActivity[0]._id,\n            type: lunchType,\n            breakStart: new Date().setMilliseconds(0),\n            description: \"Lunch Break\",\n            user: profile._id\n          }));\n          // Pause currently running task\n          pauseCurrentTask();\n          setSlotController(false);\n          break;\n        case \"teaBreak\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.createLunchBreak({\n            id: attendance._id,\n            lunchIn: new Date()\n          }));\n          dispatch(ActivityActions.breakStartRed({\n            _id: todayActivity[0]._id,\n            breakStart: new Date().setMilliseconds(0),\n            type: lunchType,\n            description: \"Tea Break\",\n            user: profile._id\n          }));\n          // Pause currently running task\n          pauseCurrentTask();\n          setSlotController(false);\n          break;\n        case \"other\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.createLunchBreak({\n            id: attendance._id,\n            lunchIn: new Date().setMilliseconds(0),\n            user: profile._id\n          }));\n          // Pause currently running task\n          pauseCurrentTask();\n          setShowBreakPop(true);\n          setSlotController(false);\n          break;\n        case \"breakOut\":\n          setLunch(!lunchController);\n          dispatch(AttendanceActions.updateLunchBreak({\n            id: attendance._id,\n            lunchOut: new Date()\n          }));\n          dispatch(ActivityActions.breakEndRed({\n            _id: todayActivity[0]._id,\n            breakEnd: new Date().setMilliseconds(0),\n            type: lunchType,\n            user: profile._id\n          }));\n          // Resume currently paused task\n          resumeCurrentTask();\n          setSlotController(true);\n          break;\n        default:\n          break;\n      }\n    }\n  }, [lunchType]);\n\n  // Process activity data to find today's activity\n  useEffect(() => {\n    // Only process activity data if we have a valid profile\n    if (!profile || !profile._id) {\n      setActivity([]);\n      return;\n    }\n    if (activities && activities.length > 0) {\n      // Get today's date for comparison\n      const today = new Date();\n      const todayYear = today.getUTCFullYear();\n      const todayMonth = today.getUTCMonth();\n      const todayDate = today.getUTCDate();\n      const result = activities.find(activity => {\n        if (!activity.checkInTime) {\n          return false;\n        }\n\n        // Ensure activity belongs to current user\n        const isCurrentUser = activity.user === profile._id || activity.user._id === profile._id;\n        if (!isCurrentUser) {\n          return false;\n        }\n        const activityDate = new Date(activity.checkInTime);\n        console.log(\"Activity Date: \", activityDate.getUTCFullYear(), todayYear, activityDate.getUTCMonth(), todayMonth, activityDate.getUTCDate(), todayDate);\n        return activityDate.getUTCFullYear() === todayYear && activityDate.getUTCMonth() === todayMonth && activityDate.getUTCDate() === todayDate;\n      });\n      console.log(\"Today Activity Result :\", result);\n      if (result) {\n        setActivity([result]);\n        setShowGoalPopup(false);\n        sessionStorage.setItem(\"hasShownGoalPopup\", \"true\");\n      } else {\n        // No activity found for today for current user\n        setActivity([]);\n      }\n    } else {\n      setActivity([]);\n    }\n  }, [activities, profile]);\n\n  // Handle popup displays for various activity statuses\n  useEffect(() => {\n    if (todayActivity.length > 0) {\n      var _todayActivity$2;\n      // Handle late check-in popup\n      if (todayActivity[0].lateCheckInStatus && !todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"lateCheckInDiscription\"))) {\n        setLateCheckIn(true);\n        // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\n      }\n\n      // Handle early check-out popup\n      if (todayActivity[0].earlyCheckOutStatus && !todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"earlyCheckOutDiscription\"))) {\n        setEarlyCheckOut(true);\n      }\n\n      // Handle today's status popup (after checkout)\n      if (todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"checkOutTime\")) && !todayActivity.some(obj => Object.prototype.hasOwnProperty.call(obj, \"workStatus\"))) {\n        setTodayStatus(true);\n      }\n\n      // Handle over-limit break popup\n      if (todayActivity[0].breaksHistory && todayActivity[0].overLimitBreakStatus === true) {\n        setOverLimitBreak(true);\n        // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\n      }\n\n      // Set covered time from HRMS activity tracking (attendance-based)\n      const workingTime = ((_todayActivity$2 = todayActivity[0]) === null || _todayActivity$2 === void 0 ? void 0 : _todayActivity$2.totalWorkingTime) || 0;\n      console.log(\"Setting total covered time from HRMS:\", workingTime);\n      setTotalCoveredTime(workingTime);\n\n      // Start real-time timer if user is checked in but not checked out\n      if (attendance.checkIn && !attendance.checkOut) {\n        startRealTimeTimer();\n      }\n    }\n  }, [todayActivity]);\n\n  // Fetch initial attendance and activity data\n  useEffect(() => {\n    console.log(\"🔄 Profile changed, current user:\", profile === null || profile === void 0 ? void 0 : profile._id, profile === null || profile === void 0 ? void 0 : profile.name);\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\n      console.log(\"✅ Clearing state for user:\", profile._id);\n\n      // Clear Redux state and local state when profile changes to ensure fresh data\n      dispatch(AttendanceActions.clearAttendances());\n      dispatch(ActivityActions.eraseActivity());\n      setAttendance({});\n      setActivity([]);\n\n      // Always get the current date to ensure we're getting today's data\n      const today = new Date();\n      const formattedDate = today.toISOString().split(\"T\")[0];\n\n      // Clear session storage flags at the start of a new day or when switching users\n      const lastLoginDate = localStorage.getItem(\"lastLoginDate\");\n      const lastUserId = localStorage.getItem(\"lastUserId\");\n      console.log(\"📅 Date check - Today:\", formattedDate, \"Last:\", lastLoginDate, \"User:\", lastUserId, \"Current:\", profile._id);\n      if (lastLoginDate !== formattedDate || lastUserId !== profile._id) {\n        console.log(\"🧹 Clearing session storage for new user/day\");\n        sessionStorage.removeItem(\"hasShownGoalPopup\");\n        sessionStorage.removeItem(\"hasShownLateCheckInPopup\");\n        sessionStorage.removeItem(\"hasShownEarlyCheckOutPopup\");\n        sessionStorage.removeItem(\"hasShownTodayStatusPopup\");\n        sessionStorage.removeItem(\"hasShownOverLimitBreakPopup\");\n        localStorage.setItem(\"lastLoginDate\", formattedDate);\n        localStorage.setItem(\"lastUserId\", profile._id);\n      }\n\n      // Add a small delay to ensure state is cleared before fetching new data\n      setTimeout(() => {\n        console.log(\"📡 Fetching fresh data for user:\", profile._id);\n        dispatch(AttendanceActions.getAttendances({\n          user: profile._id,\n          date: formattedDate,\n          _t: Date.now() // Add timestamp to prevent caching\n        }));\n        dispatch(ActivityActions.getUserActivity({\n          id: profile._id\n        }));\n      }, 100);\n    } else {\n      console.log(\"❌ No profile or permissions, clearing state\");\n      // Clear state when no profile (logged out or switching users)\n      dispatch(AttendanceActions.clearAttendances());\n      dispatch(ActivityActions.eraseActivity());\n      setAttendance({});\n      setActivity([]);\n    }\n  }, [profile, dispatch]);\n\n  // Update local attendance state when attendance data changes\n  useEffect(() => {\n    console.log(\"📊 Processing attendance data for user:\", profile === null || profile === void 0 ? void 0 : profile._id);\n    console.log(\"📊 Attendances received:\", attendances.length, attendances);\n\n    // Only process attendance data if we have a valid profile\n    if (!profile || !profile._id) {\n      console.log(\"❌ No profile, clearing attendance\");\n      setAttendance({});\n      return;\n    }\n\n    // Check if the attendance data is for today\n    const today = new Date();\n    const todayDate = today.toISOString().split(\"T\")[0];\n    if (attendances.length > 0) {\n      // Filter for today's attendance only and ensure it belongs to current user\n      const todayAttendances = attendances.filter(att => {\n        const attDate = new Date(att.checkIn).toISOString().split(\"T\")[0];\n        const isToday = attDate === todayDate;\n        const isCurrentUser = att.user === profile._id || att.user._id === profile._id;\n        console.log(\"🔍 Checking attendance:\", {\n          attDate,\n          isToday,\n          attUser: att.user,\n          currentUser: profile._id,\n          isCurrentUser\n        });\n        return isToday && isCurrentUser;\n      });\n      console.log(\"✅ Today's attendances for current user:\", todayAttendances.length, todayAttendances);\n      if (todayAttendances.length > 0) {\n        console.log(\"✅ Setting attendance:\", todayAttendances[0]);\n        setAttendance(todayAttendances[0]);\n      } else {\n        // No attendance for today for current user\n        console.log(\"❌ No attendance for today for current user\");\n        setAttendance({});\n      }\n    } else {\n      console.log(\"❌ No attendances received\");\n      setAttendance({});\n    }\n  }, [attendances, profile]);\n\n  // Refresh attendance data after check-in or break actions\n  useEffect(() => {\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\n      // Always get a fresh date object to ensure we're getting today's data\n      const today = new Date();\n      const formattedDate = today.toISOString().split(\"T\")[0];\n\n      // Force cache refresh by adding a timestamp\n      const timestamp = Date.now();\n      dispatch(AttendanceActions.getAttendances({\n        user: profile._id,\n        date: formattedDate,\n        _t: timestamp // Add timestamp to prevent caching\n      }));\n    }\n    dispatch(GeneralActions.removeSuccess(AttendanceActions.createAttendance.type));\n  }, [controller, lunchController, profile, dispatch]);\n\n  // Persist lunch type to localStorage\n  useEffect(() => {\n    localStorage.setItem(\"lunchType\", lunchType);\n  }, [lunchType]);\n\n  // Start real-time timer when user checks in\n  useEffect(() => {\n    if (attendance.checkIn && !attendance.checkOut) {\n      startRealTimeTimer();\n    } else {\n      stopRealTimeTimer();\n    }\n    return () => stopRealTimeTimer();\n  }, [attendance]);\n  const startRealTimeTimer = () => {\n    if (realTimeTimer) {\n      clearInterval(realTimeTimer);\n    }\n    const timer = setInterval(() => {\n      calculateRealTimeCoveredTime();\n    }, 1000);\n    setRealTimeTimer(timer);\n  };\n  const stopRealTimeTimer = () => {\n    if (realTimeTimer) {\n      clearInterval(realTimeTimer);\n      setRealTimeTimer(null);\n    }\n  };\n  const calculateRealTimeCoveredTime = () => {\n    var _todayActivity$3, _todayActivity$4, _todayActivity$5, _todayActivity$5$brea;\n    if (!attendance.checkIn || attendance.checkOut) {\n      return;\n    }\n    const checkInTime = new Date(attendance.checkIn);\n    const currentTime = new Date();\n    const totalMinutes = Math.floor((currentTime - checkInTime) / 60000);\n\n    // Calculate total break time\n    let totalBreakMinutes = 0;\n    if ((_todayActivity$3 = todayActivity[0]) !== null && _todayActivity$3 !== void 0 && _todayActivity$3.breaksHistory) {\n      todayActivity[0].breaksHistory.forEach(breakItem => {\n        if (breakItem.breakStartedTime && breakItem.breakEndedTime) {\n          const breakStart = new Date(breakItem.breakStartedTime);\n          const breakEnd = new Date(breakItem.breakEndedTime);\n          totalBreakMinutes += Math.floor((breakEnd - breakStart) / 60000);\n        }\n      });\n    }\n\n    // If currently on break, add current break time\n    if ((_todayActivity$4 = todayActivity[0]) !== null && _todayActivity$4 !== void 0 && _todayActivity$4.breakStatus && ((_todayActivity$5 = todayActivity[0]) === null || _todayActivity$5 === void 0 ? void 0 : (_todayActivity$5$brea = _todayActivity$5.breaksHistory) === null || _todayActivity$5$brea === void 0 ? void 0 : _todayActivity$5$brea.length) > 0) {\n      const lastBreak = todayActivity[0].breaksHistory[todayActivity[0].breaksHistory.length - 1];\n      if (lastBreak.breakStartedTime && !lastBreak.breakEndedTime) {\n        const breakStart = new Date(lastBreak.breakStartedTime);\n        totalBreakMinutes += Math.floor((currentTime - breakStart) / 60000);\n      }\n    }\n    const coveredMinutes = Math.max(0, totalMinutes - totalBreakMinutes);\n    setTotalCoveredTime(coveredMinutes);\n  };\n\n  // Handle user check-in action\n  const handleCheckIn = () => {\n    if (checkingIn) {\n      return;\n    }\n    console.log(\"check in clicked\");\n\n    // Clear any existing attendance data to ensure fresh check-in\n    setAttendance({});\n\n    // Reset session storage for a new check-in\n    const today = new Date();\n    const formattedDate = today.toISOString().split(\"T\")[0];\n    const lastCheckInDate = localStorage.getItem(\"lastCheckInDate\");\n\n    // If it's a new day, clear all session storage flags\n    if (lastCheckInDate !== formattedDate) {\n      sessionStorage.removeItem(\"hasShownGoalPopup\");\n      sessionStorage.removeItem(\"hasShownLateCheckInPopup\");\n      sessionStorage.removeItem(\"hasShownEarlyCheckOutPopup\");\n      sessionStorage.removeItem(\"hasShownTodayStatusPopup\");\n      sessionStorage.removeItem(\"hasShownOverLimitBreakPopup\");\n      localStorage.setItem(\"lastCheckInDate\", formattedDate);\n    }\n\n    // Show goal popup - the actual check-in will happen after goal submission\n    setShowGoalPopup(true);\n  };\n  const handleSubmitGoal = goalData => {\n    console.log(\"Goal submitted:\", goalData);\n\n    // Set checking in state\n    setCheckingIn(true);\n\n    // Close the goal popup\n    setShowGoalPopup(false);\n\n    // Mark as shown to prevent re-showing\n    sessionStorage.setItem(\"hasShownGoalPopup\", \"true\");\n\n    // Check if profile exists\n    if (!profile || !profile._id) {\n      console.error(\"Profile data is missing\");\n      setCheckingIn(false);\n      return;\n    }\n\n    // Create the goal first\n    if (goalData.todaysGoal) {\n      dispatch(ActivityActions.createTodayGoal({\n        id: profile._id,\n        todaysGoal: goalData.todaysGoal\n      }));\n    }\n\n    // Then perform check-in with current timestamp\n    const checkInTime = new Date();\n    dispatch(AttendanceActions.createAttendance({\n      user: profile._id,\n      checkIn: checkInTime\n    }));\n\n    // Force immediate refresh of attendance data with properly formatted date\n    const today = new Date();\n    const formattedDate = today.toISOString().split(\"T\")[0];\n    const timestamp = Date.now();\n\n    // Store the check-in date in localStorage\n    localStorage.setItem(\"lastCheckInDate\", formattedDate);\n\n    // Add a small delay before fetching updated attendance data\n    setTimeout(() => {\n      dispatch(AttendanceActions.getAttendances({\n        user: profile._id,\n        date: formattedDate,\n        _t: timestamp // Add timestamp to prevent caching\n      }));\n\n      // Update controller state\n      setController(!controller);\n\n      // Reset checking state\n      setCheckingIn(false);\n    }, 1000);\n  };\n  // Handle user check-out action\n  const handleCheckOut = () => {\n    if (checkingOut) {\n      return;\n    }\n\n    // Check if profile and attendance data exist\n    if (!profile || !profile._id || !attendance || !attendance._id || !todayActivity || !todayActivity.length) {\n      console.error(\"Missing required data for check-out\");\n      return;\n    }\n    setCheckingOut(true);\n    setController(!controller);\n    dispatch(AttendanceActions.updateAttendance({\n      id: attendance._id,\n      checkOut: new Date(),\n      user: profile._id\n    }));\n    dispatch(ActivityActions.checkOutStatusUpdate({\n      _id: todayActivity[0]._id,\n      user: profile._id\n    }));\n    setTimeout(() => {\n      setCheckingOut(false);\n    }, 2000);\n  };\n\n  // Listen for checkout success to show work hours status\n  const checkoutSuccess = useSelector(state => state.general.success.find(s => s.action === ActivityActions.checkOutStatusUpdate.type));\n\n  // Update work hours status when checkout is successful\n  useEffect(() => {\n    if (checkoutSuccess && checkoutSuccess.data) {\n      setWorkHoursStatus(checkoutSuccess.data);\n      setShowWorkHoursStatus(true);\n      setTimeout(() => {\n        dispatch(GeneralActions.removeSuccess(ActivityActions.checkOutStatusUpdate.type));\n      }, 500);\n    }\n  }, [checkoutSuccess, dispatch]);\n\n  // Handle break type selection\n  const handleBreakOut = event => {\n    setLunchType(event.target.value);\n  };\n\n  // Control the visibility of the break reason popup\n  function breakPopUpController(val) {\n    setShowBreakPop(val);\n  }\n\n  // Check if user has permission to view attendance\n  if (!Can(actions.readSelf, features.attendance)) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 704,\n      columnNumber: 12\n    }, this);\n  }\n  function convertToHoursMinutes(totalMinutes) {\n    // Ensure totalMinutes is a valid number\n    const validMinutes = Math.max(0, parseInt(totalMinutes, 10) || 0);\n    const hours = Math.floor(validMinutes / 60);\n    const minutes = validMinutes % 60;\n    return `${hours}h ${minutes}m`;\n  }\n\n  // UI\n  return /*#__PURE__*/_jsxDEV(Grid, {\n    container: true,\n    spacing: 3,\n    sx: {\n      padding: \"20px\",\n      display: \"flex\",\n      alignItems: \"center\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 8,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          height: \"100%\",\n          padding: \"20px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(ProductivityChart, {\n          todayActivities: todayActivity\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 724,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TaskProgressBar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 723,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 722,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      md: 4,\n      children: /*#__PURE__*/_jsxDEV(Card, {\n        sx: {\n          height: \"100%\",\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Timer, {\n          color: \"primary\",\n          sx: {\n            fontSize: 100\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            align: \"center\",\n            gutterBottom: true,\n            sx: {\n              padding: \"10px\",\n              margin: \"10px\"\n            },\n            children: \"Your Activity Today\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 742,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              align: \"center\",\n              gutterBottom: true,\n              sx: {\n                padding: \"10px\",\n                margin: \"10px\"\n              },\n              children: \"Covered time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 752,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              align: \"center\",\n              children: convertToHoursMinutes(totalCoveredTime)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 761,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 751,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(InfoLog, {\n            children: [\"Check In\", \"Check Out\"].map((label, index) => /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                textAlign: \"center\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                children: label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 769,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle2\",\n                children: attendance[index ? \"checkOut\" : \"checkIn\"] ? moment(attendance[index ? \"checkOut\" : \"checkIn\"]).format(\"HH:mm\") : \"-\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 770,\n                columnNumber: 19\n              }, this)]\n            }, label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 766,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 741,\n          columnNumber: 11\n        }, this), !attendance.checkOut && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mt: 3\n          },\n          children: !attendance.checkIn ? /*#__PURE__*/_jsxDEV(Button, {\n            fullWidth: true,\n            variant: \"contained\",\n            onClick: handleCheckIn,\n            disabled: checkingIn,\n            sx: {\n              backgroundColor: checkingIn ? \"#a5d6a7\" : \"green\",\n              color: \"white\",\n              fontWeight: \"bold\",\n              borderRadius: \"8px\",\n              width: \"150px\",\n              height: \"50px\",\n              boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n              display: \"flex\",\n              alignItems: \"center\",\n              \"&:hover\": {\n                backgroundColor: checkingIn ? \"#a5d6a7\" : \"darkgreen\"\n              }\n            },\n            children: checkingIn ? \"Processing...\" : \"Check In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 782,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              justifyContent: \"space-between\",\n              alignItems: \"center\",\n              width: \"100%\",\n              marginTop: \"10px\",\n              marginBottom: \"10px\",\n              gap: \"10px\"\n            },\n            children: [todayActivity.length > 0 && (_todayActivity$6 = todayActivity[0]) !== null && _todayActivity$6 !== void 0 && _todayActivity$6.breakStatus ? /*#__PURE__*/_jsxDEV(FormControl, {\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"break-label\",\n                value: lunchType,\n                label: \"Break Type\",\n                onChange: handleBreakOut,\n                sx: {\n                  backgroundColor: \"#2e7d32\",\n                  color: \"white\",\n                  fontWeight: \"bold\",\n                  borderRadius: \"8px\",\n                  width: \"150px\",\n                  height: \"50px\",\n                  boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                renderValue: () => \"----\",\n                children: /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"breakOut\",\n                  children: \"Break Out\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 837,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 819,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 818,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(FormControl, {\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                labelId: \"break-label\",\n                value: lunchType || \"\",\n                label: \"Break Type\",\n                onChange: handleBreakOut,\n                sx: {\n                  backgroundColor: \"#2e7d32\",\n                  color: \"white\",\n                  fontWeight: \"bold\",\n                  borderRadius: \"8px\",\n                  width: \"150px\",\n                  height: \"50px\",\n                  boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n                  display: \"flex\",\n                  alignItems: \"center\"\n                },\n                renderValue: () => \"Break In\",\n                children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"\",\n                  disabled: true,\n                  children: \"Break In\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 860,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"lunchBreak\",\n                  children: \"Lunch Break\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 863,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"teaBreak\",\n                  children: \"Tea Break\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 864,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                  value: \"other\",\n                  children: \"Other\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 865,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 842,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 841,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"contained\",\n              color: \"error\",\n              onClick: handleCheckOut,\n              disabled: checkingOut,\n              sx: {\n                backgroundColor: checkingOut ? \"#ef9a9a\" : \"darked\",\n                color: \"white\",\n                fontWeight: \"bold\",\n                borderRadius: \"8px\",\n                width: \"150px\",\n                height: \"50px\",\n                boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\n                display: \"flex\",\n                alignItems: \"center\"\n              },\n              children: checkingOut ? \"Processing...\" : \"Check Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 805,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 779,\n          columnNumber: 13\n        }, this), showGoalPopup && todayActivity.length === 0 && !sessionStorage.getItem(\"hasShownGoalPopup\") && /*#__PURE__*/_jsxDEV(TodayGoal, {\n          title: \"Today's Goal\",\n          task: \"goal\",\n          onSubmit: handleSubmitGoal // Fixed typo\n          ,\n          onClose: () => {\n            setShowGoalPopup(false);\n            sessionStorage.setItem(\"hasShownGoalPopup\", \"true\");\n          },\n          required: true // Make goal mandatory\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 899,\n          columnNumber: 15\n        }, this), showBreakPop && /*#__PURE__*/_jsxDEV(OtherBreak, {\n          openVal: true,\n          settingFun: breakPopUpController,\n          _id: todayActivity[0]._id,\n          profile: profile\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 912,\n          columnNumber: 13\n        }, this), lateCheckIn && /*#__PURE__*/_jsxDEV(EarlyLate, {\n          openVal: true,\n          task: \"late\",\n          dialogTitle: \"Late Check In\",\n          id: todayActivity[0]._id,\n          onClose: () => {\n            // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\n            // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\n            setLateCheckIn(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 921,\n          columnNumber: 13\n        }, this), todayStatus && /*#__PURE__*/_jsxDEV(EarlyLate, {\n          openVal: true,\n          task: \"status\",\n          dialogTitle: \"Work Status\",\n          id: todayActivity[0]._id,\n          onClose: () => {\n            // sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\n            // sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\n            setTodayStatus(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 935,\n          columnNumber: 13\n        }, this), earlyCheckOut && /*#__PURE__*/_jsxDEV(EarlyLate, {\n          openVal: true,\n          task: \"early\",\n          dialogTitle: \"Early Check Out\",\n          id: todayActivity[0]._id,\n          onClose: () => {\n            // sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\n            // sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\n            setEarlyCheckOut(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 949,\n          columnNumber: 13\n        }, this), overLimitBreak && /*#__PURE__*/_jsxDEV(OverLimitBreak, {\n          openVal: true,\n          id: todayActivity[0]._id,\n          onClose: () => {\n            // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\n            // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\n            setOverLimitBreak(false);\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 963,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(WorkHoursStatus, {\n          open: showWorkHoursStatus,\n          statusData: workHoursStatus,\n          onClose: () => setShowWorkHoursStatus(false)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 731,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 730,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      item: true,\n      xs: 12,\n      children: /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        sx: {\n          display: \"flex\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          children: /*#__PURE__*/_jsxDEV(AttendanceBarChart, {\n            activities: activities\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 992,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 991,\n          columnNumber: 11\n        }, this), Can(actions.readSelf, features.leave) && /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          md: 6,\n          container: true,\n          spacing: 2,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Widget, {\n              title: \"Leave Taken\",\n              content: countLeave !== null && countLeave !== void 0 ? countLeave : 0,\n              icon: /*#__PURE__*/_jsxDEV(AssignmentIndOutlined, {\n                sx: {\n                  color: theme.palette.primary.main,\n                  fontSize: 62\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1003,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 999,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 998,\n            columnNumber: 15\n          }, this), Can(actions.read, features.attendance) && /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            children: /*#__PURE__*/_jsxDEV(Widget, {\n              title: \"Quote Leave\",\n              content: (_setting$leaveLimit = setting === null || setting === void 0 ? void 0 : setting.leaveLimit) !== null && _setting$leaveLimit !== void 0 ? _setting$leaveLimit : 0,\n              icon: /*#__PURE__*/_jsxDEV(AssignmentIndOutlined, {\n                sx: {\n                  color: theme.palette.primary.main,\n                  fontSize: 62\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1015,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1011,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1010,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 997,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 985,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 984,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 716,\n    columnNumber: 5\n  }, this);\n}\n_s(Activity, \"BRAq6cp5GMB02qvEY0qUGEMi5xg=\", false, function () {\n  return [useTheme, useDispatch, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector, useSelector];\n});\n_c2 = Activity;\nvar _c, _c2;\n$RefreshReg$(_c, \"InfoLog\");\n$RefreshReg$(_c2, \"Activity\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "createContext", "Can", "actions", "features", "Box", "<PERSON><PERSON>", "Card", "Grid", "Typography", "MenuItem", "Select", "FormControl", "CircularProgress", "Timer", "AssignmentIndOutlined", "moment", "ActivityActions", "AttendanceActions", "GeneralActions", "ProductActions", "useDispatch", "useSelector", "AttendanceSelector", "UserSelector", "ActivitySelector", "LeaveSelector", "SettingSelector", "styled", "useTheme", "TodayGoal", "OtherBreak", "EarlyLate", "OverLimitBreak", "WorkHoursStatus", "ProductivityChart", "TaskProgressBar", "AttendanceBarChart", "ProductSelector", "Widget", "getGlobalTimerState", "setGlobalTimerState", "stopGlobalTimer", "startGlobalTimer", "jsxDEV", "_jsxDEV", "activityContext", "InfoLog", "display", "justifyContent", "_c", "Activity", "_s", "_todayActivity$6", "_setting$leaveLimit", "theme", "dispatch", "profile", "attendances", "getAttendances", "activities", "getActivityHistory", "count<PERSON><PERSON>ve", "setting", "getSetting", "product", "getProducts", "controller", "setController", "lunchController", "setLunch", "lunchType", "setLunchType", "localStorage", "getItem", "showBreakPop", "setShowBreakPop", "lateCheckIn", "setLateCheckIn", "earlyCheckOut", "setEarlyCheckOut", "todayStatus", "setTodayStatus", "overLimitBreak", "setOverLimitBreak", "slotController", "setSlotController", "todayActivity", "setActivity", "attendance", "setAttendance", "showGoalPopup", "setShowGoalPopup", "workHoursStatus", "setWorkHoursStatus", "showWorkHoursStatus", "setShowWorkHoursStatus", "checkingIn", "setCheckingIn", "checkingOut", "setCheckingOut", "totalCoveredTime", "setTotalCoveredTime", "realTimeTimer", "setRealTimeTimer", "getCurrentRunningTask", "globalTimerState", "runningTask", "pauseCurrentTask", "length", "_product$", "currentElapsedTime", "elapsedTime", "console", "log", "taskId", "_id", "pauseTask", "projectId", "pauseTime", "Date", "toISOString", "date", "split", "startTime", "resumeCurrentTask", "_product$2", "startTask", "_todayActivity$", "createLunchBreak", "id", "lunchIn", "breakStartRed", "type", "breakStart", "setMilliseconds", "description", "user", "updateLunchBreak", "lunchOut", "breakEndRed", "breakEnd", "today", "todayYear", "getUTCFullYear", "todayMonth", "getUTCMonth", "todayDate", "getUTCDate", "result", "find", "activity", "checkInTime", "isCurrentUser", "activityDate", "sessionStorage", "setItem", "_todayActivity$2", "lateCheckInStatus", "some", "obj", "Object", "prototype", "hasOwnProperty", "call", "earlyCheckOutStatus", "breaksHistory", "overLimitBreakStatus", "workingTime", "totalWorkingTime", "checkIn", "checkOut", "startRealTimeTimer", "name", "read", "clearAttendances", "eraseActivity", "formattedDate", "lastLoginDate", "lastUserId", "removeItem", "setTimeout", "_t", "now", "getUserActivity", "todayAttendances", "filter", "att", "attDate", "isToday", "attUser", "currentUser", "timestamp", "removeSuccess", "createAttendance", "stopRealTimeTimer", "clearInterval", "timer", "setInterval", "calculateRealTimeCoveredTime", "_todayActivity$3", "_todayActivity$4", "_todayActivity$5", "_todayActivity$5$brea", "currentTime", "totalMinutes", "Math", "floor", "totalBreakMinutes", "for<PERSON>ach", "breakItem", "breakStartedTime", "breakEndedTime", "breakStatus", "lastBreak", "coveredMinutes", "max", "handleCheckIn", "lastCheckInDate", "handleSubmitGoal", "goalData", "error", "todaysGoal", "createTodayGoal", "handleCheckOut", "updateAttendance", "checkOutStatusUpdate", "checkoutSuccess", "state", "general", "success", "s", "action", "data", "handleBreakOut", "event", "target", "value", "breakPopUpController", "val", "readSelf", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "convertToHoursMinutes", "validMinutes", "parseInt", "hours", "minutes", "container", "spacing", "sx", "padding", "alignItems", "children", "item", "xs", "md", "height", "todayActivities", "flexDirection", "color", "fontSize", "variant", "align", "gutterBottom", "margin", "map", "label", "index", "textAlign", "format", "mt", "fullWidth", "onClick", "disabled", "backgroundColor", "fontWeight", "borderRadius", "width", "boxShadow", "style", "marginTop", "marginBottom", "gap", "labelId", "onChange", "renderValue", "title", "task", "onSubmit", "onClose", "required", "openVal", "<PERSON><PERSON><PERSON>", "dialogTitle", "open", "statusData", "leave", "content", "icon", "palette", "primary", "main", "leaveLimit", "_c2", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Dashboard/components/Activity.js"], "sourcesContent": ["/**\r\n * Activity Component - User's daily activity tracking interface\r\n */\r\n\r\nimport React, { useEffect, useState, createContext } from \"react\";\r\nimport Can from \"../../../utils/can\";\r\nimport { actions, features } from \"../../../constants/permission\";\r\nimport {\r\n  Box,\r\n  Button,\r\n  Card,\r\n  Grid,\r\n  Typography,\r\n  MenuItem,\r\n  Select,\r\n  FormControl,\r\n  CircularProgress,\r\n} from \"@mui/material\";\r\nimport { Timer, AssignmentIndOutlined } from \"@mui/icons-material\";\r\nimport moment from \"moment\";\r\nimport {\r\n  ActivityActions,\r\n  AttendanceActions,\r\n  GeneralActions,\r\n  ProductActions,\r\n} from \"../../../slices/actions\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { AttendanceSelector, UserSelector } from \"../../../selectors\";\r\nimport { ActivitySelector } from \"selectors/ActivitySelector\";\r\nimport { LeaveSelector } from \"selectors/LeaveSelector\";\r\nimport { SettingSelector } from \"selectors/SettingSelector\";\r\nimport { styled, useTheme } from \"@mui/material/styles\";\r\nimport TodayGoal from \"./TodayGoal\";\r\nimport OtherBreak from \"./BreakReasone\";\r\nimport EarlyLate from \"./EarlyLate\";\r\nimport OverLimitBreak from \"./OverLimitBreak\";\r\nimport WorkHoursStatus from \"./WorkHoursStatus\";\r\nimport ProductivityChart from \"./ProductivityChart\";\r\nimport TaskProgressBar from \"./TaskProgressBar\";\r\nimport AttendanceBarChart from \"./AttendanceBarChart\";\r\nimport { ProductSelector } from \"selectors/ProductSelector\";\r\nimport Widget from \"./Widget\";\r\nimport { getGlobalTimerState, setGlobalTimerState, stopGlobalTimer, startGlobalTimer } from \"../../../utils/timerUtils\";\r\n\r\n// Context for sharing activity data with child components\r\nexport const activityContext = createContext();\r\n\r\nconst InfoLog = styled(Box)(() => ({\r\n  display: \"flex\",\r\n  justifyContent: \"space-between\",\r\n}));\r\n\r\nexport default function Activity() {\r\n  const theme = useTheme();\r\n  const dispatch = useDispatch();\r\n  const profile = useSelector(UserSelector.profile());\r\n  const attendances = useSelector(AttendanceSelector.getAttendances());\r\n  const activities = useSelector(ActivitySelector.getActivityHistory());\r\n  const countLeave = useSelector(LeaveSelector.countLeave());\r\n  const setting = useSelector(SettingSelector.getSetting());\r\n  const product = useSelector(ProductSelector.getProducts());\r\n\r\n  // State variables\r\n  const [controller, setController] = useState(false);\r\n  const [lunchController, setLunch] = useState(false);\r\n  const [lunchType, setLunchType] = useState(\r\n    localStorage.getItem(\"lunchType\") || \"Break In\"\r\n  );\r\n  const [showBreakPop, setShowBreakPop] = useState(false);\r\n  const [lateCheckIn, setLateCheckIn] = useState(false);\r\n  const [earlyCheckOut, setEarlyCheckOut] = useState(false);\r\n  const [todayStatus, setTodayStatus] = useState(false);\r\n  const [overLimitBreak, setOverLimitBreak] = useState(false);\r\n  const [slotController, setSlotController] = useState(true);\r\n  const [todayActivity, setActivity] = useState([]);\r\n  const [attendance, setAttendance] = useState({});\r\n  const [showGoalPopup, setShowGoalPopup] = useState(false);\r\n  const [workHoursStatus, setWorkHoursStatus] = useState(null);\r\n  const [showWorkHoursStatus, setShowWorkHoursStatus] = useState(false);\r\n  const [checkingIn, setCheckingIn] = useState(false);\r\n  const [checkingOut, setCheckingOut] = useState(false);\r\n  const [totalCoveredTime,setTotalCoveredTime] = useState(0)\r\n  const [realTimeTimer, setRealTimeTimer] = useState(null)\r\n\r\n  // Helper function to get currently running task\r\n  const getCurrentRunningTask = () => {\r\n    const globalTimerState = getGlobalTimerState();\r\n    return globalTimerState.runningTask;\r\n  };\r\n\r\n  // Helper function to pause currently running task\r\n  const pauseCurrentTask = () => {\r\n    const runningTask = getCurrentRunningTask();\r\n    if (runningTask && product && product.length > 0) {\r\n      const currentElapsedTime = getGlobalTimerState().elapsedTime || 0;\r\n\r\n      console.log(\"🔄 Pausing task due to break:\", {\r\n        taskId: runningTask.taskId || runningTask._id,\r\n        elapsedTime: currentElapsedTime\r\n      });\r\n\r\n      dispatch(ProductActions.pauseTask({\r\n        taskId: runningTask.taskId || runningTask._id,\r\n        projectId: runningTask.projectId || product[0]?._id,\r\n        elapsedTime: currentElapsedTime,\r\n        pauseTime: new Date().toISOString(),\r\n        date: new Date().toISOString().split(\"T\")[0],\r\n        startTime: runningTask.startTime || new Date().toISOString()\r\n      }));\r\n\r\n      // Stop the global timer\r\n      stopGlobalTimer();\r\n    } else {\r\n      console.log(\"⚠️ No running task to pause or no products available\");\r\n    }\r\n  };\r\n\r\n  // Helper function to resume currently paused task\r\n  const resumeCurrentTask = () => {\r\n    const runningTask = getCurrentRunningTask();\r\n    if (runningTask && product && product.length > 0) {\r\n      console.log(\"🔄 Resuming task after break:\", {\r\n        taskId: runningTask.taskId || runningTask._id\r\n      });\r\n\r\n      dispatch(ProductActions.startTask({\r\n        taskId: runningTask.taskId || runningTask._id,\r\n        projectId: runningTask.projectId || product[0]?._id,\r\n        date: new Date().toISOString().split(\"T\")[0]\r\n      }));\r\n    } else {\r\n      console.log(\"⚠️ No paused task to resume or no products available\");\r\n    }\r\n  };\r\n\r\n  // Handle break actions based on lunch type selection\r\n  useEffect(() => {\r\n    if (\r\n      todayActivity.length > 0 &&\r\n      todayActivity[0]?._id &&\r\n      profile &&\r\n      profile._id &&\r\n      attendance &&\r\n      attendance._id\r\n    ) {\r\n      switch (lunchType) {\r\n        case \"lunchBreak\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.createLunchBreak({\r\n              id: attendance._id,\r\n              lunchIn: new Date(),\r\n            })\r\n          );\r\n          dispatch(\r\n            ActivityActions.breakStartRed({\r\n              _id: todayActivity[0]._id,\r\n              type: lunchType,\r\n              breakStart: new Date().setMilliseconds(0),\r\n              description: \"Lunch Break\",\r\n              user: profile._id,\r\n            })\r\n          );\r\n          // Pause currently running task\r\n          pauseCurrentTask();\r\n          setSlotController(false);\r\n          break;\r\n\r\n        case \"teaBreak\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.createLunchBreak({\r\n              id: attendance._id,\r\n              lunchIn: new Date(),\r\n            })\r\n          );\r\n          dispatch(\r\n            ActivityActions.breakStartRed({\r\n              _id: todayActivity[0]._id,\r\n              breakStart: new Date().setMilliseconds(0),\r\n              type: lunchType,\r\n              description: \"Tea Break\",\r\n              user: profile._id,\r\n            })\r\n          );\r\n          // Pause currently running task\r\n          pauseCurrentTask();\r\n          setSlotController(false);\r\n          break;\r\n\r\n        case \"other\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.createLunchBreak({\r\n              id: attendance._id,\r\n              lunchIn: new Date().setMilliseconds(0),\r\n              user: profile._id,\r\n            })\r\n          );\r\n          // Pause currently running task\r\n          pauseCurrentTask();\r\n          setShowBreakPop(true);\r\n          setSlotController(false);\r\n          break;\r\n\r\n        case \"breakOut\":\r\n          setLunch(!lunchController);\r\n          dispatch(\r\n            AttendanceActions.updateLunchBreak({\r\n              id: attendance._id,\r\n              lunchOut: new Date(),\r\n            })\r\n          );\r\n          dispatch(\r\n            ActivityActions.breakEndRed({\r\n              _id: todayActivity[0]._id,\r\n              breakEnd: new Date().setMilliseconds(0),\r\n              type: lunchType,\r\n              user: profile._id,\r\n            })\r\n          );\r\n          // Resume currently paused task\r\n          resumeCurrentTask();\r\n          setSlotController(true);\r\n          break;\r\n\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n  }, [lunchType]);\r\n\r\n  // Process activity data to find today's activity\r\n  useEffect(() => {\r\n    // Only process activity data if we have a valid profile\r\n    if (!profile || !profile._id) {\r\n      setActivity([]);\r\n      return;\r\n    }\r\n\r\n    if (activities && activities.length > 0) {\r\n      // Get today's date for comparison\r\n      const today = new Date();\r\n      const todayYear = today.getUTCFullYear();\r\n      const todayMonth = today.getUTCMonth();\r\n      const todayDate = today.getUTCDate();\r\n\r\n      const result = activities.find((activity) => {\r\n        if (!activity.checkInTime) { return false; }\r\n\r\n        // Ensure activity belongs to current user\r\n        const isCurrentUser = activity.user === profile._id || activity.user._id === profile._id;\r\n        if (!isCurrentUser) { return false; }\r\n\r\n        const activityDate = new Date(activity.checkInTime);\r\n        console.log(\"Activity Date: \",  activityDate.getUTCFullYear() , todayYear,activityDate.getUTCMonth() , todayMonth ,activityDate.getUTCDate() , todayDate);\r\n        return (\r\n          activityDate.getUTCFullYear() === todayYear &&\r\n          activityDate.getUTCMonth() === todayMonth &&\r\n          activityDate.getUTCDate() === todayDate\r\n        );\r\n      });\r\n\r\n      console.log(\"Today Activity Result :\", result);\r\n      if (result) {\r\n        setActivity([result]);\r\n        setShowGoalPopup(false);\r\n        sessionStorage.setItem(\"hasShownGoalPopup\", \"true\");\r\n      } else {\r\n        // No activity found for today for current user\r\n        setActivity([]);\r\n      }\r\n    } else {\r\n      setActivity([]);\r\n    }\r\n  }, [activities, profile]);\r\n\r\n  // Handle popup displays for various activity statuses\r\nuseEffect(() => {\r\n  if (todayActivity.length > 0) {\r\n    // Handle late check-in popup\r\n    if (\r\n      todayActivity[0].lateCheckInStatus &&\r\n      !todayActivity.some((obj) =>\r\n        Object.prototype.hasOwnProperty.call(obj, \"lateCheckInDiscription\")\r\n      )\r\n    ) {\r\n      setLateCheckIn(true);\r\n      // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\r\n    }\r\n\r\n    // Handle early check-out popup\r\n    if (\r\n      todayActivity[0].earlyCheckOutStatus &&\r\n      !todayActivity.some((obj) =>\r\n        Object.prototype.hasOwnProperty.call(obj, \"earlyCheckOutDiscription\")\r\n      )\r\n    ) {\r\n      setEarlyCheckOut(true);\r\n    }\r\n\r\n    // Handle today's status popup (after checkout)\r\n    if (\r\n      todayActivity.some((obj) =>\r\n        Object.prototype.hasOwnProperty.call(obj, \"checkOutTime\")\r\n      ) &&\r\n      !todayActivity.some((obj) =>\r\n        Object.prototype.hasOwnProperty.call(obj, \"workStatus\")\r\n      )\r\n    ) {\r\n      setTodayStatus(true);\r\n    }\r\n\r\n      // Handle over-limit break popup\r\n      if (\r\n        todayActivity[0].breaksHistory &&\r\n        todayActivity[0].overLimitBreakStatus === true\r\n      ) {\r\n        setOverLimitBreak(true);\r\n        // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\r\n      }\r\n\r\n      // Set covered time from HRMS activity tracking (attendance-based)\r\n      const workingTime = todayActivity[0]?.totalWorkingTime || 0;\r\n      console.log(\"Setting total covered time from HRMS:\", workingTime);\r\n      setTotalCoveredTime(workingTime);\r\n      \r\n      // Start real-time timer if user is checked in but not checked out\r\n      if (attendance.checkIn && !attendance.checkOut) {\r\n        startRealTimeTimer();\r\n      }\r\n    }\r\n  }, [todayActivity]);\r\n\r\n  // Fetch initial attendance and activity data\r\n  useEffect(() => {\r\n    console.log(\"🔄 Profile changed, current user:\", profile?._id, profile?.name);\r\n\r\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\r\n      console.log(\"✅ Clearing state for user:\", profile._id);\r\n\r\n      // Clear Redux state and local state when profile changes to ensure fresh data\r\n      dispatch(AttendanceActions.clearAttendances());\r\n      dispatch(ActivityActions.eraseActivity());\r\n      setAttendance({});\r\n      setActivity([]);\r\n\r\n      // Always get the current date to ensure we're getting today's data\r\n      const today = new Date();\r\n      const formattedDate = today.toISOString().split(\"T\")[0];\r\n\r\n      // Clear session storage flags at the start of a new day or when switching users\r\n      const lastLoginDate = localStorage.getItem(\"lastLoginDate\");\r\n      const lastUserId = localStorage.getItem(\"lastUserId\");\r\n\r\n      console.log(\"📅 Date check - Today:\", formattedDate, \"Last:\", lastLoginDate, \"User:\", lastUserId, \"Current:\", profile._id);\r\n\r\n      if (lastLoginDate !== formattedDate || lastUserId !== profile._id) {\r\n        console.log(\"🧹 Clearing session storage for new user/day\");\r\n        sessionStorage.removeItem(\"hasShownGoalPopup\");\r\n        sessionStorage.removeItem(\"hasShownLateCheckInPopup\");\r\n        sessionStorage.removeItem(\"hasShownEarlyCheckOutPopup\");\r\n        sessionStorage.removeItem(\"hasShownTodayStatusPopup\");\r\n        sessionStorage.removeItem(\"hasShownOverLimitBreakPopup\");\r\n        localStorage.setItem(\"lastLoginDate\", formattedDate);\r\n        localStorage.setItem(\"lastUserId\", profile._id);\r\n      }\r\n\r\n      // Add a small delay to ensure state is cleared before fetching new data\r\n      setTimeout(() => {\r\n        console.log(\"📡 Fetching fresh data for user:\", profile._id);\r\n        dispatch(\r\n          AttendanceActions.getAttendances({\r\n            user: profile._id,\r\n            date: formattedDate,\r\n            _t: Date.now(), // Add timestamp to prevent caching\r\n          })\r\n        );\r\n        dispatch(\r\n          ActivityActions.getUserActivity({\r\n            id: profile._id,\r\n          })\r\n        );\r\n      }, 100);\r\n    } else {\r\n      console.log(\"❌ No profile or permissions, clearing state\");\r\n      // Clear state when no profile (logged out or switching users)\r\n      dispatch(AttendanceActions.clearAttendances());\r\n      dispatch(ActivityActions.eraseActivity());\r\n      setAttendance({});\r\n      setActivity([]);\r\n    }\r\n  }, [profile, dispatch]);\r\n\r\n  // Update local attendance state when attendance data changes\r\n  useEffect(() => {\r\n    console.log(\"📊 Processing attendance data for user:\", profile?._id);\r\n    console.log(\"📊 Attendances received:\", attendances.length, attendances);\r\n\r\n    // Only process attendance data if we have a valid profile\r\n    if (!profile || !profile._id) {\r\n      console.log(\"❌ No profile, clearing attendance\");\r\n      setAttendance({});\r\n      return;\r\n    }\r\n\r\n    // Check if the attendance data is for today\r\n    const today = new Date();\r\n    const todayDate = today.toISOString().split(\"T\")[0];\r\n\r\n    if (attendances.length > 0) {\r\n      // Filter for today's attendance only and ensure it belongs to current user\r\n      const todayAttendances = attendances.filter((att) => {\r\n        const attDate = new Date(att.checkIn).toISOString().split(\"T\")[0];\r\n        const isToday = attDate === todayDate;\r\n        const isCurrentUser = att.user === profile._id || att.user._id === profile._id;\r\n        console.log(\"🔍 Checking attendance:\", {\r\n          attDate,\r\n          isToday,\r\n          attUser: att.user,\r\n          currentUser: profile._id,\r\n          isCurrentUser\r\n        });\r\n        return isToday && isCurrentUser;\r\n      });\r\n\r\n      console.log(\"✅ Today's attendances for current user:\", todayAttendances.length, todayAttendances);\r\n\r\n      if (todayAttendances.length > 0) {\r\n        console.log(\"✅ Setting attendance:\", todayAttendances[0]);\r\n        setAttendance(todayAttendances[0]);\r\n      } else {\r\n        // No attendance for today for current user\r\n        console.log(\"❌ No attendance for today for current user\");\r\n        setAttendance({});\r\n      }\r\n    } else {\r\n      console.log(\"❌ No attendances received\");\r\n      setAttendance({});\r\n    }\r\n  }, [attendances, profile]);\r\n\r\n  // Refresh attendance data after check-in or break actions\r\n  useEffect(() => {\r\n    if (Can(actions.read, features.attendance) && profile && profile._id) {\r\n      // Always get a fresh date object to ensure we're getting today's data\r\n      const today = new Date();\r\n      const formattedDate = today.toISOString().split(\"T\")[0];\r\n\r\n      // Force cache refresh by adding a timestamp\r\n      const timestamp = Date.now();\r\n\r\n      dispatch(\r\n        AttendanceActions.getAttendances({\r\n          user: profile._id,\r\n          date: formattedDate,\r\n          _t: timestamp, // Add timestamp to prevent caching\r\n        })\r\n      );\r\n    }\r\n    dispatch(\r\n      GeneralActions.removeSuccess(AttendanceActions.createAttendance.type)\r\n    );\r\n  }, [controller, lunchController, profile, dispatch]);\r\n\r\n  // Persist lunch type to localStorage\r\n  useEffect(() => {\r\n    localStorage.setItem(\"lunchType\", lunchType);\r\n  }, [lunchType]);\r\n  \r\n  // Start real-time timer when user checks in\r\n  useEffect(() => {\r\n    if (attendance.checkIn && !attendance.checkOut) {\r\n      startRealTimeTimer();\r\n    } else {\r\n      stopRealTimeTimer();\r\n    }\r\n    \r\n    return () => stopRealTimeTimer();\r\n  }, [attendance]);\r\n  \r\n  const startRealTimeTimer = () => {\r\n    if (realTimeTimer) {\r\n      clearInterval(realTimeTimer);\r\n    }\r\n    \r\n    const timer = setInterval(() => {\r\n      calculateRealTimeCoveredTime();\r\n    }, 1000);\r\n    \r\n    setRealTimeTimer(timer);\r\n  };\r\n  \r\n  const stopRealTimeTimer = () => {\r\n    if (realTimeTimer) {\r\n      clearInterval(realTimeTimer);\r\n      setRealTimeTimer(null);\r\n    }\r\n  };\r\n  \r\n  const calculateRealTimeCoveredTime = () => {\r\n    if (!attendance.checkIn || attendance.checkOut) {\r\n      return;\r\n    }\r\n    \r\n    const checkInTime = new Date(attendance.checkIn);\r\n    const currentTime = new Date();\r\n    const totalMinutes = Math.floor((currentTime - checkInTime) / 60000);\r\n    \r\n    // Calculate total break time\r\n    let totalBreakMinutes = 0;\r\n    if (todayActivity[0]?.breaksHistory) {\r\n      todayActivity[0].breaksHistory.forEach(breakItem => {\r\n        if (breakItem.breakStartedTime && breakItem.breakEndedTime) {\r\n          const breakStart = new Date(breakItem.breakStartedTime);\r\n          const breakEnd = new Date(breakItem.breakEndedTime);\r\n          totalBreakMinutes += Math.floor((breakEnd - breakStart) / 60000);\r\n        }\r\n      });\r\n    }\r\n    \r\n    // If currently on break, add current break time\r\n    if (todayActivity[0]?.breakStatus && todayActivity[0]?.breaksHistory?.length > 0) {\r\n      const lastBreak = todayActivity[0].breaksHistory[todayActivity[0].breaksHistory.length - 1];\r\n      if (lastBreak.breakStartedTime && !lastBreak.breakEndedTime) {\r\n        const breakStart = new Date(lastBreak.breakStartedTime);\r\n        totalBreakMinutes += Math.floor((currentTime - breakStart) / 60000);\r\n      }\r\n    }\r\n    \r\n    const coveredMinutes = Math.max(0, totalMinutes - totalBreakMinutes);\r\n    setTotalCoveredTime(coveredMinutes);\r\n  };\r\n\r\n  // Handle user check-in action\r\n  const handleCheckIn = () => {\r\n    if (checkingIn) {\r\n      return;\r\n    }\r\n\r\n    console.log(\"check in clicked\");\r\n\r\n    // Clear any existing attendance data to ensure fresh check-in\r\n    setAttendance({});\r\n\r\n    // Reset session storage for a new check-in\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split(\"T\")[0];\r\n    const lastCheckInDate = localStorage.getItem(\"lastCheckInDate\");\r\n\r\n    // If it's a new day, clear all session storage flags\r\n    if (lastCheckInDate !== formattedDate) {\r\n      sessionStorage.removeItem(\"hasShownGoalPopup\");\r\n      sessionStorage.removeItem(\"hasShownLateCheckInPopup\");\r\n      sessionStorage.removeItem(\"hasShownEarlyCheckOutPopup\");\r\n      sessionStorage.removeItem(\"hasShownTodayStatusPopup\");\r\n      sessionStorage.removeItem(\"hasShownOverLimitBreakPopup\");\r\n      localStorage.setItem(\"lastCheckInDate\", formattedDate);\r\n    }\r\n\r\n    // Show goal popup - the actual check-in will happen after goal submission\r\n    setShowGoalPopup(true);\r\n  };\r\n\r\n  const handleSubmitGoal = (goalData) => {\r\n    console.log(\"Goal submitted:\", goalData);\r\n\r\n    // Set checking in state\r\n    setCheckingIn(true);\r\n\r\n    // Close the goal popup\r\n    setShowGoalPopup(false);\r\n\r\n    // Mark as shown to prevent re-showing\r\n    sessionStorage.setItem(\"hasShownGoalPopup\", \"true\");\r\n\r\n    // Check if profile exists\r\n    if (!profile || !profile._id) {\r\n      console.error(\"Profile data is missing\");\r\n      setCheckingIn(false);\r\n      return;\r\n    }\r\n\r\n    // Create the goal first\r\n    if (goalData.todaysGoal) {\r\n      dispatch(\r\n        ActivityActions.createTodayGoal({\r\n          id: profile._id,\r\n          todaysGoal: goalData.todaysGoal,\r\n        })\r\n      );\r\n    }\r\n\r\n    // Then perform check-in with current timestamp\r\n    const checkInTime = new Date();\r\n    dispatch(\r\n      AttendanceActions.createAttendance({\r\n        user: profile._id,\r\n        checkIn: checkInTime,\r\n      })\r\n    );\r\n\r\n    // Force immediate refresh of attendance data with properly formatted date\r\n    const today = new Date();\r\n    const formattedDate = today.toISOString().split(\"T\")[0];\r\n    const timestamp = Date.now();\r\n\r\n    // Store the check-in date in localStorage\r\n    localStorage.setItem(\"lastCheckInDate\", formattedDate);\r\n\r\n    // Add a small delay before fetching updated attendance data\r\n    setTimeout(() => {\r\n      dispatch(\r\n        AttendanceActions.getAttendances({\r\n          user: profile._id,\r\n          date: formattedDate,\r\n          _t: timestamp, // Add timestamp to prevent caching\r\n        })\r\n      );\r\n\r\n      // Update controller state\r\n      setController(!controller);\r\n\r\n      // Reset checking state\r\n      setCheckingIn(false);\r\n    }, 1000);\r\n  };\r\n  // Handle user check-out action\r\n  const handleCheckOut = () => {\r\n    if (checkingOut) {\r\n      return;\r\n    }\r\n\r\n    // Check if profile and attendance data exist\r\n    if (\r\n      !profile ||\r\n      !profile._id ||\r\n      !attendance ||\r\n      !attendance._id ||\r\n      !todayActivity ||\r\n      !todayActivity.length\r\n    ) {\r\n      console.error(\"Missing required data for check-out\");\r\n      return;\r\n    }\r\n\r\n    setCheckingOut(true);\r\n    setController(!controller);\r\n\r\n    dispatch(\r\n      AttendanceActions.updateAttendance({\r\n        id: attendance._id,\r\n        checkOut: new Date(),\r\n        user: profile._id,\r\n      })\r\n    );\r\n\r\n    dispatch(\r\n      ActivityActions.checkOutStatusUpdate({\r\n        _id: todayActivity[0]._id,\r\n        user: profile._id,\r\n      })\r\n    );\r\n\r\n    setTimeout(() => {\r\n      setCheckingOut(false);\r\n    }, 2000);\r\n  };\r\n\r\n  // Listen for checkout success to show work hours status\r\n  const checkoutSuccess = useSelector((state) =>\r\n    state.general.success.find(\r\n      (s) => s.action === ActivityActions.checkOutStatusUpdate.type\r\n    )\r\n  );\r\n\r\n  // Update work hours status when checkout is successful\r\n  useEffect(() => {\r\n    if (checkoutSuccess && checkoutSuccess.data) {\r\n      setWorkHoursStatus(checkoutSuccess.data);\r\n      setShowWorkHoursStatus(true);\r\n      setTimeout(() => {\r\n        dispatch(\r\n          GeneralActions.removeSuccess(\r\n            ActivityActions.checkOutStatusUpdate.type\r\n          )\r\n        );\r\n      }, 500);\r\n    }\r\n  }, [checkoutSuccess, dispatch]);\r\n\r\n  // Handle break type selection\r\n  const handleBreakOut = (event) => {\r\n    setLunchType(event.target.value);\r\n  };\r\n\r\n  // Control the visibility of the break reason popup\r\n  function breakPopUpController(val) {\r\n    setShowBreakPop(val);\r\n  }\r\n\r\n  // Check if user has permission to view attendance\r\n  if (!Can(actions.readSelf, features.attendance)) {\r\n    return <div></div>;\r\n  }\r\n  function convertToHoursMinutes(totalMinutes) {\r\n    // Ensure totalMinutes is a valid number\r\n    const validMinutes = Math.max(0, parseInt(totalMinutes, 10) || 0);\r\n    const hours = Math.floor(validMinutes / 60);\r\n    const minutes = validMinutes % 60;\r\n    return `${hours}h ${minutes}m`;\r\n  }\r\n\r\n  // UI\r\n  return (\r\n    <Grid\r\n      container\r\n      spacing={3}\r\n      sx={{ padding: \"20px\", display: \"flex\", alignItems: \"center\" }}\r\n    >\r\n      {/* Productivity Chart */}\r\n      <Grid item xs={12} md={8}>\r\n        <Card sx={{ height: \"100%\", padding: \"20px\" }}>\r\n          <ProductivityChart todayActivities={todayActivity} />\r\n          <TaskProgressBar />\r\n        </Card>\r\n      </Grid>\r\n\r\n      {/* Activity Card */}\r\n      <Grid item xs={12} md={4}>\r\n        <Card\r\n          sx={{\r\n            height: \"100%\",\r\n            display: \"flex\",\r\n            flexDirection: \"column\",\r\n            alignItems: \"center\",\r\n          }}\r\n        >\r\n          <Timer color=\"primary\" sx={{ fontSize: 100 }} />\r\n\r\n          <Box>\r\n            <Typography\r\n              variant=\"h5\"\r\n              align=\"center\"\r\n              gutterBottom\r\n              sx={{ padding: \"10px\", margin: \"10px\" }}\r\n            >\r\n              Your Activity Today\r\n            </Typography>\r\n\r\n          <Box>\r\n            <Typography\r\n              variant=\"h6\"\r\n              align=\"center\"\r\n              gutterBottom\r\n              sx={{ padding: \"10px\", margin: \"10px\" }}\r\n            >\r\n             Covered time\r\n            </Typography>\r\n\r\n            <Typography variant=\"h6\" align=\"center\">\r\n              {convertToHoursMinutes(totalCoveredTime)}\r\n            </Typography>\r\n            </Box>\r\n\r\n            <InfoLog>\r\n              {[\"Check In\", \"Check Out\"].map((label, index) => (\r\n                <Box key={label} sx={{ textAlign: \"center\" }}>\r\n                  <Typography>{label}</Typography>\r\n                  <Typography variant=\"subtitle2\">\r\n                    {attendance[index ? \"checkOut\" : \"checkIn\"] ? moment(attendance[index ? \"checkOut\" : \"checkIn\"]).format(\"HH:mm\"): \"-\"}\r\n                  </Typography>\r\n                </Box>\r\n              ))}\r\n            </InfoLog>\r\n          </Box>\r\n\r\n          {!attendance.checkOut && (\r\n            <Box sx={{ mt: 3 }}>\r\n              {/* Check In button */}\r\n              {!attendance.checkIn ? (\r\n                <Button\r\n                  fullWidth\r\n                  variant=\"contained\"\r\n                  onClick={handleCheckIn}\r\n                  disabled={checkingIn}\r\n                  sx={{\r\n                    backgroundColor: checkingIn ? \"#a5d6a7\" : \"green\",\r\n                    color: \"white\",\r\n                    fontWeight: \"bold\",\r\n                    borderRadius: \"8px\",\r\n                    width: \"150px\",\r\n                    height: \"50px\",\r\n                    boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                    display: \"flex\",\r\n                    alignItems: \"center\",\r\n                    \"&:hover\": {\r\n                      backgroundColor: checkingIn ? \"#a5d6a7\" : \"darkgreen\",\r\n                    },\r\n                  }}\r\n                >\r\n                  {checkingIn ? \"Processing...\" : \"Check In\"}\r\n                </Button>\r\n              ) : (\r\n                <div\r\n                  style={{\r\n                    display: \"flex\",\r\n                    justifyContent: \"space-between\",\r\n                    alignItems: \"center\",\r\n                    width: \"100%\",\r\n                    marginTop: \"10px\",\r\n                    marginBottom: \"10px\",\r\n                    gap: \"10px\",\r\n                  }}\r\n                >\r\n                  {/* Break controls */}\r\n                  {todayActivity.length > 0 && todayActivity[0]?.breakStatus ? (\r\n                    <FormControl>\r\n                      <Select\r\n                        labelId=\"break-label\"\r\n                        value={lunchType}\r\n                        label=\"Break Type\"\r\n                        onChange={handleBreakOut}\r\n                        sx={{\r\n                          backgroundColor: \"#2e7d32\",\r\n                          color: \"white\",\r\n                          fontWeight: \"bold\",\r\n                          borderRadius: \"8px\",\r\n                          width: \"150px\",\r\n                          height: \"50px\",\r\n                          boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                        }}\r\n                        renderValue={() => \"----\"}\r\n                      >\r\n                        <MenuItem value=\"breakOut\">Break Out</MenuItem>\r\n                      </Select>\r\n                    </FormControl>\r\n                  ) : (\r\n                    <FormControl>\r\n                      <Select\r\n                        labelId=\"break-label\"\r\n                        value={lunchType || \"\"}\r\n                        label=\"Break Type\"\r\n                        onChange={handleBreakOut}\r\n                        sx={{\r\n                          backgroundColor: \"#2e7d32\",\r\n                          color: \"white\",\r\n                          fontWeight: \"bold\",\r\n                          borderRadius: \"8px\",\r\n                          width: \"150px\",\r\n                          height: \"50px\",\r\n                          boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                          display: \"flex\",\r\n                          alignItems: \"center\",\r\n                        }}\r\n                        renderValue={() => \"Break In\"}\r\n                      >\r\n                        <MenuItem value=\"\" disabled>\r\n                          Break In\r\n                        </MenuItem>\r\n                        <MenuItem value=\"lunchBreak\">Lunch Break</MenuItem>\r\n                        <MenuItem value=\"teaBreak\">Tea Break</MenuItem>\r\n                        <MenuItem value=\"other\">Other</MenuItem>\r\n                      </Select>\r\n                    </FormControl>\r\n                  )}\r\n\r\n                  {/* Check Out button */}\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"error\"\r\n                    onClick={handleCheckOut}\r\n                    disabled={checkingOut}\r\n                    sx={{\r\n                      backgroundColor: checkingOut ? \"#ef9a9a\" : \"darked\",\r\n                      color: \"white\",\r\n                      fontWeight: \"bold\",\r\n                      borderRadius: \"8px\",\r\n                      width: \"150px\",\r\n                      height: \"50px\",\r\n                      boxShadow: \"0px 4px 6px rgba(0, 0, 0, 0.2)\",\r\n                      display: \"flex\",\r\n                      alignItems: \"center\",\r\n                    }}\r\n                  >\r\n                    {checkingOut ? \"Processing...\" : \"Check Out\"}\r\n                  </Button>\r\n                </div>\r\n              )}\r\n            </Box>\r\n          )}\r\n\r\n          {/* Popups */}\r\n          {showGoalPopup &&\r\n            todayActivity.length === 0 &&\r\n            !sessionStorage.getItem(\"hasShownGoalPopup\") && (\r\n              <TodayGoal\r\n                title=\"Today's Goal\"\r\n                task=\"goal\"\r\n                onSubmit={handleSubmitGoal} // Fixed typo\r\n                onClose={() => {\r\n                  setShowGoalPopup(false);\r\n                  sessionStorage.setItem(\"hasShownGoalPopup\", \"true\");\r\n                }}\r\n                required={true} // Make goal mandatory\r\n              />\r\n            )}\r\n\r\n          {showBreakPop && (\r\n            <OtherBreak\r\n              openVal={true}\r\n              settingFun={breakPopUpController}\r\n              _id={todayActivity[0]._id}\r\n              profile={profile}\r\n            />\r\n          )}\r\n\r\n          {lateCheckIn && (\r\n            <EarlyLate\r\n              openVal={true}\r\n              task=\"late\"\r\n              dialogTitle=\"Late Check In\"\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\r\n                // sessionStorage.setItem('hasShownLateCheckInPopup', 'true');\r\n                setLateCheckIn(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {todayStatus && (\r\n            <EarlyLate\r\n              openVal={true}\r\n              task=\"status\"\r\n              dialogTitle=\"Work Status\"\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                // sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\r\n                // sessionStorage.setItem('hasShownTodayStatusPopup', 'true');\r\n                setTodayStatus(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {earlyCheckOut && (\r\n            <EarlyLate\r\n              openVal={true}\r\n              task=\"early\"\r\n              dialogTitle=\"Early Check Out\"\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                // sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\r\n                // sessionStorage.setItem('hasShownEarlyCheckOutPopup', 'true');\r\n                setEarlyCheckOut(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {overLimitBreak && (\r\n            <OverLimitBreak\r\n              openVal={true}\r\n              id={todayActivity[0]._id}\r\n              onClose={() => {\r\n                // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\r\n                // sessionStorage.setItem('hasShownOverLimitBreakPopup', 'true');\r\n                setOverLimitBreak(false);\r\n              }}\r\n            />\r\n          )}\r\n\r\n          {/* Work Hours Status Dialog */}\r\n          <WorkHoursStatus\r\n            open={showWorkHoursStatus}\r\n            statusData={workHoursStatus}\r\n            onClose={() => setShowWorkHoursStatus(false)}\r\n          />\r\n        </Card>\r\n      </Grid>\r\n\r\n      {/* Bottom Section */}\r\n      <Grid item xs={12}>\r\n        <Grid\r\n          container\r\n          spacing={3}\r\n          sx={{ display: \"flex\", alignItems: \"center\" }}\r\n        >\r\n          {/* Attendance Bar Chart */}\r\n          <Grid item xs={12} md={6}>\r\n            <AttendanceBarChart activities={activities} />\r\n          </Grid>\r\n\r\n          {/* Leave Information */}\r\n          {Can(actions.readSelf, features.leave) && (\r\n            <Grid item xs={12} md={6} container spacing={2}>\r\n              <Grid item xs={12}>\r\n                <Widget\r\n                  title=\"Leave Taken\"\r\n                  content={countLeave ?? 0}\r\n                  icon={\r\n                    <AssignmentIndOutlined\r\n                      sx={{ color: theme.palette.primary.main, fontSize: 62 }}\r\n                    />\r\n                  }\r\n                />\r\n              </Grid>\r\n              {Can(actions.read, features.attendance) && (\r\n                <Grid item xs={12}>\r\n                  <Widget\r\n                    title=\"Quote Leave\"\r\n                    content={setting?.leaveLimit ?? 0}\r\n                    icon={\r\n                      <AssignmentIndOutlined\r\n                        sx={{ color: theme.palette.primary.main, fontSize: 62 }}\r\n                      />\r\n                    }\r\n                  />\r\n                </Grid>\r\n              )}\r\n            </Grid>\r\n          )}\r\n        </Grid>\r\n      </Grid>\r\n    </Grid>\r\n  );\r\n}\r\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,aAAa,QAAQ,OAAO;AACjE,OAAOC,GAAG,MAAM,oBAAoB;AACpC,SAASC,OAAO,EAAEC,QAAQ,QAAQ,+BAA+B;AACjE,SACEC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,gBAAgB,QACX,eAAe;AACtB,SAASC,KAAK,EAAEC,qBAAqB,QAAQ,qBAAqB;AAClE,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SACEC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,cAAc,QACT,yBAAyB;AAChC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,kBAAkB,EAAEC,YAAY,QAAQ,oBAAoB;AACrE,SAASC,gBAAgB,QAAQ,4BAA4B;AAC7D,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,MAAM,EAAEC,QAAQ,QAAQ,sBAAsB;AACvD,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,UAAU,MAAM,gBAAgB;AACvC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,mBAAmB,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,2BAA2B;;AAEvH;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,eAAe,gBAAG7C,aAAa,CAAC,CAAC;AAE9C,MAAM8C,OAAO,GAAGnB,MAAM,CAACvB,GAAG,CAAC,CAAC,OAAO;EACjC2C,OAAO,EAAE,MAAM;EACfC,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC;AAACC,EAAA,GAHEH,OAAO;AAKb,eAAe,SAASI,QAAQA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,mBAAA;EACjC,MAAMC,KAAK,GAAG1B,QAAQ,CAAC,CAAC;EACxB,MAAM2B,QAAQ,GAAGnC,WAAW,CAAC,CAAC;EAC9B,MAAMoC,OAAO,GAAGnC,WAAW,CAACE,YAAY,CAACiC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAMC,WAAW,GAAGpC,WAAW,CAACC,kBAAkB,CAACoC,cAAc,CAAC,CAAC,CAAC;EACpE,MAAMC,UAAU,GAAGtC,WAAW,CAACG,gBAAgB,CAACoC,kBAAkB,CAAC,CAAC,CAAC;EACrE,MAAMC,UAAU,GAAGxC,WAAW,CAACI,aAAa,CAACoC,UAAU,CAAC,CAAC,CAAC;EAC1D,MAAMC,OAAO,GAAGzC,WAAW,CAACK,eAAe,CAACqC,UAAU,CAAC,CAAC,CAAC;EACzD,MAAMC,OAAO,GAAG3C,WAAW,CAACgB,eAAe,CAAC4B,WAAW,CAAC,CAAC,CAAC;;EAE1D;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACqE,eAAe,EAAEC,QAAQ,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CACxCyE,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC,IAAI,UACvC,CAAC;EACD,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG5E,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6E,WAAW,EAAEC,cAAc,CAAC,GAAG9E,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+E,aAAa,EAAEC,gBAAgB,CAAC,GAAGhF,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAACiF,WAAW,EAAEC,cAAc,CAAC,GAAGlF,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACmF,cAAc,EAAEC,iBAAiB,CAAC,GAAGpF,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACuF,aAAa,EAAEC,WAAW,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EACjD,MAAM,CAACyF,UAAU,EAAEC,aAAa,CAAC,GAAG1F,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAAC2F,aAAa,EAAEC,gBAAgB,CAAC,GAAG5F,QAAQ,CAAC,KAAK,CAAC;EACzD,MAAM,CAAC6F,eAAe,EAAEC,kBAAkB,CAAC,GAAG9F,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC+F,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhG,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACiG,UAAU,EAAEC,aAAa,CAAC,GAAGlG,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACmG,WAAW,EAAEC,cAAc,CAAC,GAAGpG,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACqG,gBAAgB,EAACC,mBAAmB,CAAC,GAAGtG,QAAQ,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACuG,aAAa,EAAEC,gBAAgB,CAAC,GAAGxG,QAAQ,CAAC,IAAI,CAAC;;EAExD;EACA,MAAMyG,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,gBAAgB,GAAGlE,mBAAmB,CAAC,CAAC;IAC9C,OAAOkE,gBAAgB,CAACC,WAAW;EACrC,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMD,WAAW,GAAGF,qBAAqB,CAAC,CAAC;IAC3C,IAAIE,WAAW,IAAI1C,OAAO,IAAIA,OAAO,CAAC4C,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAC,SAAA;MAChD,MAAMC,kBAAkB,GAAGvE,mBAAmB,CAAC,CAAC,CAACwE,WAAW,IAAI,CAAC;MAEjEC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CC,MAAM,EAAER,WAAW,CAACQ,MAAM,IAAIR,WAAW,CAACS,GAAG;QAC7CJ,WAAW,EAAED;MACf,CAAC,CAAC;MAEFvD,QAAQ,CAACpC,cAAc,CAACiG,SAAS,CAAC;QAChCF,MAAM,EAAER,WAAW,CAACQ,MAAM,IAAIR,WAAW,CAACS,GAAG;QAC7CE,SAAS,EAAEX,WAAW,CAACW,SAAS,MAAAR,SAAA,GAAI7C,OAAO,CAAC,CAAC,CAAC,cAAA6C,SAAA,uBAAVA,SAAA,CAAYM,GAAG;QACnDJ,WAAW,EAAED,kBAAkB;QAC/BQ,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,IAAI,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QAC5CC,SAAS,EAAEjB,WAAW,CAACiB,SAAS,IAAI,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MAC7D,CAAC,CAAC,CAAC;;MAEH;MACA/E,eAAe,CAAC,CAAC;IACnB,CAAC,MAAM;MACLuE,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACrE;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMlB,WAAW,GAAGF,qBAAqB,CAAC,CAAC;IAC3C,IAAIE,WAAW,IAAI1C,OAAO,IAAIA,OAAO,CAAC4C,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAiB,UAAA;MAChDb,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE;QAC3CC,MAAM,EAAER,WAAW,CAACQ,MAAM,IAAIR,WAAW,CAACS;MAC5C,CAAC,CAAC;MAEF5D,QAAQ,CAACpC,cAAc,CAAC2G,SAAS,CAAC;QAChCZ,MAAM,EAAER,WAAW,CAACQ,MAAM,IAAIR,WAAW,CAACS,GAAG;QAC7CE,SAAS,EAAEX,WAAW,CAACW,SAAS,MAAAQ,UAAA,GAAI7D,OAAO,CAAC,CAAC,CAAC,cAAA6D,UAAA,uBAAVA,UAAA,CAAYV,GAAG;QACnDM,IAAI,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLV,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACrE;EACF,CAAC;;EAED;EACAnH,SAAS,CAAC,MAAM;IAAA,IAAAiI,eAAA;IACd,IACEzC,aAAa,CAACsB,MAAM,GAAG,CAAC,KAAAmB,eAAA,GACxBzC,aAAa,CAAC,CAAC,CAAC,cAAAyC,eAAA,eAAhBA,eAAA,CAAkBZ,GAAG,IACrB3D,OAAO,IACPA,OAAO,CAAC2D,GAAG,IACX3B,UAAU,IACVA,UAAU,CAAC2B,GAAG,EACd;MACA,QAAQ7C,SAAS;QACf,KAAK,YAAY;UACfD,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1Bb,QAAQ,CACNtC,iBAAiB,CAAC+G,gBAAgB,CAAC;YACjCC,EAAE,EAAEzC,UAAU,CAAC2B,GAAG;YAClBe,OAAO,EAAE,IAAIX,IAAI,CAAC;UACpB,CAAC,CACH,CAAC;UACDhE,QAAQ,CACNvC,eAAe,CAACmH,aAAa,CAAC;YAC5BhB,GAAG,EAAE7B,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAG;YACzBiB,IAAI,EAAE9D,SAAS;YACf+D,UAAU,EAAE,IAAId,IAAI,CAAC,CAAC,CAACe,eAAe,CAAC,CAAC,CAAC;YACzCC,WAAW,EAAE,aAAa;YAC1BC,IAAI,EAAEhF,OAAO,CAAC2D;UAChB,CAAC,CACH,CAAC;UACD;UACAR,gBAAgB,CAAC,CAAC;UAClBtB,iBAAiB,CAAC,KAAK,CAAC;UACxB;QAEF,KAAK,UAAU;UACbhB,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1Bb,QAAQ,CACNtC,iBAAiB,CAAC+G,gBAAgB,CAAC;YACjCC,EAAE,EAAEzC,UAAU,CAAC2B,GAAG;YAClBe,OAAO,EAAE,IAAIX,IAAI,CAAC;UACpB,CAAC,CACH,CAAC;UACDhE,QAAQ,CACNvC,eAAe,CAACmH,aAAa,CAAC;YAC5BhB,GAAG,EAAE7B,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAG;YACzBkB,UAAU,EAAE,IAAId,IAAI,CAAC,CAAC,CAACe,eAAe,CAAC,CAAC,CAAC;YACzCF,IAAI,EAAE9D,SAAS;YACfiE,WAAW,EAAE,WAAW;YACxBC,IAAI,EAAEhF,OAAO,CAAC2D;UAChB,CAAC,CACH,CAAC;UACD;UACAR,gBAAgB,CAAC,CAAC;UAClBtB,iBAAiB,CAAC,KAAK,CAAC;UACxB;QAEF,KAAK,OAAO;UACVhB,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1Bb,QAAQ,CACNtC,iBAAiB,CAAC+G,gBAAgB,CAAC;YACjCC,EAAE,EAAEzC,UAAU,CAAC2B,GAAG;YAClBe,OAAO,EAAE,IAAIX,IAAI,CAAC,CAAC,CAACe,eAAe,CAAC,CAAC,CAAC;YACtCE,IAAI,EAAEhF,OAAO,CAAC2D;UAChB,CAAC,CACH,CAAC;UACD;UACAR,gBAAgB,CAAC,CAAC;UAClBhC,eAAe,CAAC,IAAI,CAAC;UACrBU,iBAAiB,CAAC,KAAK,CAAC;UACxB;QAEF,KAAK,UAAU;UACbhB,QAAQ,CAAC,CAACD,eAAe,CAAC;UAC1Bb,QAAQ,CACNtC,iBAAiB,CAACwH,gBAAgB,CAAC;YACjCR,EAAE,EAAEzC,UAAU,CAAC2B,GAAG;YAClBuB,QAAQ,EAAE,IAAInB,IAAI,CAAC;UACrB,CAAC,CACH,CAAC;UACDhE,QAAQ,CACNvC,eAAe,CAAC2H,WAAW,CAAC;YAC1BxB,GAAG,EAAE7B,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAG;YACzByB,QAAQ,EAAE,IAAIrB,IAAI,CAAC,CAAC,CAACe,eAAe,CAAC,CAAC,CAAC;YACvCF,IAAI,EAAE9D,SAAS;YACfkE,IAAI,EAAEhF,OAAO,CAAC2D;UAChB,CAAC,CACH,CAAC;UACD;UACAS,iBAAiB,CAAC,CAAC;UACnBvC,iBAAiB,CAAC,IAAI,CAAC;UACvB;QAEF;UACE;MACJ;IACF;EACF,CAAC,EAAE,CAACf,SAAS,CAAC,CAAC;;EAEf;EACAxE,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAAC0D,OAAO,IAAI,CAACA,OAAO,CAAC2D,GAAG,EAAE;MAC5B5B,WAAW,CAAC,EAAE,CAAC;MACf;IACF;IAEA,IAAI5B,UAAU,IAAIA,UAAU,CAACiD,MAAM,GAAG,CAAC,EAAE;MACvC;MACA,MAAMiC,KAAK,GAAG,IAAItB,IAAI,CAAC,CAAC;MACxB,MAAMuB,SAAS,GAAGD,KAAK,CAACE,cAAc,CAAC,CAAC;MACxC,MAAMC,UAAU,GAAGH,KAAK,CAACI,WAAW,CAAC,CAAC;MACtC,MAAMC,SAAS,GAAGL,KAAK,CAACM,UAAU,CAAC,CAAC;MAEpC,MAAMC,MAAM,GAAGzF,UAAU,CAAC0F,IAAI,CAAEC,QAAQ,IAAK;QAC3C,IAAI,CAACA,QAAQ,CAACC,WAAW,EAAE;UAAE,OAAO,KAAK;QAAE;;QAE3C;QACA,MAAMC,aAAa,GAAGF,QAAQ,CAACd,IAAI,KAAKhF,OAAO,CAAC2D,GAAG,IAAImC,QAAQ,CAACd,IAAI,CAACrB,GAAG,KAAK3D,OAAO,CAAC2D,GAAG;QACxF,IAAI,CAACqC,aAAa,EAAE;UAAE,OAAO,KAAK;QAAE;QAEpC,MAAMC,YAAY,GAAG,IAAIlC,IAAI,CAAC+B,QAAQ,CAACC,WAAW,CAAC;QACnDvC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAGwC,YAAY,CAACV,cAAc,CAAC,CAAC,EAAGD,SAAS,EAACW,YAAY,CAACR,WAAW,CAAC,CAAC,EAAGD,UAAU,EAAES,YAAY,CAACN,UAAU,CAAC,CAAC,EAAGD,SAAS,CAAC;QACzJ,OACEO,YAAY,CAACV,cAAc,CAAC,CAAC,KAAKD,SAAS,IAC3CW,YAAY,CAACR,WAAW,CAAC,CAAC,KAAKD,UAAU,IACzCS,YAAY,CAACN,UAAU,CAAC,CAAC,KAAKD,SAAS;MAE3C,CAAC,CAAC;MAEFlC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmC,MAAM,CAAC;MAC9C,IAAIA,MAAM,EAAE;QACV7D,WAAW,CAAC,CAAC6D,MAAM,CAAC,CAAC;QACrBzD,gBAAgB,CAAC,KAAK,CAAC;QACvB+D,cAAc,CAACC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;MACrD,CAAC,MAAM;QACL;QACApE,WAAW,CAAC,EAAE,CAAC;MACjB;IACF,CAAC,MAAM;MACLA,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,CAAC5B,UAAU,EAAEH,OAAO,CAAC,CAAC;;EAEzB;EACF1D,SAAS,CAAC,MAAM;IACd,IAAIwF,aAAa,CAACsB,MAAM,GAAG,CAAC,EAAE;MAAA,IAAAgD,gBAAA;MAC5B;MACA,IACEtE,aAAa,CAAC,CAAC,CAAC,CAACuE,iBAAiB,IAClC,CAACvE,aAAa,CAACwE,IAAI,CAAEC,GAAG,IACtBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,wBAAwB,CACpE,CAAC,EACD;QACAlF,cAAc,CAAC,IAAI,CAAC;QACpB;MACF;;MAEA;MACA,IACES,aAAa,CAAC,CAAC,CAAC,CAAC8E,mBAAmB,IACpC,CAAC9E,aAAa,CAACwE,IAAI,CAAEC,GAAG,IACtBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,0BAA0B,CACtE,CAAC,EACD;QACAhF,gBAAgB,CAAC,IAAI,CAAC;MACxB;;MAEA;MACA,IACEO,aAAa,CAACwE,IAAI,CAAEC,GAAG,IACrBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,cAAc,CAC1D,CAAC,IACD,CAACzE,aAAa,CAACwE,IAAI,CAAEC,GAAG,IACtBC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,GAAG,EAAE,YAAY,CACxD,CAAC,EACD;QACA9E,cAAc,CAAC,IAAI,CAAC;MACtB;;MAEE;MACA,IACEK,aAAa,CAAC,CAAC,CAAC,CAAC+E,aAAa,IAC9B/E,aAAa,CAAC,CAAC,CAAC,CAACgF,oBAAoB,KAAK,IAAI,EAC9C;QACAnF,iBAAiB,CAAC,IAAI,CAAC;QACvB;MACF;;MAEA;MACA,MAAMoF,WAAW,GAAG,EAAAX,gBAAA,GAAAtE,aAAa,CAAC,CAAC,CAAC,cAAAsE,gBAAA,uBAAhBA,gBAAA,CAAkBY,gBAAgB,KAAI,CAAC;MAC3DxD,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEsD,WAAW,CAAC;MACjElE,mBAAmB,CAACkE,WAAW,CAAC;;MAEhC;MACA,IAAI/E,UAAU,CAACiF,OAAO,IAAI,CAACjF,UAAU,CAACkF,QAAQ,EAAE;QAC9CC,kBAAkB,CAAC,CAAC;MACtB;IACF;EACF,CAAC,EAAE,CAACrF,aAAa,CAAC,CAAC;;EAEnB;EACAxF,SAAS,CAAC,MAAM;IACdkH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEzD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,GAAG,EAAE3D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEoH,IAAI,CAAC;IAE7E,IAAI3K,GAAG,CAACC,OAAO,CAAC2K,IAAI,EAAE1K,QAAQ,CAACqF,UAAU,CAAC,IAAIhC,OAAO,IAAIA,OAAO,CAAC2D,GAAG,EAAE;MACpEH,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEzD,OAAO,CAAC2D,GAAG,CAAC;;MAEtD;MACA5D,QAAQ,CAACtC,iBAAiB,CAAC6J,gBAAgB,CAAC,CAAC,CAAC;MAC9CvH,QAAQ,CAACvC,eAAe,CAAC+J,aAAa,CAAC,CAAC,CAAC;MACzCtF,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBF,WAAW,CAAC,EAAE,CAAC;;MAEf;MACA,MAAMsD,KAAK,GAAG,IAAItB,IAAI,CAAC,CAAC;MACxB,MAAMyD,aAAa,GAAGnC,KAAK,CAACrB,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEvD;MACA,MAAMuD,aAAa,GAAGzG,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAC3D,MAAMyG,UAAU,GAAG1G,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;MAErDuC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE+D,aAAa,EAAE,OAAO,EAAEC,aAAa,EAAE,OAAO,EAAEC,UAAU,EAAE,UAAU,EAAE1H,OAAO,CAAC2D,GAAG,CAAC;MAE1H,IAAI8D,aAAa,KAAKD,aAAa,IAAIE,UAAU,KAAK1H,OAAO,CAAC2D,GAAG,EAAE;QACjEH,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC3DyC,cAAc,CAACyB,UAAU,CAAC,mBAAmB,CAAC;QAC9CzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;QACrDzB,cAAc,CAACyB,UAAU,CAAC,4BAA4B,CAAC;QACvDzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;QACrDzB,cAAc,CAACyB,UAAU,CAAC,6BAA6B,CAAC;QACxD3G,YAAY,CAACmF,OAAO,CAAC,eAAe,EAAEqB,aAAa,CAAC;QACpDxG,YAAY,CAACmF,OAAO,CAAC,YAAY,EAAEnG,OAAO,CAAC2D,GAAG,CAAC;MACjD;;MAEA;MACAiE,UAAU,CAAC,MAAM;QACfpE,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEzD,OAAO,CAAC2D,GAAG,CAAC;QAC5D5D,QAAQ,CACNtC,iBAAiB,CAACyC,cAAc,CAAC;UAC/B8E,IAAI,EAAEhF,OAAO,CAAC2D,GAAG;UACjBM,IAAI,EAAEuD,aAAa;UACnBK,EAAE,EAAE9D,IAAI,CAAC+D,GAAG,CAAC,CAAC,CAAE;QAClB,CAAC,CACH,CAAC;QACD/H,QAAQ,CACNvC,eAAe,CAACuK,eAAe,CAAC;UAC9BtD,EAAE,EAAEzE,OAAO,CAAC2D;QACd,CAAC,CACH,CAAC;MACH,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACLH,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;MAC1D;MACA1D,QAAQ,CAACtC,iBAAiB,CAAC6J,gBAAgB,CAAC,CAAC,CAAC;MAC9CvH,QAAQ,CAACvC,eAAe,CAAC+J,aAAa,CAAC,CAAC,CAAC;MACzCtF,aAAa,CAAC,CAAC,CAAC,CAAC;MACjBF,WAAW,CAAC,EAAE,CAAC;IACjB;EACF,CAAC,EAAE,CAAC/B,OAAO,EAAED,QAAQ,CAAC,CAAC;;EAEvB;EACAzD,SAAS,CAAC,MAAM;IACdkH,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEzD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE2D,GAAG,CAAC;IACpEH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAExD,WAAW,CAACmD,MAAM,EAAEnD,WAAW,CAAC;;IAExE;IACA,IAAI,CAACD,OAAO,IAAI,CAACA,OAAO,CAAC2D,GAAG,EAAE;MAC5BH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;MAChDxB,aAAa,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;;IAEA;IACA,MAAMoD,KAAK,GAAG,IAAItB,IAAI,CAAC,CAAC;IACxB,MAAM2B,SAAS,GAAGL,KAAK,CAACrB,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IAEnD,IAAIjE,WAAW,CAACmD,MAAM,GAAG,CAAC,EAAE;MAC1B;MACA,MAAM4E,gBAAgB,GAAG/H,WAAW,CAACgI,MAAM,CAAEC,GAAG,IAAK;QACnD,MAAMC,OAAO,GAAG,IAAIpE,IAAI,CAACmE,GAAG,CAACjB,OAAO,CAAC,CAACjD,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACjE,MAAMkE,OAAO,GAAGD,OAAO,KAAKzC,SAAS;QACrC,MAAMM,aAAa,GAAGkC,GAAG,CAAClD,IAAI,KAAKhF,OAAO,CAAC2D,GAAG,IAAIuE,GAAG,CAAClD,IAAI,CAACrB,GAAG,KAAK3D,OAAO,CAAC2D,GAAG;QAC9EH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE;UACrC0E,OAAO;UACPC,OAAO;UACPC,OAAO,EAAEH,GAAG,CAAClD,IAAI;UACjBsD,WAAW,EAAEtI,OAAO,CAAC2D,GAAG;UACxBqC;QACF,CAAC,CAAC;QACF,OAAOoC,OAAO,IAAIpC,aAAa;MACjC,CAAC,CAAC;MAEFxC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEuE,gBAAgB,CAAC5E,MAAM,EAAE4E,gBAAgB,CAAC;MAEjG,IAAIA,gBAAgB,CAAC5E,MAAM,GAAG,CAAC,EAAE;QAC/BI,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEuE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACzD/F,aAAa,CAAC+F,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACpC,CAAC,MAAM;QACL;QACAxE,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;QACzDxB,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB;IACF,CAAC,MAAM;MACLuB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxCxB,aAAa,CAAC,CAAC,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,CAAChC,WAAW,EAAED,OAAO,CAAC,CAAC;;EAE1B;EACA1D,SAAS,CAAC,MAAM;IACd,IAAIG,GAAG,CAACC,OAAO,CAAC2K,IAAI,EAAE1K,QAAQ,CAACqF,UAAU,CAAC,IAAIhC,OAAO,IAAIA,OAAO,CAAC2D,GAAG,EAAE;MACpE;MACA,MAAM0B,KAAK,GAAG,IAAItB,IAAI,CAAC,CAAC;MACxB,MAAMyD,aAAa,GAAGnC,KAAK,CAACrB,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;;MAEvD;MACA,MAAMqE,SAAS,GAAGxE,IAAI,CAAC+D,GAAG,CAAC,CAAC;MAE5B/H,QAAQ,CACNtC,iBAAiB,CAACyC,cAAc,CAAC;QAC/B8E,IAAI,EAAEhF,OAAO,CAAC2D,GAAG;QACjBM,IAAI,EAAEuD,aAAa;QACnBK,EAAE,EAAEU,SAAS,CAAE;MACjB,CAAC,CACH,CAAC;IACH;IACAxI,QAAQ,CACNrC,cAAc,CAAC8K,aAAa,CAAC/K,iBAAiB,CAACgL,gBAAgB,CAAC7D,IAAI,CACtE,CAAC;EACH,CAAC,EAAE,CAAClE,UAAU,EAAEE,eAAe,EAAEZ,OAAO,EAAED,QAAQ,CAAC,CAAC;;EAEpD;EACAzD,SAAS,CAAC,MAAM;IACd0E,YAAY,CAACmF,OAAO,CAAC,WAAW,EAAErF,SAAS,CAAC;EAC9C,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;;EAEf;EACAxE,SAAS,CAAC,MAAM;IACd,IAAI0F,UAAU,CAACiF,OAAO,IAAI,CAACjF,UAAU,CAACkF,QAAQ,EAAE;MAC9CC,kBAAkB,CAAC,CAAC;IACtB,CAAC,MAAM;MACLuB,iBAAiB,CAAC,CAAC;IACrB;IAEA,OAAO,MAAMA,iBAAiB,CAAC,CAAC;EAClC,CAAC,EAAE,CAAC1G,UAAU,CAAC,CAAC;EAEhB,MAAMmF,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIrE,aAAa,EAAE;MACjB6F,aAAa,CAAC7F,aAAa,CAAC;IAC9B;IAEA,MAAM8F,KAAK,GAAGC,WAAW,CAAC,MAAM;MAC9BC,4BAA4B,CAAC,CAAC;IAChC,CAAC,EAAE,IAAI,CAAC;IAER/F,gBAAgB,CAAC6F,KAAK,CAAC;EACzB,CAAC;EAED,MAAMF,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI5F,aAAa,EAAE;MACjB6F,aAAa,CAAC7F,aAAa,CAAC;MAC5BC,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC;EAED,MAAM+F,4BAA4B,GAAGA,CAAA,KAAM;IAAA,IAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACzC,IAAI,CAAClH,UAAU,CAACiF,OAAO,IAAIjF,UAAU,CAACkF,QAAQ,EAAE;MAC9C;IACF;IAEA,MAAMnB,WAAW,GAAG,IAAIhC,IAAI,CAAC/B,UAAU,CAACiF,OAAO,CAAC;IAChD,MAAMkC,WAAW,GAAG,IAAIpF,IAAI,CAAC,CAAC;IAC9B,MAAMqF,YAAY,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACH,WAAW,GAAGpD,WAAW,IAAI,KAAK,CAAC;;IAEpE;IACA,IAAIwD,iBAAiB,GAAG,CAAC;IACzB,KAAAR,gBAAA,GAAIjH,aAAa,CAAC,CAAC,CAAC,cAAAiH,gBAAA,eAAhBA,gBAAA,CAAkBlC,aAAa,EAAE;MACnC/E,aAAa,CAAC,CAAC,CAAC,CAAC+E,aAAa,CAAC2C,OAAO,CAACC,SAAS,IAAI;QAClD,IAAIA,SAAS,CAACC,gBAAgB,IAAID,SAAS,CAACE,cAAc,EAAE;UAC1D,MAAM9E,UAAU,GAAG,IAAId,IAAI,CAAC0F,SAAS,CAACC,gBAAgB,CAAC;UACvD,MAAMtE,QAAQ,GAAG,IAAIrB,IAAI,CAAC0F,SAAS,CAACE,cAAc,CAAC;UACnDJ,iBAAiB,IAAIF,IAAI,CAACC,KAAK,CAAC,CAAClE,QAAQ,GAAGP,UAAU,IAAI,KAAK,CAAC;QAClE;MACF,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAAAmE,gBAAA,GAAAlH,aAAa,CAAC,CAAC,CAAC,cAAAkH,gBAAA,eAAhBA,gBAAA,CAAkBY,WAAW,IAAI,EAAAX,gBAAA,GAAAnH,aAAa,CAAC,CAAC,CAAC,cAAAmH,gBAAA,wBAAAC,qBAAA,GAAhBD,gBAAA,CAAkBpC,aAAa,cAAAqC,qBAAA,uBAA/BA,qBAAA,CAAiC9F,MAAM,IAAG,CAAC,EAAE;MAChF,MAAMyG,SAAS,GAAG/H,aAAa,CAAC,CAAC,CAAC,CAAC+E,aAAa,CAAC/E,aAAa,CAAC,CAAC,CAAC,CAAC+E,aAAa,CAACzD,MAAM,GAAG,CAAC,CAAC;MAC3F,IAAIyG,SAAS,CAACH,gBAAgB,IAAI,CAACG,SAAS,CAACF,cAAc,EAAE;QAC3D,MAAM9E,UAAU,GAAG,IAAId,IAAI,CAAC8F,SAAS,CAACH,gBAAgB,CAAC;QACvDH,iBAAiB,IAAIF,IAAI,CAACC,KAAK,CAAC,CAACH,WAAW,GAAGtE,UAAU,IAAI,KAAK,CAAC;MACrE;IACF;IAEA,MAAMiF,cAAc,GAAGT,IAAI,CAACU,GAAG,CAAC,CAAC,EAAEX,YAAY,GAAGG,iBAAiB,CAAC;IACpE1G,mBAAmB,CAACiH,cAAc,CAAC;EACrC,CAAC;;EAED;EACA,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAIxH,UAAU,EAAE;MACd;IACF;IAEAgB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;;IAE/B;IACAxB,aAAa,CAAC,CAAC,CAAC,CAAC;;IAEjB;IACA,MAAMoD,KAAK,GAAG,IAAItB,IAAI,CAAC,CAAC;IACxB,MAAMyD,aAAa,GAAGnC,KAAK,CAACrB,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD,MAAM+F,eAAe,GAAGjJ,YAAY,CAACC,OAAO,CAAC,iBAAiB,CAAC;;IAE/D;IACA,IAAIgJ,eAAe,KAAKzC,aAAa,EAAE;MACrCtB,cAAc,CAACyB,UAAU,CAAC,mBAAmB,CAAC;MAC9CzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;MACrDzB,cAAc,CAACyB,UAAU,CAAC,4BAA4B,CAAC;MACvDzB,cAAc,CAACyB,UAAU,CAAC,0BAA0B,CAAC;MACrDzB,cAAc,CAACyB,UAAU,CAAC,6BAA6B,CAAC;MACxD3G,YAAY,CAACmF,OAAO,CAAC,iBAAiB,EAAEqB,aAAa,CAAC;IACxD;;IAEA;IACArF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,MAAM+H,gBAAgB,GAAIC,QAAQ,IAAK;IACrC3G,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE0G,QAAQ,CAAC;;IAExC;IACA1H,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACAN,gBAAgB,CAAC,KAAK,CAAC;;IAEvB;IACA+D,cAAc,CAACC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;;IAEnD;IACA,IAAI,CAACnG,OAAO,IAAI,CAACA,OAAO,CAAC2D,GAAG,EAAE;MAC5BH,OAAO,CAAC4G,KAAK,CAAC,yBAAyB,CAAC;MACxC3H,aAAa,CAAC,KAAK,CAAC;MACpB;IACF;;IAEA;IACA,IAAI0H,QAAQ,CAACE,UAAU,EAAE;MACvBtK,QAAQ,CACNvC,eAAe,CAAC8M,eAAe,CAAC;QAC9B7F,EAAE,EAAEzE,OAAO,CAAC2D,GAAG;QACf0G,UAAU,EAAEF,QAAQ,CAACE;MACvB,CAAC,CACH,CAAC;IACH;;IAEA;IACA,MAAMtE,WAAW,GAAG,IAAIhC,IAAI,CAAC,CAAC;IAC9BhE,QAAQ,CACNtC,iBAAiB,CAACgL,gBAAgB,CAAC;MACjCzD,IAAI,EAAEhF,OAAO,CAAC2D,GAAG;MACjBsD,OAAO,EAAElB;IACX,CAAC,CACH,CAAC;;IAED;IACA,MAAMV,KAAK,GAAG,IAAItB,IAAI,CAAC,CAAC;IACxB,MAAMyD,aAAa,GAAGnC,KAAK,CAACrB,WAAW,CAAC,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACvD,MAAMqE,SAAS,GAAGxE,IAAI,CAAC+D,GAAG,CAAC,CAAC;;IAE5B;IACA9G,YAAY,CAACmF,OAAO,CAAC,iBAAiB,EAAEqB,aAAa,CAAC;;IAEtD;IACAI,UAAU,CAAC,MAAM;MACf7H,QAAQ,CACNtC,iBAAiB,CAACyC,cAAc,CAAC;QAC/B8E,IAAI,EAAEhF,OAAO,CAAC2D,GAAG;QACjBM,IAAI,EAAEuD,aAAa;QACnBK,EAAE,EAAEU,SAAS,CAAE;MACjB,CAAC,CACH,CAAC;;MAED;MACA5H,aAAa,CAAC,CAACD,UAAU,CAAC;;MAE1B;MACA+B,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EACD;EACA,MAAM8H,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI7H,WAAW,EAAE;MACf;IACF;;IAEA;IACA,IACE,CAAC1C,OAAO,IACR,CAACA,OAAO,CAAC2D,GAAG,IACZ,CAAC3B,UAAU,IACX,CAACA,UAAU,CAAC2B,GAAG,IACf,CAAC7B,aAAa,IACd,CAACA,aAAa,CAACsB,MAAM,EACrB;MACAI,OAAO,CAAC4G,KAAK,CAAC,qCAAqC,CAAC;MACpD;IACF;IAEAzH,cAAc,CAAC,IAAI,CAAC;IACpBhC,aAAa,CAAC,CAACD,UAAU,CAAC;IAE1BX,QAAQ,CACNtC,iBAAiB,CAAC+M,gBAAgB,CAAC;MACjC/F,EAAE,EAAEzC,UAAU,CAAC2B,GAAG;MAClBuD,QAAQ,EAAE,IAAInD,IAAI,CAAC,CAAC;MACpBiB,IAAI,EAAEhF,OAAO,CAAC2D;IAChB,CAAC,CACH,CAAC;IAED5D,QAAQ,CACNvC,eAAe,CAACiN,oBAAoB,CAAC;MACnC9G,GAAG,EAAE7B,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAG;MACzBqB,IAAI,EAAEhF,OAAO,CAAC2D;IAChB,CAAC,CACH,CAAC;IAEDiE,UAAU,CAAC,MAAM;MACfjF,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;;EAED;EACA,MAAM+H,eAAe,GAAG7M,WAAW,CAAE8M,KAAK,IACxCA,KAAK,CAACC,OAAO,CAACC,OAAO,CAAChF,IAAI,CACvBiF,CAAC,IAAKA,CAAC,CAACC,MAAM,KAAKvN,eAAe,CAACiN,oBAAoB,CAAC7F,IAC3D,CACF,CAAC;;EAED;EACAtI,SAAS,CAAC,MAAM;IACd,IAAIoO,eAAe,IAAIA,eAAe,CAACM,IAAI,EAAE;MAC3C3I,kBAAkB,CAACqI,eAAe,CAACM,IAAI,CAAC;MACxCzI,sBAAsB,CAAC,IAAI,CAAC;MAC5BqF,UAAU,CAAC,MAAM;QACf7H,QAAQ,CACNrC,cAAc,CAAC8K,aAAa,CAC1BhL,eAAe,CAACiN,oBAAoB,CAAC7F,IACvC,CACF,CAAC;MACH,CAAC,EAAE,GAAG,CAAC;IACT;EACF,CAAC,EAAE,CAAC8F,eAAe,EAAE3K,QAAQ,CAAC,CAAC;;EAE/B;EACA,MAAMkL,cAAc,GAAIC,KAAK,IAAK;IAChCnK,YAAY,CAACmK,KAAK,CAACC,MAAM,CAACC,KAAK,CAAC;EAClC,CAAC;;EAED;EACA,SAASC,oBAAoBA,CAACC,GAAG,EAAE;IACjCnK,eAAe,CAACmK,GAAG,CAAC;EACtB;;EAEA;EACA,IAAI,CAAC7O,GAAG,CAACC,OAAO,CAAC6O,QAAQ,EAAE5O,QAAQ,CAACqF,UAAU,CAAC,EAAE;IAC/C,oBAAO5C,OAAA;MAAAoM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAU,CAAC;EACpB;EACA,SAASC,qBAAqBA,CAACxC,YAAY,EAAE;IAC3C;IACA,MAAMyC,YAAY,GAAGxC,IAAI,CAACU,GAAG,CAAC,CAAC,EAAE+B,QAAQ,CAAC1C,YAAY,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;IACjE,MAAM2C,KAAK,GAAG1C,IAAI,CAACC,KAAK,CAACuC,YAAY,GAAG,EAAE,CAAC;IAC3C,MAAMG,OAAO,GAAGH,YAAY,GAAG,EAAE;IACjC,OAAO,GAAGE,KAAK,KAAKC,OAAO,GAAG;EAChC;;EAEA;EACA,oBACE5M,OAAA,CAACrC,IAAI;IACHkP,SAAS;IACTC,OAAO,EAAE,CAAE;IACXC,EAAE,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAE7M,OAAO,EAAE,MAAM;MAAE8M,UAAU,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAG/DlN,OAAA,CAACrC,IAAI;MAACwP,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACvBlN,OAAA,CAACtC,IAAI;QAACqP,EAAE,EAAE;UAAEO,MAAM,EAAE,MAAM;UAAEN,OAAO,EAAE;QAAO,CAAE;QAAAE,QAAA,gBAC5ClN,OAAA,CAACV,iBAAiB;UAACiO,eAAe,EAAE7K;QAAc;UAAA0J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDvM,OAAA,CAACT,eAAe;UAAA6M,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvM,OAAA,CAACrC,IAAI;MAACwP,IAAI;MAACC,EAAE,EAAE,EAAG;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,eACvBlN,OAAA,CAACtC,IAAI;QACHqP,EAAE,EAAE;UACFO,MAAM,EAAE,MAAM;UACdnN,OAAO,EAAE,MAAM;UACfqN,aAAa,EAAE,QAAQ;UACvBP,UAAU,EAAE;QACd,CAAE;QAAAC,QAAA,gBAEFlN,OAAA,CAAC/B,KAAK;UAACwP,KAAK,EAAC,SAAS;UAACV,EAAE,EAAE;YAAEW,QAAQ,EAAE;UAAI;QAAE;UAAAtB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEhDvM,OAAA,CAACxC,GAAG;UAAA0P,QAAA,gBACFlN,OAAA,CAACpC,UAAU;YACT+P,OAAO,EAAC,IAAI;YACZC,KAAK,EAAC,QAAQ;YACdC,YAAY;YACZd,EAAE,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEc,MAAM,EAAE;YAAO,CAAE;YAAAZ,QAAA,EACzC;UAED;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEfvM,OAAA,CAACxC,GAAG;YAAA0P,QAAA,gBACFlN,OAAA,CAACpC,UAAU;cACT+P,OAAO,EAAC,IAAI;cACZC,KAAK,EAAC,QAAQ;cACdC,YAAY;cACZd,EAAE,EAAE;gBAAEC,OAAO,EAAE,MAAM;gBAAEc,MAAM,EAAE;cAAO,CAAE;cAAAZ,QAAA,EACzC;YAED;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eAEbvM,OAAA,CAACpC,UAAU;cAAC+P,OAAO,EAAC,IAAI;cAACC,KAAK,EAAC,QAAQ;cAAAV,QAAA,EACpCV,qBAAqB,CAAChJ,gBAAgB;YAAC;cAAA4I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENvM,OAAA,CAACE,OAAO;YAAAgN,QAAA,EACL,CAAC,UAAU,EAAE,WAAW,CAAC,CAACa,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC1CjO,OAAA,CAACxC,GAAG;cAAauP,EAAE,EAAE;gBAAEmB,SAAS,EAAE;cAAS,CAAE;cAAAhB,QAAA,gBAC3ClN,OAAA,CAACpC,UAAU;gBAAAsP,QAAA,EAAEc;cAAK;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CAAC,eAChCvM,OAAA,CAACpC,UAAU;gBAAC+P,OAAO,EAAC,WAAW;gBAAAT,QAAA,EAC5BtK,UAAU,CAACqL,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC,GAAG9P,MAAM,CAACyE,UAAU,CAACqL,KAAK,GAAG,UAAU,GAAG,SAAS,CAAC,CAAC,CAACE,MAAM,CAAC,OAAO,CAAC,GAAE;cAAG;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3G,CAAC;YAAA,GAJLyB,KAAK;cAAA5B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKV,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EAEL,CAAC3J,UAAU,CAACkF,QAAQ,iBACnB9H,OAAA,CAACxC,GAAG;UAACuP,EAAE,EAAE;YAAEqB,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,EAEhB,CAACtK,UAAU,CAACiF,OAAO,gBAClB7H,OAAA,CAACvC,MAAM;YACL4Q,SAAS;YACTV,OAAO,EAAC,WAAW;YACnBW,OAAO,EAAE1D,aAAc;YACvB2D,QAAQ,EAAEnL,UAAW;YACrB2J,EAAE,EAAE;cACFyB,eAAe,EAAEpL,UAAU,GAAG,SAAS,GAAG,OAAO;cACjDqK,KAAK,EAAE,OAAO;cACdgB,UAAU,EAAE,MAAM;cAClBC,YAAY,EAAE,KAAK;cACnBC,KAAK,EAAE,OAAO;cACdrB,MAAM,EAAE,MAAM;cACdsB,SAAS,EAAE,gCAAgC;cAC3CzO,OAAO,EAAE,MAAM;cACf8M,UAAU,EAAE,QAAQ;cACpB,SAAS,EAAE;gBACTuB,eAAe,EAAEpL,UAAU,GAAG,SAAS,GAAG;cAC5C;YACF,CAAE;YAAA8J,QAAA,EAED9J,UAAU,GAAG,eAAe,GAAG;UAAU;YAAAgJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,gBAETvM,OAAA;YACE6O,KAAK,EAAE;cACL1O,OAAO,EAAE,MAAM;cACfC,cAAc,EAAE,eAAe;cAC/B6M,UAAU,EAAE,QAAQ;cACpB0B,KAAK,EAAE,MAAM;cACbG,SAAS,EAAE,MAAM;cACjBC,YAAY,EAAE,MAAM;cACpBC,GAAG,EAAE;YACP,CAAE;YAAA9B,QAAA,GAGDxK,aAAa,CAACsB,MAAM,GAAG,CAAC,KAAAxD,gBAAA,GAAIkC,aAAa,CAAC,CAAC,CAAC,cAAAlC,gBAAA,eAAhBA,gBAAA,CAAkBgK,WAAW,gBACxDxK,OAAA,CAACjC,WAAW;cAAAmP,QAAA,eACVlN,OAAA,CAAClC,MAAM;gBACLmR,OAAO,EAAC,aAAa;gBACrBjD,KAAK,EAAEtK,SAAU;gBACjBsM,KAAK,EAAC,YAAY;gBAClBkB,QAAQ,EAAErD,cAAe;gBACzBkB,EAAE,EAAE;kBACFyB,eAAe,EAAE,SAAS;kBAC1Bf,KAAK,EAAE,OAAO;kBACdgB,UAAU,EAAE,MAAM;kBAClBC,YAAY,EAAE,KAAK;kBACnBC,KAAK,EAAE,OAAO;kBACdrB,MAAM,EAAE,MAAM;kBACdsB,SAAS,EAAE,gCAAgC;kBAC3CzO,OAAO,EAAE,MAAM;kBACf8M,UAAU,EAAE;gBACd,CAAE;gBACFkC,WAAW,EAAEA,CAAA,KAAM,MAAO;gBAAAjC,QAAA,eAE1BlN,OAAA,CAACnC,QAAQ;kBAACmO,KAAK,EAAC,UAAU;kBAAAkB,QAAA,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,gBAEdvM,OAAA,CAACjC,WAAW;cAAAmP,QAAA,eACVlN,OAAA,CAAClC,MAAM;gBACLmR,OAAO,EAAC,aAAa;gBACrBjD,KAAK,EAAEtK,SAAS,IAAI,EAAG;gBACvBsM,KAAK,EAAC,YAAY;gBAClBkB,QAAQ,EAAErD,cAAe;gBACzBkB,EAAE,EAAE;kBACFyB,eAAe,EAAE,SAAS;kBAC1Bf,KAAK,EAAE,OAAO;kBACdgB,UAAU,EAAE,MAAM;kBAClBC,YAAY,EAAE,KAAK;kBACnBC,KAAK,EAAE,OAAO;kBACdrB,MAAM,EAAE,MAAM;kBACdsB,SAAS,EAAE,gCAAgC;kBAC3CzO,OAAO,EAAE,MAAM;kBACf8M,UAAU,EAAE;gBACd,CAAE;gBACFkC,WAAW,EAAEA,CAAA,KAAM,UAAW;gBAAAjC,QAAA,gBAE9BlN,OAAA,CAACnC,QAAQ;kBAACmO,KAAK,EAAC,EAAE;kBAACuC,QAAQ;kBAAArB,QAAA,EAAC;gBAE5B;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACXvM,OAAA,CAACnC,QAAQ;kBAACmO,KAAK,EAAC,YAAY;kBAAAkB,QAAA,EAAC;gBAAW;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eACnDvM,OAAA,CAACnC,QAAQ;kBAACmO,KAAK,EAAC,UAAU;kBAAAkB,QAAA,EAAC;gBAAS;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC,eAC/CvM,OAAA,CAACnC,QAAQ;kBAACmO,KAAK,EAAC,OAAO;kBAAAkB,QAAA,EAAC;gBAAK;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAU,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACd,eAGDvM,OAAA,CAACvC,MAAM;cACLkQ,OAAO,EAAC,WAAW;cACnBF,KAAK,EAAC,OAAO;cACba,OAAO,EAAEnD,cAAe;cACxBoD,QAAQ,EAAEjL,WAAY;cACtByJ,EAAE,EAAE;gBACFyB,eAAe,EAAElL,WAAW,GAAG,SAAS,GAAG,QAAQ;gBACnDmK,KAAK,EAAE,OAAO;gBACdgB,UAAU,EAAE,MAAM;gBAClBC,YAAY,EAAE,KAAK;gBACnBC,KAAK,EAAE,OAAO;gBACdrB,MAAM,EAAE,MAAM;gBACdsB,SAAS,EAAE,gCAAgC;gBAC3CzO,OAAO,EAAE,MAAM;gBACf8M,UAAU,EAAE;cACd,CAAE;cAAAC,QAAA,EAED5J,WAAW,GAAG,eAAe,GAAG;YAAW;cAAA8I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,EAGAzJ,aAAa,IACZJ,aAAa,CAACsB,MAAM,KAAK,CAAC,IAC1B,CAAC8C,cAAc,CAACjF,OAAO,CAAC,mBAAmB,CAAC,iBAC1C7B,OAAA,CAACf,SAAS;UACRmQ,KAAK,EAAC,cAAc;UACpBC,IAAI,EAAC,MAAM;UACXC,QAAQ,EAAExE,gBAAiB,CAAC;UAAA;UAC5ByE,OAAO,EAAEA,CAAA,KAAM;YACbxM,gBAAgB,CAAC,KAAK,CAAC;YACvB+D,cAAc,CAACC,OAAO,CAAC,mBAAmB,EAAE,MAAM,CAAC;UACrD,CAAE;UACFyI,QAAQ,EAAE,IAAK,CAAC;QAAA;UAAApD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACF,EAEFzK,YAAY,iBACX9B,OAAA,CAACd,UAAU;UACTuQ,OAAO,EAAE,IAAK;UACdC,UAAU,EAAEzD,oBAAqB;UACjC1H,GAAG,EAAE7B,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAI;UAC1B3D,OAAO,EAAEA;QAAQ;UAAAwL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACF,EAEAvK,WAAW,iBACVhC,OAAA,CAACb,SAAS;UACRsQ,OAAO,EAAE,IAAK;UACdJ,IAAI,EAAC,MAAM;UACXM,WAAW,EAAC,eAAe;UAC3BtK,EAAE,EAAE3C,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAI;UACzBgL,OAAO,EAAEA,CAAA,KAAM;YACb;YACA;YACAtN,cAAc,CAAC,KAAK,CAAC;UACvB;QAAE;UAAAmK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEAnK,WAAW,iBACVpC,OAAA,CAACb,SAAS;UACRsQ,OAAO,EAAE,IAAK;UACdJ,IAAI,EAAC,QAAQ;UACbM,WAAW,EAAC,aAAa;UACzBtK,EAAE,EAAE3C,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAI;UACzBgL,OAAO,EAAEA,CAAA,KAAM;YACb;YACA;YACAlN,cAAc,CAAC,KAAK,CAAC;UACvB;QAAE;UAAA+J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEArK,aAAa,iBACZlC,OAAA,CAACb,SAAS;UACRsQ,OAAO,EAAE,IAAK;UACdJ,IAAI,EAAC,OAAO;UACZM,WAAW,EAAC,iBAAiB;UAC7BtK,EAAE,EAAE3C,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAI;UACzBgL,OAAO,EAAEA,CAAA,KAAM;YACb;YACA;YACApN,gBAAgB,CAAC,KAAK,CAAC;UACzB;QAAE;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,EAEAjK,cAAc,iBACbtC,OAAA,CAACZ,cAAc;UACbqQ,OAAO,EAAE,IAAK;UACdpK,EAAE,EAAE3C,aAAa,CAAC,CAAC,CAAC,CAAC6B,GAAI;UACzBgL,OAAO,EAAEA,CAAA,KAAM;YACb;YACA;YACAhN,iBAAiB,CAAC,KAAK,CAAC;UAC1B;QAAE;UAAA6J,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF,eAGDvM,OAAA,CAACX,eAAe;UACduQ,IAAI,EAAE1M,mBAAoB;UAC1B2M,UAAU,EAAE7M,eAAgB;UAC5BuM,OAAO,EAAEA,CAAA,KAAMpM,sBAAsB,CAAC,KAAK;QAAE;UAAAiJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvM,OAAA,CAACrC,IAAI;MAACwP,IAAI;MAACC,EAAE,EAAE,EAAG;MAAAF,QAAA,eAChBlN,OAAA,CAACrC,IAAI;QACHkP,SAAS;QACTC,OAAO,EAAE,CAAE;QACXC,EAAE,EAAE;UAAE5M,OAAO,EAAE,MAAM;UAAE8M,UAAU,EAAE;QAAS,CAAE;QAAAC,QAAA,gBAG9ClN,OAAA,CAACrC,IAAI;UAACwP,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAAAH,QAAA,eACvBlN,OAAA,CAACR,kBAAkB;YAACuB,UAAU,EAAEA;UAAW;YAAAqL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,EAGNlP,GAAG,CAACC,OAAO,CAAC6O,QAAQ,EAAE5O,QAAQ,CAACuS,KAAK,CAAC,iBACpC9P,OAAA,CAACrC,IAAI;UAACwP,IAAI;UAACC,EAAE,EAAE,EAAG;UAACC,EAAE,EAAE,CAAE;UAACR,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAI,QAAA,gBAC7ClN,OAAA,CAACrC,IAAI;YAACwP,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eAChBlN,OAAA,CAACN,MAAM;cACL0P,KAAK,EAAC,aAAa;cACnBW,OAAO,EAAE9O,UAAU,aAAVA,UAAU,cAAVA,UAAU,GAAI,CAAE;cACzB+O,IAAI,eACFhQ,OAAA,CAAC9B,qBAAqB;gBACpB6O,EAAE,EAAE;kBAAEU,KAAK,EAAE/M,KAAK,CAACuP,OAAO,CAACC,OAAO,CAACC,IAAI;kBAAEzC,QAAQ,EAAE;gBAAG;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EACNlP,GAAG,CAACC,OAAO,CAAC2K,IAAI,EAAE1K,QAAQ,CAACqF,UAAU,CAAC,iBACrC5C,OAAA,CAACrC,IAAI;YAACwP,IAAI;YAACC,EAAE,EAAE,EAAG;YAAAF,QAAA,eAChBlN,OAAA,CAACN,MAAM;cACL0P,KAAK,EAAC,aAAa;cACnBW,OAAO,GAAAtP,mBAAA,GAAES,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkP,UAAU,cAAA3P,mBAAA,cAAAA,mBAAA,GAAI,CAAE;cAClCuP,IAAI,eACFhQ,OAAA,CAAC9B,qBAAqB;gBACpB6O,EAAE,EAAE;kBAAEU,KAAK,EAAE/M,KAAK,CAACuP,OAAO,CAACC,OAAO,CAACC,IAAI;kBAAEzC,QAAQ,EAAE;gBAAG;cAAE;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CACP;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX;AAAChM,EAAA,CA/8BuBD,QAAQ;EAAA,QAChBtB,QAAQ,EACLR,WAAW,EACZC,WAAW,EACPA,WAAW,EACZA,WAAW,EACXA,WAAW,EACdA,WAAW,EACXA,WAAW,EAkmBHA,WAAW;AAAA;AAAA4R,GAAA,GA1mBb/P,QAAQ;AAAA,IAAAD,EAAA,EAAAgQ,GAAA;AAAAC,YAAA,CAAAjQ,EAAA;AAAAiQ,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}