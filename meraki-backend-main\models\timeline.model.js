'use strict';
const mongoose = require("mongoose")

const taskSchema = new mongoose.Schema({
  
    taskTitle: {
        type: String,
        default: "",
        trim: true
    },
    status:{
        type: String,
        enum: ["pending", "rejected","approved"], // Example statuses
        default: "pending"
    },
    userName:{
        type: String
    }
}, { timestamps: true})

const requestSchema = new mongoose.Schema({
    fromTime : {
        type : Date
    },
    toTime : {
        type : Date
    },
    description : {
        type : String
    },
    status:{
        type: Number,
        default: 0
    },
    updatedByAdmin: {
        type: Boolean,
        default: false
    },
    userName: {
        type: String
    },
    requestFrom: {
        type: String
    },
    user:{
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
    },
    taskDetails: {
        type: taskSchema,
        
    }   
})

const Timeline = mongoose.model("timeline",requestSchema)

module.exports = Timeline