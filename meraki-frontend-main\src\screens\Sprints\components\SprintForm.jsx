import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Typography
} from '@mui/material';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { ProductSelector } from '../../../selectors/ProductSelector';
import { UserSelector } from '../../../selectors/UserSelector';
import { ProductActions } from '../../../slices/actions';

/**
 * Sprint Form Component
 * 
 * Form for creating or editing a sprint.
 */
const SprintForm = ({ sprint, onSubmit, productId }) => {
  const dispatch = useDispatch();
  const products = useSelector(ProductSelector.getProducts());
  const currentUser = useSelector(UserSelector.profile());
  const isEditing = Boolean(sprint);
  
  const [formData, setFormData] = useState({
    name: '',
    goal: '',
    startDate: null,
    endDate: null,
    productId: productId || '',
    status: 'planned'
  });
  
  const [errors, setErrors] = useState({});

  // Fetch products when component mounts
  useEffect(() => {
    dispatch(ProductActions.getProducts());
  }, [dispatch]);

  // Initialize form with sprint data if editing
  useEffect(() => {
    if (sprint) {
      setFormData({
        name: sprint.name || '',
        goal: sprint.goal || '',
        startDate: sprint.startDate ? new Date(sprint.startDate) : null,
        endDate: sprint.endDate ? new Date(sprint.endDate) : null,
        productId: sprint.productId || productId || '',
        status: sprint.status || 'planned'
      });
    } else {
      setFormData({
        name: '',
        goal: '',
        startDate: null,
        endDate: null,
        productId: productId || '',
        status: 'planned'
      });
    }
  }, [sprint, productId]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Handle date changes
  const handleDateChange = (name, date) => {
    setFormData({
      ...formData,
      [name]: date
    });
    
    // Clear error for this field
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };

  // Validate form before submission
  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Sprint name is required';
    }
    
    if (!formData.goal.trim()) {
      newErrors.goal = 'Sprint goal is required';
    }
    
    // Only validate product selection for new sprints
    if (!isEditing && !formData.productId) {
      newErrors.productId = 'Product is required';
    }
    
    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    }
    
    if (!formData.endDate) {
      newErrors.endDate = 'End date is required';
    } else if (formData.startDate && formData.endDate && formData.endDate < formData.startDate) {
      newErrors.endDate = 'End date must be after start date';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (validateForm()) {
      // Format dates for API
      const formattedData = {
        ...formData,
        startDate: formData.startDate ? formData.startDate.toISOString() : null,
        endDate: formData.endDate ? formData.endDate.toISOString() : null
      };
      
      onSubmit(formattedData);
    }
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ mt: 2 }}>
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            label="Sprint Name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            error={Boolean(errors.name)}
            helperText={errors.name}
            required
          />
        </Grid>
        
        <Grid item xs={12} md={6}>
          <FormControl fullWidth error={Boolean(errors.productId)}>
            <InputLabel id="product-label">Product</InputLabel>
            <Select
              labelId="product-label"
              name="productId"
              value={formData.productId}
              onChange={handleChange}
              label="Product"
              disabled={isEditing || Boolean(productId)} // Disable if editing or productId is provided as prop
              required={!isEditing}
            >
              <MenuItem value="">Select a product</MenuItem>
              {products && products.map(product => (
                <MenuItem key={product._id} value={product._id}>
                  {product.productName}
                </MenuItem>
              ))}
            </Select>
            {errors.productId && <FormHelperText>{errors.productId}</FormHelperText>}
            {isEditing && !errors.productId && (
              <FormHelperText>Product cannot be changed after sprint creation</FormHelperText>
            )}
          </FormControl>
        </Grid>
        
        <Grid item xs={12}>
          <TextField
            fullWidth
            label="Sprint Goal"
            name="goal"
            value={formData.goal}
            onChange={handleChange}
            multiline
            rows={2}
            error={Boolean(errors.goal)}
            helpertext={errors.goal}
            required
          />
        </Grid>
        
        <Grid item xs={12}>
          <Typography variant="body2" color="textSecondary">
            Created by: {currentUser?.name || 'You'}
          </Typography>
        </Grid>
        
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <Grid item xs={12} md={6}>
            <DatePicker
              label="Start Date"
              value={formData.startDate}
              onChange={(date) => handleDateChange('startDate', date)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  error={Boolean(errors.startDate)}
                  helpertext={errors.startDate}
                  required
                />
              )}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <DatePicker
              label="End Date"
              value={formData.endDate}
              onChange={(date) => handleDateChange('endDate', date)}
              renderInput={(params) => (
                <TextField
                  {...params}
                  fullWidth
                  error={Boolean(errors.endDate)}
                  helpertext={errors.endDate}
                  required
                />
              )}
              minDate={formData.startDate || undefined}
            />
          </Grid>
        </LocalizationProvider>
        
        {sprint && (
          <Grid item xs={12}>
            <FormControl fullWidth>
              <InputLabel id="status-label">Status</InputLabel>
              <Select
                labelId="status-label"
                name="status"
                value={formData.status}
                onChange={handleChange}
                label="Status"
              >
                <MenuItem value="planned">Planned</MenuItem>
                <MenuItem value="active">Active</MenuItem>
                <MenuItem value="completed">Completed</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        )}
        
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              sx={{ mt: 2 }}
            >
              {sprint ? 'Update Sprint' : 'Create Sprint'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

SprintForm.propTypes = {
  sprint: PropTypes.object,
  onSubmit: PropTypes.func.isRequired,
  productId: PropTypes.string
};

export default SprintForm;