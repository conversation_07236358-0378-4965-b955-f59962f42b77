import {get} from "../utils/api";
const API_URL = "http://localhost:10000/api";
const GetTimelineRequests = async (params) => {

      let result = await get(`${API_URL}/timeline/get`,params) 
      if(result) {
        return result 
   }
};
async function UpdateTimelineRequest(body) {
    const result = await fetch(`${API_URL}/timeline/update/${body.id}`,{
        method: 'PATCH',
        body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }
}
async function CreateTimelineRequest (params) { 
    const result = await fetch(`${API_URL}/timeline/create`, {
        method: 'POST',
         body: JSON.stringify(params),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }
}
async function GetTimelineRequestByDate(date,id) {
    const result = await fetch(`${API_URL}/timeline/get/${date}/${id}`, {
        method: 'GET',
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    const result = await fetch(`${API_URL}/timeline/update-task/${id}/${taskId}`,{
        method: 'PATCH',
        body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }
}
async function DeleteTaskTimelineRequest(id,taskId) {
    const result = await fetch(`${API_URL}/timeline/delete-task/${id}/${taskId}`,{
        method: 'DELETE',
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }
}
export const TimelineService = {
    GetTimelineRequests,
    UpdateTimelineRequest,
    CreateTimelineRequest,
    GetTimelineRequestByDate,
    UpdateTaskTimelineRequest,
    DeleteTaskTimelineRequest
};
