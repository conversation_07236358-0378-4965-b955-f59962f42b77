import {get} from "../utils/api";

const API_URL = "http://localhost:10000/api";

const GetTimelineRequests = async (params) => {
    console.log("Get Timeline Reqiests Params ",params)

      let result = await get(`${API_URL}/timeline/get`,params) 

      if(result) {
        return result 
   }
    
};

async function UpdateTimelineRequest(body) {
    console.log("Update check out styatus ",body)
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/timeline/update/${body.id}`,{
        method: 'PATCH',
        body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }

}

async function CreateTimelineRequest (params) { 

    console.log("Time Line Request Create ",params)
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/timeline/create`, {
        method: 'POST',
         body: JSON.stringify(params),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }
}

async function GetTimelineRequestByDate(date,id) {

    console.log("Get Timeline Request By Date  ",date)

    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/timeline/get/${date}/${id}`, {
        method: 'GET',
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
       console.log("Get Timeline Request By Date Result ",result)
    if(result) {
        return result
    }
}  

async function UpdateTaskTimelineRequest(id,taskId,body) {
    console.log("Update Task Timeline Request ",id,body)
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/timeline/update-task/${id}/${taskId}`,{
        method: 'PATCH',
        body: JSON.stringify(body),
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }
}

async function DeleteTaskTimelineRequest(id,taskId) {
    console.log("Delete Task Timeline Request ",id)
    const token = localStorage.getItem("merakihr-token");
    const result = await fetch(`${API_URL}/timeline/delete-task/${id}/${taskId}`,{
        method: 'DELETE',
         headers: {
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    if(result) {
         return result 
    }
}


export const TimelineService = {
    GetTimelineRequests,
    UpdateTimelineRequest,
    CreateTimelineRequest,
    GetTimelineRequestByDate,
    UpdateTaskTimelineRequest,
    DeleteTaskTimelineRequest

};
 
