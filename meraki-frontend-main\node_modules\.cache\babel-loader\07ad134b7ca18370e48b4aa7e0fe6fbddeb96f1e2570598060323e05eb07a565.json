{"ast": null, "code": "/**\r\n * Utility functions for logging and debugging permissions\r\n */\n\n/**\r\n * Logs all permissions for a user\r\n * @param {Object} user - The user object with permissions\r\n */\nexport const logUserPermissions = user => {\n  if (!user) {\n    return;\n  }\n  if (user.permissions && Array.isArray(user.permissions)) {\n    // Group permissions by feature\n    const permissionsByFeature = {};\n    user.permissions.forEach(p => {\n      permissionsByFeature[p.feat] = p.acts;\n    });\n  }\n};\n\n/**\r\n * Logs all permission checks for a specific route\r\n * @param {string} path - The route path\r\n * @param {Object} permission - The permission object with feat and act properties\r\n * @param {boolean} hasPermission - Whether the user has the permission\r\n * @param {string} reason - The reason for the permission decision\r\n */\nexport const logRoutePermission = (path, permission, hasPermission, reason = '') => {\n  // Function preserved for compatibility but logging removed\n};\nexport default {\n  logUserPermissions,\n  logRoutePermission\n};", "map": {"version": 3, "names": ["logUserPermissions", "user", "permissions", "Array", "isArray", "permissionsByFeature", "for<PERSON>ach", "p", "feat", "acts", "logRoutePermission", "path", "permission", "hasPermission", "reason"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/utils/permissionLogger.js"], "sourcesContent": ["/**\r\n * Utility functions for logging and debugging permissions\r\n */\r\n\r\n/**\r\n * Logs all permissions for a user\r\n * @param {Object} user - The user object with permissions\r\n */\r\nexport const logUserPermissions = (user) => {\r\n  if (!user) {\r\n    return;\r\n  }\r\n\r\n  if (user.permissions && Array.isArray(user.permissions)) {\r\n    // Group permissions by feature\r\n    const permissionsByFeature = {};\r\n    user.permissions.forEach(p => {\r\n      permissionsByFeature[p.feat] = p.acts;\r\n    });\r\n  }\r\n};\r\n\r\n/**\r\n * Logs all permission checks for a specific route\r\n * @param {string} path - The route path\r\n * @param {Object} permission - The permission object with feat and act properties\r\n * @param {boolean} hasPermission - Whether the user has the permission\r\n * @param {string} reason - The reason for the permission decision\r\n */\r\nexport const logRoutePermission = (path, permission, hasPermission, reason = '') => {\r\n  // Function preserved for compatibility but logging removed\r\n};\r\n\r\nexport default {\r\n  logUserPermissions,\r\n  logRoutePermission\r\n};\r\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,OAAO,MAAMA,kBAAkB,GAAIC,IAAI,IAAK;EAC1C,IAAI,CAACA,IAAI,EAAE;IACT;EACF;EAEA,IAAIA,IAAI,CAACC,WAAW,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAACC,WAAW,CAAC,EAAE;IACvD;IACA,MAAMG,oBAAoB,GAAG,CAAC,CAAC;IAC/BJ,IAAI,CAACC,WAAW,CAACI,OAAO,CAACC,CAAC,IAAI;MAC5BF,oBAAoB,CAACE,CAAC,CAACC,IAAI,CAAC,GAAGD,CAAC,CAACE,IAAI;IACvC,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,UAAU,EAAEC,aAAa,EAAEC,MAAM,GAAG,EAAE,KAAK;EAClF;AAAA,CACD;AAED,eAAe;EACbd,kBAAkB;EAClBU;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}