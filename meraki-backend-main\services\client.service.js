
const {db} = require('../models')

const Client = db.client

exports.createClient = async(body) => {
    const result = await Client.create(body)
    return result
}

exports.getClients = async(queries) => {
    const limit = queries.limit ?? 10
    const sort = queries.sort ?? {createdAt:-1}
    const skip = limit * (parseInt(queries.page) - 1);
    const page = queries.page

    const result = await Client.find().limit(limit).skip(skip).sort(sort)
    const counts = await Client.countDocuments({})
    return {
        pagination: {
            perPage: limit,
            currentPage: page,
            counts,
            pages: Math.ceil(counts / limit)
        },
        data:result
    }
}

exports.deleteClient = async(id) => {
    const result = await Client.findByIdAndDelete(id)
    return result
}

exports.updateClient = async(_id,body) => {
    const result = await Client.findByIdAndUpdate(_id,
        body
    )
    return result
}