import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Button
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { UserActions } from 'slices/actions';
import { UserSelector } from 'selectors';
import WorkScheduleForm from './WorkScheduleForm';

// Weekday headers
const daysOfWeek = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

// Generate actual calendar for current month
const generateCalendarDays = (year, month) => {
  const calendar = [];
  const date = new Date(year, month, 1);
  const firstDayIndex = date.getDay(); // 0 = Sunday, 6 = Saturday
  const totalDays = new Date(year, month + 1, 0).getDate(); // total days in month

  let currentDay = 1;
  for (let week = 0; week < 6; week++) {
    const weekDays = [];
    for (let day = 0; day < 7; day++) {
      if ((week === 0 && day < firstDayIndex) || currentDay > totalDays) {
        weekDays.push('');
      } else {
        weekDays.push(currentDay++);
      }
    }
    calendar.push(weekDays);
    if (currentDay > totalDays) { break } // Stop after finishing the month
  }
  return calendar;
};

const MonthWorkSchedule = ({ dateRange }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());

  // Get the current month from dateRange or default to current month
  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();
  const year = currentDate.year();
  const month = currentDate.month(); // 0-based index

  const [open, setOpen] = useState(false);
  const calendar = generateCalendarDays(year, month);

  // Fetch users when component mounts
  useEffect(() => {
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  const handleAddSchedule = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">
          {currentDate.format('MMMM YYYY')}
        </Typography>
        <Button
          variant="contained"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleAddSchedule}
          sx={{ bgcolor: '#1976d2' }}
        >
          Add Schedule
        </Button>
      </Box>

      {/* Calendar Header */}
      <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 0.5, mb: 1 }}>
        {daysOfWeek.map((day) => (
          <Box key={day} sx={{ textAlign: 'center', p: 1 }}>
            <Typography variant="subtitle2" fontWeight="bold">{day}</Typography>
          </Box>
        ))}
      </Box>

      {/* Calendar Grid */}
      {calendar.map((week, weekIdx) => (
        <Box key={weekIdx} sx={{ display: 'grid', gridTemplateColumns: 'repeat(7, 1fr)', gap: 0.5, mb: 0.5 }}>
          {week.map((day, dayIdx) => {
            if (day === '') {
              return (
                <Box
                  key={dayIdx}
                  sx={{
                    height: 80,
                    bgcolor: 'rgba(0,0,0,0.03)',
                    borderRadius: 1
                  }}
                />
              );
            }

            // Create the date string for this day
            const dayDateStr = `${year}-${(month + 1).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;

            // Count users with schedules for this specific day
            const usersWithSchedule = users.filter(user => {
              const hasSchedule = user.workSchedule &&
                user.workSchedule.startTime &&
                user.workSchedule.endTime &&
                user.workSchedule.shiftStart &&
                user.workSchedule.shiftEnd;

              if (!hasSchedule) { return false }

              // Check if this day falls within the schedule date range
              const shiftStartDate = dayjs(user.workSchedule.shiftStart).format('YYYY-MM-DD');
              const shiftEndDate = dayjs(user.workSchedule.shiftEnd).format('YYYY-MM-DD');

              return dayDateStr >= shiftStartDate && dayDateStr <= shiftEndDate;
            });

            return (
              <Box
                key={dayIdx}
                sx={{
                  height: 80,
                  bgcolor: usersWithSchedule.length > 0 ? '#4caf50' : 'rgba(0,0,0,0.03)',
                  borderRadius: 1,
                  position: 'relative',
                  display: 'flex',
                  flexDirection: 'column',
                  p: 1
                }}
              >
                <Typography variant="body2" fontWeight={600} sx={{ color: usersWithSchedule.length > 0 ? 'white' : 'inherit' }}>
                  {day}
                </Typography>

                {/* Show schedule summary if there are users with schedules */}
                {usersWithSchedule.length > 0 && (
                  <Box sx={{ mt: 1, flex: 1 }}>
                    <Typography
                      variant="caption"
                      sx={{
                        fontSize: '8px',
                        color: 'white',
                        fontWeight: 600,
                        display: 'block'
                      }}
                    >
                      {usersWithSchedule.length} Schedule{usersWithSchedule.length > 1 ? 's' : ''}
                    </Typography>
                    {usersWithSchedule.slice(0, 2).map((user, idx) => (
                      <Typography
                        key={idx}
                        variant="caption"
                        sx={{
                          fontSize: '7px',
                          color: 'white',
                          display: 'block',
                          lineHeight: 1.1,
                          opacity: 0.9
                        }}
                      >
                        {user.name?.split(' ')[0]}: {user.workSchedule.startTime}-{user.workSchedule.endTime}
                      </Typography>
                    ))}
                    {usersWithSchedule.length > 2 && (
                      <Typography variant="caption" sx={{ fontSize: '7px', color: 'white', opacity: 0.8 }}>
                        +{usersWithSchedule.length - 2} more
                      </Typography>
                    )}
                  </Box>
                )}
              </Box>
            );
          })}
        </Box>
      ))}

      {/* Work Schedule Form */}
      <WorkScheduleForm
        open={open}
        onClose={handleClose}
        selectedDate={currentDate.format('YYYY-MM-DD')}
      />
    </Box>
  );
};

MonthWorkSchedule.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default MonthWorkSchedule;
