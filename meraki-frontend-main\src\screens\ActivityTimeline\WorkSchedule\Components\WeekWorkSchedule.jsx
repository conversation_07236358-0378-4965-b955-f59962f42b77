import React, { useState, useEffect } from 'react';
import {
  Box, Typography, Button
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import dayjs from 'dayjs';
import PropTypes from 'prop-types';
import { useDispatch, useSelector } from 'react-redux';
import { UserActions } from 'slices/actions';
import { UserSelector } from 'selectors';
import WorkScheduleForm from './WorkScheduleForm';

const WeekWorkSchedule = ({ dateRange }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const [open, setOpen] = useState(false);

  // Fetch users when component mounts
  useEffect(() => {
    dispatch(UserActions.getUsers());
  }, [dispatch]);

  // Generate days for the current week based on dateRange
  const generateWeekDays = () => {
    if (!dateRange?.startDate) {
      // Default to current week if no dateRange provided
      const startOfWeek = dayjs().startOf('week');
      return Array.from({ length: 7 }, (_, i) => {
        const date = startOfWeek.add(i, 'day');
        return {
          label: `${date.format('ddd DD')}`,
          date: date.format('YYYY-MM-DD'),
          fullDate: date
        };
      });
    }

    const startDate = dayjs(dateRange.startDate);
    const endDate = dayjs(dateRange.endDate);
    const days = [];

    // Calculate the difference in days
    const daysDiff = endDate.diff(startDate, 'day') + 1; // +1 to include both start and end dates

    for (let i = 0; i < daysDiff && i < 7; i++) { // Limit to 7 days max for safety
      const currentDate = startDate.add(i, 'day');
      days.push({
        label: `${currentDate.format('ddd DD')}`,
        date: currentDate.format('YYYY-MM-DD'),
        fullDate: currentDate
      });
    }

    return days;
  };

  const days = generateWeekDays();

  // Format the week range display
  const getWeekDisplayText = () => {
    if (days.length > 0) {
      const firstDay = days[0].fullDate;
      const lastDay = days[days.length - 1].fullDate;
      return `${firstDay.format("MMM D")} - ${lastDay.format("MMM D, YYYY")}`;
    }
    return '';
  };

  const handleAddSchedule = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h5">
          {getWeekDisplayText()}
        </Typography>
        <Button
          variant="contained"
          size="small"
          startIcon={<AddIcon />}
          onClick={handleAddSchedule}
          sx={{ bgcolor: '#1976d2' }}
        >
          Add Schedule
        </Button>
      </Box>

      {/* Week Header */}
      <Box sx={{ display: 'flex', mb: 1 }}>
        <Box sx={{ width: '200px' }}></Box>
        {days.map((day) => (
          <Box key={day.date} sx={{ flex: 1, textAlign: 'center' }}>
            <Typography variant="subtitle2">{day.label}</Typography>
          </Box>
        ))}
      </Box>

      {/* User Rows */}
      {users.map((user, rowIndex) => (
        <Box key={rowIndex} sx={{ display: 'flex', alignItems: 'center', mt: 1, gap: 1 }}>
          {/* User Info */}
          <Box sx={{ width: '200px' }}>
            <Typography variant="body1" fontWeight="bold">{user.name || 'Unknown User'}</Typography>
            <Typography variant="caption" color="text.secondary">
              {user.designation?.name || user.role || 'No Role'}
            </Typography>
          </Box>

          {/* Day Cells */}
          {days.map((day, colIndex) => {
            // Check if user has work schedule for this specific day
            const hasSchedule = user.workSchedule &&
              user.workSchedule.startTime &&
              user.workSchedule.endTime &&
              user.workSchedule.shiftStart &&
              user.workSchedule.shiftEnd;

            // Check if this day falls within the schedule date range
            const dayDateStr = day.date;
            const shiftStartDate = hasSchedule ? dayjs(user.workSchedule.shiftStart).format('YYYY-MM-DD') : null;
            const shiftEndDate = hasSchedule ? dayjs(user.workSchedule.shiftEnd).format('YYYY-MM-DD') : null;

            const isScheduledDay = hasSchedule &&
              dayDateStr >= shiftStartDate &&
              dayDateStr <= shiftEndDate;

            return (
              <Box
                key={colIndex}
                sx={{
                  flex: 1,
                  height: 60,
                  bgcolor: isScheduledDay ? '#4caf50' : 'rgba(0,0,0,0.03)',
                  borderRadius: 1,
                  position: 'relative',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mx: 0.5
                }}
              >
                {/* Show schedule info if schedule exists for this day */}
                {isScheduledDay && (
                  <Typography
                    variant="caption"
                    sx={{
                      fontSize: '10px',
                      color: 'white',
                      fontWeight: 600,
                      textAlign: 'center',
                      lineHeight: 1.2
                    }}
                  >
                    {user.workSchedule.scheduleTemplate === 'night_shift' ? 'Night' : 'Day'}
                    <br />
                    {user.workSchedule.startTime}-{user.workSchedule.endTime}
                  </Typography>
                )}
              </Box>
            );
          })}
        </Box>
      ))}

      {/* Work Schedule Form */}
      <WorkScheduleForm
        open={open}
        onClose={handleClose}
        selectedDate={days.length > 0 ? days[0].date : dayjs().format('YYYY-MM-DD')}
      />
    </Box>
  );
};

WeekWorkSchedule.propTypes = {
  dateRange: PropTypes.shape({
    startDate: PropTypes.string,
    endDate: PropTypes.string
  })
};

export default WeekWorkSchedule;
