'use strict';
const Screenshot = require("../models/screenshot.model")
 
 
exports.uploadScreenShot = async (req, res) => {

     if (!req.file) {
            return res.status(400).json({ error: "No file uploaded" });
    }
    const path = req.file.path
    const result = await Screenshot.create({
        imagePath : path,
        captureTime: new Date()
    })

    return res.status(200).send({ message: "Screenshot uploaded!"});
}