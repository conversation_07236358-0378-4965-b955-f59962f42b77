import React, { useEffect, useState, useRef } from 'react';
import {
  Box,
  Card,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableHead,
  Pagination,
  TableRow,
  Hidden,
  Typography,
  Button,
  IconButton,
  CircularProgress
} from "@mui/material";

import styled from "@emotion/styled";
import { DefaultSort } from 'constants/sort';
import { PlayCircle, StopCircle, Delete, Visibility, PauseCircle } from '@mui/icons-material';
import { useDispatch, useSelector } from 'react-redux';
import { ProductSelector, UserSelector } from 'selectors';
import { useParams, useHistory } from 'react-router-dom/cjs/react-router-dom.min';
import ProductHeader from './components/ProductHeader';
import { ProductActions, UserActions } from 'slices/actions';
import TaskInfoComponent from './components/TaskInfoComponent';
import { getGlobalTimerState, setGlobalTimerState, clearGlobalTimerState, formatTime, formatDecimalToTime } from '../../utils/timerUtils';

const FilterBox = styled(Box)(() => ({
  width: "100%",
  marginTop: 30,
  marginBottom: 20,
  display: "flex",
  justifyContent: "space-between",
}));

function TaskHistoryAdmin() {
  const { data } = useParams();
  const tasks = useSelector(ProductSelector.getTasks()) || [];
  const projects = useSelector(ProductSelector.getProducts()) || [];
  const pagination = useSelector(ProductSelector.getTaskPagination()) || {};
  const users = useSelector(UserSelector.getUsers()) || [];
  const profile = useSelector(UserSelector.profile());

  const dispatch = useDispatch();
  const history = useHistory();

  const [product, setProduct] = useState([]);
  const [currentTask, setCurrentTask] = useState(null);
  const [showTaskInfo, setShowTaskInfo] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filter, setFilter] = useState({
    sort: DefaultSort.newest.value,
    page: 1,
  });
  
  // Shared timer state
  const globalTimerState = getGlobalTimerState();
  const [elapsedTime, setElapsedTime] = useState(globalTimerState.elapsedTime || 0);
  const [runningTask, setRunningTask] = useState(globalTimerState.runningTask);
  const timerRef = useRef(null);

  useEffect(() => {
    if (!projects.length) { dispatch(ProductActions.getProducts()) }
    if (!users.length) { dispatch(UserActions.getUsers()) }
  }, []);
  
  // Listen for global timer state changes
  useEffect(() => {
    const handleTimerStateChange = (event) => {
      const newState = event.detail;
      if (newState) {
        setElapsedTime(newState.elapsedTime || 0);
        setRunningTask(newState.runningTask);
      } else {
        setElapsedTime(0);
        setRunningTask(null);
      }
    };

    window.addEventListener('timerStateChanged', handleTimerStateChange);
    return () => window.removeEventListener('timerStateChanged', handleTimerStateChange);
  }, []);

  // Timer effect
  useEffect(() => {
    if (runningTask && !runningTask.isPaused) {
      timerRef.current = setInterval(() => {
        setElapsedTime((prev) => {
          const newTime = prev + 1;
          setGlobalTimerState({ elapsedTime: newTime, runningTask, isRunning: true });
          return newTime;
        });
      }, 1000);

      return () => clearInterval(timerRef.current);
    }
  }, [runningTask]);

  useEffect(() => {
    if (data && projects?.length > 0) {
      const foundProduct = projects.filter((element) => element._id === data);
      setProduct(foundProduct);
    }
  }, [data, projects]);

  useEffect(() => {
    if (data) {
      setLoading(true);
      dispatch(ProductActions.getTasks({ projectid: data, filter }));
      setTimeout(() => setLoading(false), 2000);
    }
  }, [data, filter]);

  useEffect(() => {
    if (tasks.length > 0 && product.length > 0) {
      const updatedProduct = { ...product[0], taskArr: tasks };
      setProduct([updatedProduct]);
    }
  }, [tasks]);

  const handleChangePagination = (e, val) => {
    setFilter((prev) => ({ ...prev, page: val }));
  };

  const manualFetch = () => {
    if (data) {
      setLoading(true);
      dispatch(ProductActions.getTasks({ projectid: data, filter }));
      setTimeout(() => setLoading(false), 2000);
    }
  };

  const handleStart = (taskId) => {
    try {
      // Check if another task is already running
      if (runningTask && !runningTask.isPaused && runningTask.taskId !== taskId) {
        alert("You can only run one task at a time!");
        return;
      }
      
      const projectId = product[0]?._id;
      if (!projectId) {
        console.error("Project ID not found");
        return;
      }
      
      const today = new Date().toISOString().split("T")[0];
      
      // If resuming a paused task
      if (runningTask && runningTask.isPaused && runningTask.taskId === taskId) {
        const resumeTime = Date.now();

        // Find the task and project details
        const currentTask = product[0]?.taskArr.find(t => t._id === taskId);

        // Calculate accumulated time from previous sessions
        let accumulatedTime = 0;
        if (currentTask && currentTask.pauseTimes) {
          accumulatedTime = currentTask.pauseTimes.reduce((total, pause) => {
            return total + (pause.elapsedSeconds || 0);
          }, 0);
        }

        const updatedTask = {
          ...runningTask,
          isPaused: false,
          resumeTime: resumeTime,
          startTime: resumeTime,
          currentDate: today,
          taskTitle: currentTask?.taskTitle || runningTask.taskTitle || 'Unknown Task',
          projectName: product[0]?.productName || runningTask.projectName || 'Unknown Project',
          accumulatedTime: accumulatedTime
        };

        setRunningTask(updatedTask);
        setElapsedTime(0);
        setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: true });
        return;
      }
      
      // Starting new task
      const currentTask = product[0]?.taskArr.find(t => t._id === taskId);

      dispatch(ProductActions.startTask({
        taskId,
        projectId,
        date: today
      }));

      const newTask = {
        taskId,
        projectId,
        startTime: Date.now(),
        isRunning: true,
        isPaused: false,
        currentDate: today,
        taskTitle: currentTask?.taskTitle || 'Unknown Task',
        projectName: product[0]?.productName || 'Unknown Project',
        accumulatedTime: 0
      };
      
      setRunningTask(newTask);
      setElapsedTime(0);
      setGlobalTimerState({ elapsedTime: 0, runningTask: newTask, isRunning: true });
      
      setTimeout(() => manualFetch(), 500);
    } catch (error) {
      console.error("Error starting task:", error);
    }
  };

  const handleStop = (taskId) => {
    try {
      const projectId = product[0]?._id;
      if (!projectId) {
        console.error("Project ID not found");
        return;
      }
      
      const currentElapsedTime = Math.max(0, elapsedTime);
      
      dispatch(ProductActions.stopTask({ 
        taskId, 
        projectId,
        elapsedTime: currentElapsedTime,
        date: new Date().toISOString().split("T")[0]
      }));
      
      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      
      setRunningTask(null);
      setElapsedTime(0);
      clearGlobalTimerState();
      
      setTimeout(() => manualFetch(), 500);
    } catch (error) {
      console.error("Error stopping task:", error);
    }
  };
  
  const handlePause = (taskId) => {
    try {
      if (!runningTask || runningTask.taskId !== taskId) { return }
      
      const projectId = product[0]?._id;
      if (!projectId) {
        console.error("Project ID not found");
        return;
      }
      
      const currentElapsedTime = Math.max(0, elapsedTime);
      const pauseTime = new Date().toISOString();
      const today = new Date().toISOString().split("T")[0];
      
      dispatch(ProductActions.pauseTask({ 
        taskId, 
        projectId,
        elapsedTime: currentElapsedTime,
        pauseTime: pauseTime,
        date: today,
        startTime: new Date(runningTask.startTime).toISOString()
      }));
      
      // Update accumulated time
      const newAccumulatedTime = (runningTask.accumulatedTime || 0) + currentElapsedTime;

      const updatedTask = {
        ...runningTask,
        isPaused: true,
        pausedAt: Date.now(),
        pausedElapsedTime: currentElapsedTime,
        accumulatedTime: newAccumulatedTime
      };

      console.log(`Task paused. Session time: ${currentElapsedTime}s, Total accumulated: ${newAccumulatedTime}s`);
      
      // Clear timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      
      setElapsedTime(0);
      setRunningTask(updatedTask);
      setGlobalTimerState({ elapsedTime: 0, runningTask: updatedTask, isRunning: false });
      
      setTimeout(() => manualFetch(), 500);
    } catch (error) {
      console.error("Error pausing task:", error);
    }
  };

  const handleDelete = (taskId) => {
    // Get projectId from the current product data
    const projectId = product[0]?._id;
    if (!projectId) {
      console.error("Project ID not found");
      return;
    }
    
    dispatch(ProductActions.deleteTask({ 
      taskId, 
      projectId
    }));
    setTimeout(() => manualFetch(), 500);
  };

  const handleView = (task) => {
    setCurrentTask(task);
    setShowTaskInfo(true);
  };

  const closeTaskInfo = () => {
    setShowTaskInfo(false);
    setCurrentTask(null);
    manualFetch();
  };

  const tasksArr = product[0]?.taskArr || [];
  const filteredTasks = tasksArr.filter(
    (task) =>
      task.assignee.includes(profile?._id) || task.reporter === profile?._id
  );

  return (
    <>
      {showTaskInfo && currentTask && (
        <TaskInfoComponent
          data={currentTask}
          productId={product[0]?._id}
          taskInfoController={closeTaskInfo}
        />
      )}

      <Card style={{ overflow: "scroll" }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            {product[0]?.productName || "Loading Product..."}
          </Typography>

          <Button
            variant="contained"
            color="primary"
            onClick={manualFetch}
            disabled={loading}
          >
            Refresh Tasks
          </Button>
        </Box>

        <FilterBox>
          <Grid container spacing={10} justifyContent="space-between">
            <Grid item lg={11} sm={12} xs={12}>
              <ProductHeader product={product} />
            </Grid>
          </Grid>
        </FilterBox>

        <Box>
          {loading ? (
            <Box display="flex" justifyContent="center" p={4}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <Table>
                <TableHead>
                  <TableRow>
                    <Hidden smDown>
                      <TableCell align="center">Task Name</TableCell>
                      <TableCell align="center">Assignee</TableCell>
                      <TableCell align="center">Status</TableCell>
                      <TableCell align="center">Spent/Assigned</TableCell>
                      <TableCell align="center">Action</TableCell>
                    </Hidden>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTasks.length > 0 ? (
                    filteredTasks.map((task, index) => (
                      <TableRow key={index}>
                        <TableCell align="center">{task.taskTitle}</TableCell>
                        <TableCell align="center">
                          {task.assignee.map((assigneeId) => {
                              const user = users.find((u) => u._id === assigneeId);
                              return user ? user.name : null;
                            }).filter((name) => name !== null).join(", ")}
                        </TableCell>
                        <TableCell align="center">
                          {(!runningTask || runningTask.taskId !== task._id || runningTask.isPaused) && (
                            <IconButton 
                              onClick={() => handleStart(task._id)}
                              color="primary"
                              disabled={runningTask && runningTask.taskId !== task._id && !runningTask.isPaused}
                            >
                              <PlayCircle />
                            </IconButton>
                          )}
                          
                          {runningTask && runningTask.taskId === task._id && !runningTask.isPaused && (
                            <IconButton onClick={() => handlePause(task._id)} color="warning">
                              <PauseCircle />
                            </IconButton>
                          )}
                          
                          <IconButton 
                            onClick={() => handleStop(task._id)}
                            color="secondary"
                            disabled={!runningTask || runningTask.taskId !== task._id}
                          >
                            <StopCircle />
                          </IconButton>
                        </TableCell>
                        <TableCell align="center">
                          {runningTask?.taskId === task._id ? formatTime(elapsedTime) : formatDecimalToTime(task.totalSpent)}
                        </TableCell>
                        <TableCell align="center">
                          <IconButton onClick={() => handleView(task)}>
                            <Visibility />
                          </IconButton>
                          <IconButton onClick={() => handleDelete(task._id)}>
                            <Delete />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} align="center">
                        No Tasks Found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {pagination.pages > 0 && (
                <Pagination
                  sx={{ mt: 1 }}
                  page={filter.page}
                  count={pagination.pages}
                  onChange={handleChangePagination}
                />
              )}
            </>
          )}
        </Box>
      </Card>
    </>
  );
}

export default TaskHistoryAdmin;