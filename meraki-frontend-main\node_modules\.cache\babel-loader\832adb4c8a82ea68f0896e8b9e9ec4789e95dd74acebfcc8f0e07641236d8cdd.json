{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4.5 10c-.08 0-.5.03-.5.6 0 2 .82 3.4 2 3.4.51 0 .89-.12 2.69-.86-.07-.2-.12-.41-.15-.62-1.48-.33-2.49-.89-3.39-2.16-.22-.31-.38-.36-.65-.36m5.5 9.5c0 .08.03.5.6.5 2 0 3.4-.82 3.4-2 0-.38-.07-.69-.42-1.59l-.97-.97c-.03.01-.06.02-.09.02-.33 1.48-.89 2.49-2.16 3.39-.31.22-.36.38-.36.65m3.65-14.35c.3-.22.35-.38.35-.65 0-.08-.03-.5-.6-.5-2 0-3.4.82-3.4 2 0 .38.07.69.42 1.59l1.05 1.05.02-.1c.33-1.48.88-2.5 2.16-3.39m5.2 8.5c.21.3.38.35.65.35.08 0 .5-.03.5-.6 0-2-.82-3.4-2-3.4-.51 0-.89.12-2.69.86.07.2.12.41.15.62 1.48.34 2.5.89 3.39 2.17\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 6c0-1.18 1.4-2 3.4-2 .57 0 .6.42.6.5 0 .27-.05.43-.35.65-1.27.9-1.83 1.91-2.16 3.39l-.02.1 7.25 7.25c.24.06.5.11.78.11 1.22 0 2.5-.95 2.5-2.6C22 9.91 20.11 8 18 8c-1.06 0-1.64.29-3.91 1.19-.19-.14-.4-.27-.62-.37.25-1.03.61-1.53 1.33-2.04.81-.57 1.2-1.34 1.2-2.28 0-1.22-.95-2.5-2.6-2.5-3.08 0-4.92 1.47-5.32 3.26l2.33 2.33C10.07 6.69 10 6.38 10 6m8 4c1.18 0 2 1.4 2 3.4 0 .57-.42.6-.5.6-.27 0-.43-.05-.65-.35-.9-1.27-1.91-1.83-3.39-2.16a3.12 3.12 0 0 0-.15-.62c1.8-.75 2.18-.87 2.69-.87M1.39 4.22l3.89 3.89C5.04 8.05 4.78 8 4.5 8 3.28 8 2 8.95 2 10.6 2 14.09 3.89 16 6 16c1.06 0 1.64-.29 3.91-1.19.19.14.4.27.62.37-.25 1.03-.61 1.53-1.33 2.04-.81.57-1.2 1.34-1.2 2.28 0 1.22.95 2.5 2.6 2.5 3.08 0 4.92-1.47 5.32-3.26l3.86 3.86 1.41-1.41L2.81 2.81zm11.13 11.24c.03 0 .06-.02.09-.02l.97.97c.35.9.42 1.21.42 1.59 0 1.18-1.4 2-3.4 2-.57 0-.6-.42-.6-.5 0-.27.05-.43.35-.65 1.28-.89 1.83-1.91 2.17-3.39m-3.98-2.94c.03.22.08.42.15.62-1.8.74-2.18.86-2.69.86-1.18 0-2-1.4-2-3.4 0-.57.42-.6.5-.6.27 0 .43.05.65.35.89 1.28 1.91 1.83 3.39 2.17\"\n}, \"1\")], 'ModeFanOffTwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "opacity"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/node_modules/@mui/icons-material/esm/ModeFanOffTwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4.5 10c-.08 0-.5.03-.5.6 0 2 .82 3.4 2 3.4.51 0 .89-.12 2.69-.86-.07-.2-.12-.41-.15-.62-1.48-.33-2.49-.89-3.39-2.16-.22-.31-.38-.36-.65-.36m5.5 9.5c0 .08.03.5.6.5 2 0 3.4-.82 3.4-2 0-.38-.07-.69-.42-1.59l-.97-.97c-.03.01-.06.02-.09.02-.33 1.48-.89 2.49-2.16 3.39-.31.22-.36.38-.36.65m3.65-14.35c.3-.22.35-.38.35-.65 0-.08-.03-.5-.6-.5-2 0-3.4.82-3.4 2 0 .38.07.69.42 1.59l1.05 1.05.02-.1c.33-1.48.88-2.5 2.16-3.39m5.2 8.5c.21.3.38.35.65.35.08 0 .5-.03.5-.6 0-2-.82-3.4-2-3.4-.51 0-.89.12-2.69.86.07.2.12.41.15.62 1.48.34 2.5.89 3.39 2.17\",\n  opacity: \".3\"\n}, \"0\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M10 6c0-1.18 1.4-2 3.4-2 .57 0 .6.42.6.5 0 .27-.05.43-.35.65-1.27.9-1.83 1.91-2.16 3.39l-.02.1 7.25 7.25c.24.06.5.11.78.11 1.22 0 2.5-.95 2.5-2.6C22 9.91 20.11 8 18 8c-1.06 0-1.64.29-3.91 1.19-.19-.14-.4-.27-.62-.37.25-1.03.61-1.53 1.33-2.04.81-.57 1.2-1.34 1.2-2.28 0-1.22-.95-2.5-2.6-2.5-3.08 0-4.92 1.47-5.32 3.26l2.33 2.33C10.07 6.69 10 6.38 10 6m8 4c1.18 0 2 1.4 2 3.4 0 .57-.42.6-.5.6-.27 0-.43-.05-.65-.35-.9-1.27-1.91-1.83-3.39-2.16a3.12 3.12 0 0 0-.15-.62c1.8-.75 2.18-.87 2.69-.87M1.39 4.22l3.89 3.89C5.04 8.05 4.78 8 4.5 8 3.28 8 2 8.95 2 10.6 2 14.09 3.89 16 6 16c1.06 0 1.64-.29 3.91-1.19.19.14.4.27.62.37-.25 1.03-.61 1.53-1.33 2.04-.81.57-1.2 1.34-1.2 2.28 0 1.22.95 2.5 2.6 2.5 3.08 0 4.92-1.47 5.32-3.26l3.86 3.86 1.41-1.41L2.81 2.81zm11.13 11.24c.03 0 .06-.02.09-.02l.97.97c.35.9.42 1.21.42 1.59 0 1.18-1.4 2-3.4 2-.57 0-.6-.42-.6-.5 0-.27.05-.43.35-.65 1.28-.89 1.83-1.91 2.17-3.39m-3.98-2.94c.03.22.08.42.15.62-1.8.74-2.18.86-2.69.86-1.18 0-2-1.4-2-3.4 0-.57.42-.6.5-.6.27 0 .43.05.65.35.89 1.28 1.91 1.83 3.39 2.17\"\n}, \"1\")], 'ModeFanOffTwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE,4hBAA4hB;EAC/hBC,OAAO,EAAE;AACX,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaF,IAAI,CAAC,MAAM,EAAE;EACjCC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}