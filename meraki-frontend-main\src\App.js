import React, { lazy, Suspense } from "react";
import './App.css';
import { ThemeProvider } from "@mui/material";
import theme from "./theme";
import { ToastContainer } from "react-toastify";
import 'react-toastify/dist/ReactToastify.css';
import { ConnectedRouter } from "connected-react-router";
import { history } from "./store";
import Backgroundprovider from 'screens/Dashboard/components/Backgroundprovider';
import LoadingScreen from "./components/LoadingScreen";

// Lazy load routes
const Routes = lazy(() => import("./routes"));

/**
 * Main Application Component
 *
 * This is the root component of the application that sets up:
 * - Theme provider for Material UI
 * - Toast notifications
 * - Router configuration with code splitting
 * - Background provider for consistent styling
 *
 * @returns {JSX.Element} The rendered application
 */
export default function App() {
  return (
    <Backgroundprovider>
      <ThemeProvider theme={theme}>
        <ToastContainer
          autoClose={3000}
          position="top-right"
          hideProgressBar
          theme="light"
        />
        <ConnectedRouter history={history}>
          <Suspense fallback={<LoadingScreen />}>
            <Routes />
          </Suspense>
        </ConnectedRouter>
      </ThemeProvider>
   </Backgroundprovider>
  );
}