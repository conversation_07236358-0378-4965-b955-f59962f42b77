import { createSlice } from "@reduxjs/toolkit";


export const ActivitySlice = createSlice({
    name: "Activity",
    initialState: {
        activityArr: [], // For single user activity (legacy)
        multiUserActivityArr: [], // For multi-user activity data (new)
    },
    reducers: {
        createTodayGoal: () => {
        },
        createTodayStatus: () => {
        },
        getUserActivitySuccessfull: (state,action) => {
            console.log("🔍 ActivitySlice - Received payload:", action.payload);
            console.log("🔍 ActivitySlice - Payload length:", action.payload?.length || 0);

            if(action.payload.length === 0) {
                state.activityArr = []
                state.multiUserActivityArr = []
                console.log("🔍 ActivitySlice - Empty payload, clearing arrays");
            } else {
                // Check if this is multi-user data vs single-user data
                // Multi-user data has 'name' and 'email' properties and can have different view structures:
                // - Day view: has 'clockin', 'clockout', 'atwork' etc.
                // - Week view: has 'weekData' array and 'total'
                // - Month view: has 'worked', 'focus', 'productive' etc.
                // Single-user data has 'user' and 'checkInTime' properties
                const firstItem = action.payload[0];

                // More robust detection: check for multi-user specific properties
                const hasMultiUserProps = Object.prototype.hasOwnProperty.call(firstItem, 'name') &&
                    Object.prototype.hasOwnProperty.call(firstItem, 'email');
                const hasSingleUserProps = Object.prototype.hasOwnProperty.call(firstItem, 'user') &&
                    Object.prototype.hasOwnProperty.call(firstItem, 'checkInTime');
                const hasActivityViewProps = Object.prototype.hasOwnProperty.call(firstItem, 'clockin') ||
                    Object.prototype.hasOwnProperty.call(firstItem, 'weekData') ||
                    Object.prototype.hasOwnProperty.call(firstItem, 'worked');

                const isMultiUserData = hasMultiUserProps && !hasSingleUserProps && hasActivityViewProps;

                console.log("🔍 ActivitySlice - Checking data type - First item:", firstItem);
                console.log("🔍 ActivitySlice - Detection details:", {
                    hasMultiUserProps,
                    hasSingleUserProps,
                    hasActivityViewProps,
                    isMultiUserData
                });

                if (isMultiUserData) {
                    state.multiUserActivityArr = action.payload;
                    console.log("🔍 ActivitySlice - REDUCER MULTI-USER ACTIVITY LOG ", action.payload);
                    console.log("🔍 ActivitySlice - Multi-user count:", action.payload.length);
                } else {
                    state.activityArr = action.payload;
                    console.log("🔍 ActivitySlice - REDUCER SINGLE-USER ACTIVITY LOG ", action.payload);
                }
            }

        },
        getUserActivity: () => {
        },
        checkOutStatusUpdate: () => {},
        breakStartRed: () => {},
        breakEndRed: () => {},
        lateCheckIn: () => {},
        earlyCheckOut: () => {},
        idelStartRed: () => {},
        idelEndRed: () => {},
        productivityStatusRed: () => {},
        overLimitBreakRed: () => {},
        eraseActivity: (state = []) => {
            state.activityArr = []
            state.multiUserActivityArr = []
        },
        createTimelineRequest: () => {},
        updateTimelineRequest: () => {},
        getTimelineRequests: () => {}
    }
});

export default ActivitySlice;