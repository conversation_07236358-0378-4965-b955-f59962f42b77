// Core dependencies
const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const connectDB = require('./config/connectDB');

// Load environment variables
dotenv.config();

// Initialize Express application
const app = express();

// CORS Configuration
app.use(cors({ origin: "*" }));

// Additional CORS headers for more specific control
app.use((req, res, next) => {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  next();
});

// Request body parsers
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use("/static",express.static(path.join(__dirname,'/static/screenshot')))


// // Initialize Default Data
// // These functions create default records if the database is empty
// const SettingController = require('./controllers/setting.controller');
// SettingController.createDefaultSetting();

// const DepartmentController = require('./controllers/department.controller');
// DepartmentController.createDefaultDepartments();

// const DesignationController = require('./controllers/designation.controller');
// DesignationController.createDefaultDesignations();

// const UserController = require("./controllers/user.controller");
// UserController.createDefaultUsers();

// =====================================================================
// Route Configuration
// =====================================================================

// Public routes (no authentication required)
require("./routes/auth.route")(app);





// Authentication middleware for all /api routes
app.use("/api*", require('./middlewares/auth.middleware'));

// User Management Routes
require("./routes/user.route")(app);


// Organization Structure Routes
require("./routes/department.route")(app);
require("./routes/designation.route")(app);

// HR Management Routes
require("./routes/attendance.route")(app);
require("./routes/leave.route")(app);
require("./routes/expenses.route")(app);

// Activity & Timeline Routes
require("./routes/activity.route")(app);
require('./routes/timeline.route')(app);

// Project Management Routes
require('./routes/product.route')(app);
require('./routes/client.route')(app);
require('./routes/screenshot.route')(app);
require('./routes/sprint.route')(app);

// System Configuration Routes
require("./routes/setting.route")(app);
require('./routes/permissions.route')(app);

// Initialize the scheduler service
const scheduler = require('./services/scheduler.service');

// Start Server
const port = process.env.PORT || 10000;
app.listen(port, () => {
  console.log(`✅ Backend server is running on port ${port}!`);

  // Initialize the scheduler after server starts
  scheduler.initScheduler();
  console.log(`✅ Auto-checkout scheduler initialized!`);
});

// Database Connection
connectDB(process.env.MONGODB_URL);