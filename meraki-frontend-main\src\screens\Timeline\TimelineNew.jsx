import React, { useContext, useEffect, useState, useRef } from 'react';
import {
  Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Typography,
  Box, Button, ButtonGroup, useTheme, Dialog, DialogTitle, DialogContent, DialogActions,
  IconButton, Popper, Popover
} from '@mui/material';
import { backContext } from 'screens/Dashboard/components/Backgroundprovider';
import { useDispatch, useSelector } from 'react-redux';
import { ActivityActions } from '../../slices/actions';
import { ActivitySelector } from '../../selectors/ActivitySelector';
import { UserSelector } from '../../selectors';
import "../../App.css";
// Removed unused imports
import WeeklyPicker from '../Product/WeeklyPicker';
import DayPicker from './DayPicker';
import MonthPicker from './MonthPicker';
import dayjs from 'dayjs';
import isoWeek from "dayjs/plugin/isoWeek";
import weekOfYear from "dayjs/plugin/weekOfYear";

// Import view components
import DayView from './components/DayView';
import WeekView from './components/WeekView';
import MonthView from './components/MonthView';

// Extend dayjs with necessary plugins
dayjs.extend(isoWeek);
dayjs.extend(weekOfYear);

const TimelineNew = () => {
  const dispatch = useDispatch();
  const profile = useSelector(UserSelector.profile());
  const activities = useSelector(ActivitySelector.getActivityHistory());
  const { activities: contextActivities } = useContext(backContext);
  const [data, setData] = useState([]);
  const theme = useTheme();

  console.log("🔍 Timeline data sources:", {
    activitiesFromSelector: activities?.length || 0,
    activitiesFromContext: contextActivities?.length || 0,
    profile: profile?._id
  });

  // Reference to track previous view for smooth transitions
  const previousViewRef = useRef(null);

  // Initialize with current day
  const today = dayjs();
  // Use ISO week (Monday to Sunday) for better week view
  dayjs.extend(isoWeek);
  const startOfWeek = today.startOf('isoWeek');
  const endOfWeek = today.endOf('isoWeek');
  const startOfMonth = today.startOf('month');
  const endOfMonth = today.endOf('month');



  // Create valid Date objects with proper validation
  const createValidDate = (dateString) => {
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? new Date() : date;
    } catch (error) {
      return new Date();
    }
  };

  // Separate date ranges for each view
  // Day view - default to today
  const [dayViewRange, setDayViewRange] = useState({
    startDate: createValidDate(today.format('YYYY-MM-DD')),
    endDate: createValidDate(today.format('YYYY-MM-DD'))
  });

  // We keep selectedRange for backward compatibility
  // It's used to track the selected date range in the DayPicker component
  const [, setSelectedRange] = useState({
    startDate: dayViewRange.startDate,
    endDate: dayViewRange.endDate
  });


  // Week view - default to current week
  const [weekViewRange, setWeekViewRange] = useState({
    startDate: createValidDate(startOfWeek.format('YYYY-MM-DD')),
    endDate: createValidDate(endOfWeek.format('YYYY-MM-DD'))
  });

  // For compatibility with WeeklyPicker
  const [weekRange, setWeekRange] = useState({
    startOfWeek: startOfWeek.format('YYYY-MM-DD'),
    endOfWeek: endOfWeek.format('YYYY-MM-DD'),
    selectedDate: today.format('YYYY-MM-DD')
  });

  // Month view - default to current month
  const [monthViewRange, setMonthViewRange] = useState({
    startDate: createValidDate(startOfMonth.format('YYYY-MM-DD')),
    endDate: createValidDate(endOfMonth.format('YYYY-MM-DD'))
  });

  // Current active date range based on view
  const [startDate, setStartDate] = useState(createValidDate(today.format('YYYY-MM-DD')));
  const [endDate, setEndDate] = useState(createValidDate(today.format('YYYY-MM-DD')));

  // View options
  const [viewOption, setViewOption] = useState("Day");

  // Date picker states - used by MonthPicker

  // We use the UserSelector for potential future user-specific features

  // This function is now handled directly in the WeeklyPicker component

  // Initialize component when it mounts - only set initial date ranges once
  useEffect(() => {
    // Set initial date ranges for all views
    const today = dayjs();
    const startOfWeek = today.startOf('isoWeek');
    const endOfWeek = today.endOf('isoWeek');
    const startOfMonth = today.startOf('month');
    const endOfMonth = today.endOf('month');

    // Initialize week view range
    setWeekViewRange({
      startDate: createValidDate(startOfWeek.format('YYYY-MM-DD')),
      endDate: createValidDate(endOfWeek.format('YYYY-MM-DD'))
    });

    // Initialize day view range (today only)
    setDayViewRange({
      startDate: createValidDate(today.format('YYYY-MM-DD')),
      endDate: createValidDate(today.format('YYYY-MM-DD'))
    });

    // Initialize month view range
    setMonthViewRange({
      startDate: createValidDate(startOfMonth.format('YYYY-MM-DD')),
      endDate: createValidDate(endOfMonth.format('YYYY-MM-DD'))
    });

    // Set the active date range based on the initial view
    // This will only happen once when the component first mounts
    switch(viewOption) {
      case "Day":
        setStartDate(createValidDate(today.format('YYYY-MM-DD')));
        setEndDate(createValidDate(today.format('YYYY-MM-DD')));
        break;
      case "Week":
        setStartDate(createValidDate(startOfWeek.format('YYYY-MM-DD')));
        setEndDate(createValidDate(endOfWeek.format('YYYY-MM-DD')));
        break;
      case "Month":
        setStartDate(createValidDate(startOfMonth.format('YYYY-MM-DD')));
        setEndDate(createValidDate(endOfMonth.format('YYYY-MM-DD')));
        break;
      default:
        break;
    }

    // Set the previous view reference to prevent re-initialization
    previousViewRef.current = viewOption;
  }, []);

  // Fetch activity data when component mounts or profile changes
  useEffect(() => {
    if (profile && profile._id) {
      console.log("🔄 Fetching activity data for Timeline user:", profile._id);
      dispatch(ActivityActions.getUserActivity({
        id: profile._id
      }));
    }
  }, [profile, dispatch]);

  useEffect(() => {
    // Check if we have valid date range and activities
    if (!startDate || !endDate) {
      console.log("❌ Timeline: Missing date range", { startDate, endDate });
      return;
    }

    console.log("🔄 Timeline: Processing data", {
      startDate,
      endDate,
      activitiesCount: activities?.length || 0,
      viewOption
    });

    try {
      const start = new Date(startDate);
      const newData = []; // Initialize an empty array to store the data
      const end = new Date(endDate);

      // Validate date range
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        console.log("❌ Timeline: Invalid date range");
        return;
      }

      // Loop through each day in the date range
      const startMs = start.getTime();
      const endMs = end.getTime();
      let currentMs = startMs;

      while (currentMs <= endMs) {
        // Create a date object for the current day to check
        const dateToCheck = new Date(currentMs);

        // Increment to the next day for the next iteration
        currentMs += 24 * 60 * 60 * 1000; // Add one day in milliseconds

        if (activities && activities.length > 0) {
          let found = false; // Flag to check if a match is found in activities

          // Check each activity for a match with the current date
          for (const activity of activities) {
            if (dateExistOrNot(activity.checkInTime, dateToCheck)) {
              const obj = {
                date: dateFormat(activity.checkInTime),
                atwork: atWorkFormat(activity.totalWorkingTime),
                productivitytime: productivityCalculate(activity.productivityHistory),
                idletime: idleCalculate(activity.idelHistory),
                privatetime: privateCalculate(activity.breaksHistory),
                clockin: dateFormatTime(activity.checkInTime),
                clockout: activity.checkOutTime ? dateFormatTime(activity.checkOutTime) : "--"
              };
              newData.push(obj);
              found = true; // Mark as found
              break; // Exit the loop once a match is found
            }
          }

          // If no match is found, add a default entry for the date
          if (!found) {
            const obj = {
              date: dateFormat(dateToCheck),
              atwork: "--",
              productivitytime: "--",
              idletime: "--",
              privatetime: "--",
              clockin: "--",
              clockout: "--"
            };
            newData.push(obj);
          }
        } else {
          // Add default entry if activities are empty or not provided
          const obj = {
            date: dateFormat(dateToCheck),
            atwork: "--",
            productivitytime: "--",
            idletime: "--",
            privatetime: "--",
            clockin: "--",
            clockout: "--"
          };
          newData.push(obj);
        }
      }

      // Update the state once after the loop completes
      console.log("✅ Timeline: Generated data", {
        dataCount: newData.length,
        sampleData: newData.slice(0, 2),
        dateRange: `${start.toDateString()} to ${end.toDateString()}`
      });
      setData(newData);

      // Only update the selectedRange to keep everything in sync
      // We don't update dayViewRange here to avoid overriding user selections
      setSelectedRange({
        startDate: startDate,
        endDate: endDate
      });

    } catch (error) {
      // Silent error handling
    }
  }, [activities, startDate, endDate]);

  // Update active date range when view changes
  useEffect(() => {
    // When switching between views, use the view-specific date range
    switch(viewOption) {
      case "Day":
        // Use the day view range
        setStartDate(dayViewRange.startDate);
        setEndDate(dayViewRange.endDate);
        break;
      case "Week":
        // Use the week view range
        setStartDate(weekViewRange.startDate);
        setEndDate(weekViewRange.endDate);
        break;
      case "Month":
        // Use the month view range
        setStartDate(monthViewRange.startDate);
        setEndDate(monthViewRange.endDate);
        break;
      default:
        break;
    }

    // Update the previous view reference
    previousViewRef.current = viewOption;
  }, [viewOption, dayViewRange, weekViewRange, monthViewRange]);

  const atWorkFormat = (data) => {
    const min = data % 60
    const hou = Math.floor(data/60)
    const format = data < 60 ? `${min}m`:`${hou}h ${min}m`
    return format
  }

  const dateFormatTime = (data) => {
    if (!data) { return "--" }

    try {
      const date = new Date(data);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return "--";
      }

      const ampm = date.getHours() < 12 ? "AM" : "PM";
      const hour12 = date.getHours() % 12 || 12;
      // Ensure minutes are padded with leading zero if needed
      const minutes = date.getMinutes().toString().padStart(2, '0');
      const timeVal = `${hour12}:${minutes} ${ampm}`;
      return timeVal;
    } catch (error) {
      return "--";
    }
  }

  const dateFormat = (isoDate) => {
    if (!isoDate) {
      return 'Invalid Date';
    }
    try {
      const date = new Date(isoDate);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid Date';
      }

      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();
      const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
      const dayOfWeek = daysOfWeek[date.getDay()];
      return `${day}-${month}-${year} ${dayOfWeek}`;
    } catch (error) {
      return 'Invalid Date';
    }
  };

  const productivityCalculate = (arr) => {
    if (!arr || arr.length === 0) {
        return 0;
    }
    let sum = 0;
    arr.forEach(item => {
        sum += item.productivityFilled
    });
    return sum < 60 ? `${sum}m` : `${Math.floor(sum/60)}h ${Math.floor(sum%60)}m`;
  };

  const idleCalculate = (arr) => {
    if (!arr || arr.length === 0) {
        return 0;
    }
    let sum = 0;
    let diff = 0
    arr.forEach((val) => {
      if(val.idelEndedTime) {
         diff = new Date(val.idelEndedTime) - new Date(val.idelStartedTime)
      }
      else {
        diff = (Date.now()) - new Date(val.idelStartedTime)
      }
      sum+= Math.floor(diff / (1000 * 60))
    })
    return sum < 60 ? `${sum}m` : `${Math.floor(sum/60)}h ${Math.floor(sum%60)}m`;
  };

  // this for private time calculation
  const privateCalculate = (arr) => {
    if (!arr || arr.length === 0) {
      return 0;
    }
    let sum = 0;
    let diff = 0
    arr.forEach((val) => {
      if(val.breakEndedTime) {
         diff = new Date(val.breakEndedTime) - new Date(val.breakStartedTime)
      }
      else {
        diff = Date.now() - new Date(val.breakStartedTime)
      }
      sum+= Math.floor(diff / (1000 * 60))
    })
    return sum < 60 ? `${sum}m` : `${Math.floor(sum/60)}h ${Math.floor(sum%60)}m`;
  };

  const dateExistOrNot = (nonActivityDate, activityDate) => {
    try {
      if (!nonActivityDate || !activityDate) {
        return false;
      }

      // Convert both to Date objects and set to midnight for comparison
      const date1 = new Date(nonActivityDate);
      const date2 = new Date(activityDate);

      // Check if dates are valid
      if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
        return false;
      }

      // Compare year, month, and day
      return date1.getFullYear() === date2.getFullYear() &&
             date1.getMonth() === date2.getMonth() &&
             date1.getDate() === date2.getDate();
    } catch (error) {
      return false;
    }
  }

  return (
    <Box sx={{
      p: 3,
      overflow: "visible", // Changed from "hidden" to allow scrollbars to be visible
    }}>
      <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>Timeline</Typography>

      {/* View options and date picker */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3, position: 'relative' }}>
        {/* Day/Week/Month tabs */}
        <Box sx={{ display: 'flex', borderRadius: '4px', overflow: 'hidden', border: '1px solid #e0e0e0' }}>
          <Button
            onClick={() => setViewOption("Day")}
            sx={{
              bgcolor: viewOption === "Day" ? 'primary.main' : 'transparent',
              color: viewOption === "Day" ? 'white' : 'text.primary',
              borderRadius: 0,
              '&:hover': {
                bgcolor: viewOption === "Day" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            Day
          </Button>
          <Button
            onClick={() => setViewOption("Week")}
            sx={{
              bgcolor: viewOption === "Week" ? 'primary.main' : 'transparent',
              color: viewOption === "Week" ? 'white' : 'text.primary',
              borderRadius: 0,
              '&:hover': {
                bgcolor: viewOption === "Week" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            Week
          </Button>
          <Button
            onClick={() => setViewOption("Month")}
            sx={{
              bgcolor: viewOption === "Month" ? 'primary.main' : 'transparent',
              color: viewOption === "Month" ? 'white' : 'text.primary',
              borderRadius: 0,
              '&:hover': {
                bgcolor: viewOption === "Month" ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            Month
          </Button>
        </Box>

        {/* Date pickers for each view type */}
        <Box sx={{ position: 'absolute', right: 0, top: 0, zIndex: 1 }}>
          {viewOption === "Day" && (
          <DayPicker
          onChange={(newDateRange) => {
            if (newDateRange?.startDate && newDateRange?.endDate) {
              const newStartDate = createValidDate(newDateRange.startDate);
              const newEndDate = createValidDate(newDateRange.endDate);

              // Update the day view range
              setDayViewRange({
                startDate: newStartDate,
                endDate: newEndDate
              });

              // Also update the active date range since we're in Day view
              setStartDate(newStartDate);
              setEndDate(newEndDate);
            }
          }}
          startDate={dayViewRange.startDate}
          endDate={dayViewRange.endDate}
          isRange={dayjs(dayViewRange.startDate).format('YYYY-MM-DD') !== dayjs(dayViewRange.endDate).format('YYYY-MM-DD')}
        />
          )}

          {viewOption === "Week" && (
            <WeeklyPicker
              onChange={(newWeekRangeFn) => {
                // WeeklyPicker passes a function that takes previous state
                const newWeekRange = newWeekRangeFn(weekRange);
                if (newWeekRange) {
                  // Get the new date range
                  const newStartDate = createValidDate(newWeekRange.startOfWeek);
                  const newEndDate = createValidDate(newWeekRange.endOfWeek);

                  // Update the week view range
                  setWeekViewRange({
                    startDate: newStartDate,
                    endDate: newEndDate
                  });

                  // Also update the active date range since we're in Week view
                  setStartDate(newStartDate);
                  setEndDate(newEndDate);

                  // Update the week range for compatibility with the WeeklyPicker component
                  setWeekRange(newWeekRange);
                }
              }}
            />
          )}

          {viewOption === "Month" && (
            <MonthPicker
              onChange={(newDateRange) => {
                const dateRange = newDateRange({});
                if (dateRange) {
                  // Get the new date range
                  const newStartDate = createValidDate(dateRange.startDate);
                  const newEndDate = createValidDate(dateRange.endDate);



                  // Update the month view range
                  setMonthViewRange({
                    startDate: newStartDate,
                    endDate: newEndDate
                  });

                  // Also update the active date range since we're in Month view
                  setStartDate(newStartDate);
                  setEndDate(newEndDate);
                }
              }}
              // Use the month view's selected month
              selectedMonth={monthViewRange.startDate}
            />
          )}
        </Box>

        {/* We've replaced the popover dialogs with inline date pickers */}
      </Box>

      {/* Render different views based on viewOption */}
      {viewOption === "Day" && (
        <>


          {/* Render appropriate day view based on data and date range */}
          {(() => {
            // If data is not ready, show loading state
            if (!data || data.length === 0) {
              return (
                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
                  <Typography variant="h6" color="text.secondary">Loading day data...</Typography>
                </Box>
              );
            }

            // Check if start and end dates are the same
            const isSameDay = dayjs(startDate).format('YYYY-MM-DD') === dayjs(endDate).format('YYYY-MM-DD');

            // If same day, show single day view, otherwise show multi-day view
            if (isSameDay) {
              return <DayView data={data[0]} />;
            } else {
              return <DayView multiDay={true} dataArray={data} />;
            }
          })()}
        </>
      )}

      {viewOption === "Week" && (
        <>
          {data.length > 0 ? (
            <WeekView data={data} />
          ) : (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
              <Typography variant="h6" color="text.secondary">Loading week data...</Typography>
            </Box>
          )}
        </>
      )}

      {viewOption === "Month" && (
        <MonthView data={data} startDate={startDate} />
      )}
    </Box>
  );
};

export default TimelineNew;
