{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\MonthlyWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Paper, LinearProgress, Avatar } from \"@mui/material\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { format, parseISO, getDaysInMonth } from \"date-fns\";\nimport { ActivityActions } from \"../../../../slices/actions\";\n\n// Helper function to parse time strings like \"8h 30m\" to minutes\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst parseTimeToMinutes = timeStr => {\n  var _hourMatch$groups, _minuteMatch$groups;\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\" || timeStr === \"-\") {\n    return 0;\n  }\n\n  // Use named capture groups to extract hours and minutes\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\n  const hours = hourMatch !== null && hourMatch !== void 0 && (_hourMatch$groups = hourMatch.groups) !== null && _hourMatch$groups !== void 0 && _hourMatch$groups.hours ? Math.max(0, parseInt(hourMatch.groups.hours, 10)) : 0;\n  const minutes = minuteMatch !== null && minuteMatch !== void 0 && (_minuteMatch$groups = minuteMatch.groups) !== null && _minuteMatch$groups !== void 0 && _minuteMatch$groups.minutes ? Math.max(0, parseInt(minuteMatch.groups.minutes, 10)) : 0;\n  return Math.max(0, hours * 60 + minutes); // Ensure result is never negative\n};\nconst MonthlyWorkReport = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    multiUserActivityArr\n  } = useSelector(state => state.activity || {\n    multiUserActivityArr: []\n  });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n\n  // Debug logging for month view\n  console.log(\"MonthlyWorkReport - multiUserActivityArr:\", multiUserActivityArr);\n  console.log(\"MonthlyWorkReport - dateRange:\", dateRange);\n\n  // Format the selected month for display\n  const displayMonth = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? format(parseISO(dateRange.startDate), \"MMMM yyyy\") : format(new Date(), \"MMMM yyyy\");\n\n  // Note: API call is handled by the parent Overview component to avoid duplicate requests\n  // useEffect(() => {\n  //   if (dateRange?.startDate && dateRange?.endDate) {\n  //     dispatch(ActivityActions.getUserActivity({\n  //       startDate: dateRange.startDate,\n  //       endDate: dateRange.endDate,\n  //       view: 'month'\n  //     }));\n  //   }\n  // }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Monthly Work Report \\u2013 \", displayMonth]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No employee data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: \"#f5f5f5\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Work Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Worked Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Focus Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Productive Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Idle + Private\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, idx) => {\n            // Calculate monthly progress based on worked hours vs expected hours\n            // Handle both worked and totalWork fields, and fallback to other time fields\n            const workedTime = emp.worked !== \"--\" ? emp.worked : emp.totalWork !== \"--\" ? emp.totalWork : emp.productive !== \"--\" ? emp.productive : \"0h 0m\";\n            const workedMinutes = parseTimeToMinutes(workedTime);\n\n            // Calculate expected monthly working hours\n            // Get the number of days in the current month\n            const currentMonth = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? parseISO(dateRange.startDate) : new Date();\n            const daysInMonth = getDaysInMonth(currentMonth);\n\n            // Assume 22 working days per month (excluding weekends and holidays)\n            // This is a standard approximation, you can adjust based on your business logic\n            const workingDaysInMonth = Math.min(22, daysInMonth);\n            const expectedMonthlyMinutes = workingDaysInMonth * 8 * 60; // working days * 8 hours * 60 minutes\n\n            // Calculate progress percentage\n            const monthlyProgress = expectedMonthlyMinutes > 0 ? Math.min(workedMinutes / expectedMonthlyMinutes * 100, 100) : 0;\n\n            // Debug logging for progress calculation\n            console.log(`${emp.name}: worked=${emp.worked}, totalWork=${emp.totalWork}, productive=${emp.productive}, workedTime=${workedTime}, workedMin=${workedMinutes}, expectedMin=${expectedMonthlyMinutes}, progress=${monthlyProgress}`);\n\n            // Determine color based on progress - show bar for any user with data\n            let barColor = \"inherit\";\n            const hasAnyData = emp.worked !== \"--\" || emp.totalWork !== \"--\" || emp.productive !== \"--\" || emp.focus !== \"--\";\n            if (monthlyProgress >= 90) {\n              barColor = \"success\";\n            } else if (monthlyProgress >= 60) {\n              barColor = \"warning\";\n            } else if (monthlyProgress > 0 || workedMinutes > 0 || hasAnyData) {\n              barColor = \"error\";\n            } // Show red bar for any activity\n\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    children: (emp.name || \"\")[0]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 128,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: emp.name || \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  minWidth: 150\n                },\n                children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: Math.round(monthlyProgress > 0 ? monthlyProgress : hasAnyData ? 3 : 0) // Show minimum 3% if any data exists\n                  ,\n                  color: barColor,\n                  sx: {\n                    height: 6,\n                    borderRadius: 4,\n                    width: \"140px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.totalWork || \"0h 0m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.worked || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.focus || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.productive || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.idle || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 19\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      color: \"gray\",\n      mt: 2,\n      display: \"block\",\n      children: [\"\\u2139\\uFE0F Progress calculation based on \", /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Worked Hours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 42\n      }, this), \" vs expected monthly hours (22 working days \\xD7 8 hours)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), \"\\uD83D\\uDCDD \\\"--\\\" indicates no activity data for the selected month period\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(MonthlyWorkReport, \"LHHZnzIouqrj1jSi+0iP2Ig/qk8=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = MonthlyWorkReport;\nMonthlyWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default MonthlyWorkReport;\nvar _c;\n$RefreshReg$(_c, \"MonthlyWorkReport\");", "map": {"version": 3, "names": ["React", "useEffect", "PropTypes", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "Paper", "LinearProgress", "Avatar", "useSelector", "useDispatch", "format", "parseISO", "getDaysInMonth", "ActivityActions", "jsxDEV", "_jsxDEV", "parseTimeToMinutes", "timeStr", "_hourMatch$groups", "_minuteMatch$groups", "hourMatch", "match", "minuteMatch", "hours", "groups", "Math", "max", "parseInt", "minutes", "MonthlyWorkReport", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "multiUserActivityArr", "state", "activity", "activityArr", "console", "log", "displayMonth", "startDate", "Date", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "sx", "backgroundColor", "map", "emp", "idx", "workedTime", "worked", "totalWork", "productive", "workedMinutes", "currentMonth", "daysInMonth", "workingDaysInMonth", "min", "expectedMonthlyMinutes", "monthlyProgress", "name", "barColor", "hasAnyData", "focus", "display", "alignItems", "gap", "min<PERSON><PERSON><PERSON>", "value", "round", "color", "height", "borderRadius", "width", "idle", "mt", "_c", "propTypes", "shape", "string", "endDate", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/MonthlyWorkReport.jsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\r\nimport PropTypes from \"prop-types\";\r\nimport {\r\n  Box,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Typography,\r\n  Paper,\r\n  LinearProgress,\r\n  Avatar\r\n} from \"@mui/material\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { format, parseISO, getDaysInMonth } from \"date-fns\";\r\nimport { ActivityActions } from \"../../../../slices/actions\";\r\n\r\n// Helper function to parse time strings like \"8h 30m\" to minutes\r\nconst parseTimeToMinutes = (timeStr) => {\r\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\" || timeStr === \"-\") {\r\n    return 0;\r\n  }\r\n\r\n  // Use named capture groups to extract hours and minutes\r\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\r\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\r\n\r\n  const hours = hourMatch?.groups?.hours ? Math.max(0, parseInt(hourMatch.groups.hours, 10)) : 0;\r\n  const minutes = minuteMatch?.groups?.minutes ? Math.max(0, parseInt(minuteMatch.groups.minutes, 10)) : 0;\r\n\r\n  return Math.max(0, (hours * 60) + minutes); // Ensure result is never negative\r\n};\r\n\r\nconst MonthlyWorkReport = ({ dateRange }) => {\r\n  const dispatch = useDispatch();\r\n  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });\r\n\r\n  // Use multiUserActivityArr for the ActivityTimeline components\r\n  const activityArr = multiUserActivityArr;\r\n\r\n  // Debug logging for month view\r\n  console.log(\"MonthlyWorkReport - multiUserActivityArr:\", multiUserActivityArr);\r\n  console.log(\"MonthlyWorkReport - dateRange:\", dateRange);\r\n\r\n  // Format the selected month for display\r\n  const displayMonth = dateRange?.startDate ? format(parseISO(dateRange.startDate), \"MMMM yyyy\") : format(new Date(), \"MMMM yyyy\");\r\n\r\n  // Note: API call is handled by the parent Overview component to avoid duplicate requests\r\n  // useEffect(() => {\r\n  //   if (dateRange?.startDate && dateRange?.endDate) {\r\n  //     dispatch(ActivityActions.getUserActivity({\r\n  //       startDate: dateRange.startDate,\r\n  //       endDate: dateRange.endDate,\r\n  //       view: 'month'\r\n  //     }));\r\n  //   }\r\n  // }, [dateRange, dispatch]);\r\n\r\n  // If data is not available, show placeholder\r\n  if (!activityArr || activityArr.length === 0) {\r\n    return (\r\n      <Box p={3}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          Monthly Work Report – {displayMonth}\r\n        </Typography>\r\n        <Typography>No employee data available</Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box p={3}>\r\n      {/* <Typography variant=\"h6\" gutterBottom>\r\n        Monthly Work Report – {displayMonth}\r\n      </Typography> */}\r\n      <TableContainer component={Paper}>\r\n        <Table>\r\n          <TableHead>\r\n            <TableRow sx={{ backgroundColor: \"#f5f5f5\" }}>\r\n              <TableCell><strong>Name</strong></TableCell>\r\n              <TableCell></TableCell>\r\n              <TableCell><strong>Total Work Hours</strong></TableCell>\r\n              <TableCell><strong>Worked Hours</strong></TableCell>\r\n              <TableCell><strong>Focus Hours</strong></TableCell>\r\n              <TableCell><strong>Productive Hours</strong></TableCell>\r\n              <TableCell><strong>Idle + Private</strong></TableCell>\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {activityArr.map((emp, idx) => {\r\n              // Calculate monthly progress based on worked hours vs expected hours\r\n              // Handle both worked and totalWork fields, and fallback to other time fields\r\n              const workedTime = emp.worked !== \"--\" ? emp.worked :\r\n                                emp.totalWork !== \"--\" ? emp.totalWork :\r\n                                emp.productive !== \"--\" ? emp.productive : \"0h 0m\";\r\n              const workedMinutes = parseTimeToMinutes(workedTime);\r\n\r\n              // Calculate expected monthly working hours\r\n              // Get the number of days in the current month\r\n              const currentMonth = dateRange?.startDate ? parseISO(dateRange.startDate) : new Date();\r\n              const daysInMonth = getDaysInMonth(currentMonth);\r\n\r\n              // Assume 22 working days per month (excluding weekends and holidays)\r\n              // This is a standard approximation, you can adjust based on your business logic\r\n              const workingDaysInMonth = Math.min(22, daysInMonth);\r\n              const expectedMonthlyMinutes = workingDaysInMonth * 8 * 60; // working days * 8 hours * 60 minutes\r\n\r\n              // Calculate progress percentage\r\n              const monthlyProgress = expectedMonthlyMinutes > 0 ? Math.min((workedMinutes / expectedMonthlyMinutes) * 100, 100): 0;\r\n\r\n              // Debug logging for progress calculation\r\n              console.log(`${emp.name}: worked=${emp.worked}, totalWork=${emp.totalWork}, productive=${emp.productive}, workedTime=${workedTime}, workedMin=${workedMinutes}, expectedMin=${expectedMonthlyMinutes}, progress=${monthlyProgress}`);\r\n\r\n              // Determine color based on progress - show bar for any user with data\r\n              let barColor = \"inherit\";\r\n              const hasAnyData = emp.worked !== \"--\" || emp.totalWork !== \"--\" || emp.productive !== \"--\" || emp.focus !== \"--\";\r\n\r\n              if (monthlyProgress >= 90) { barColor = \"success\"; }\r\n              else if (monthlyProgress >= 60) { barColor = \"warning\"; }\r\n              else if (monthlyProgress > 0 || workedMinutes > 0 || hasAnyData) { barColor = \"error\"; } // Show red bar for any activity\r\n\r\n              return (\r\n                <TableRow key={idx}>\r\n                  <TableCell>\r\n                    <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                      <Avatar>{(emp.name || \"\")[0]}</Avatar>\r\n                      <Typography>{emp.name || \"\"}</Typography>\r\n                    </Box>\r\n                  </TableCell>\r\n                  <TableCell sx={{ minWidth: 150 }}>\r\n                    <LinearProgress\r\n                      variant=\"determinate\"\r\n                      value={Math.round(monthlyProgress > 0 ? monthlyProgress : (hasAnyData ? 3 : 0))} // Show minimum 3% if any data exists\r\n                      color={barColor}\r\n                      sx={{ height: 6, borderRadius: 4, width: \"140px\" }}\r\n                    />\r\n                  </TableCell>\r\n                  <TableCell>{emp.totalWork || \"0h 0m\"}</TableCell>\r\n                  <TableCell>{emp.worked || \"--\"}</TableCell>\r\n                  <TableCell>{emp.focus || \"--\"}</TableCell>\r\n                  <TableCell>{emp.productive || \"--\"}</TableCell>\r\n                  <TableCell>{emp.idle || \"--\"}</TableCell>\r\n                </TableRow>\r\n              );\r\n            })}\r\n          </TableBody>\r\n        </Table>\r\n      </TableContainer>\r\n      <Typography variant=\"caption\" color=\"gray\" mt={2} display=\"block\">\r\n        ℹ️ Progress calculation based on <strong>Worked Hours</strong> vs expected monthly hours (22 working days × 8 hours)\r\n        <br />\r\n        📝 \"--\" indicates no activity data for the selected month period\r\n      </Typography>\r\n    </Box>\r\n  );\r\n};\r\n\r\nMonthlyWorkReport.propTypes = {\r\n  dateRange: PropTypes.shape({\r\n    startDate: PropTypes.string,\r\n    endDate: PropTypes.string\r\n  })\r\n};\r\n\r\nexport default MonthlyWorkReport;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,cAAc,EACdC,MAAM,QACD,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,UAAU;AAC3D,SAASC,eAAe,QAAQ,4BAA4B;;AAE5D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;EAAA,IAAAC,iBAAA,EAAAC,mBAAA;EACtC,IAAI,CAACF,OAAO,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,GAAG,EAAE;IAC5E,OAAO,CAAC;EACV;;EAEA;EACA,MAAMG,SAAS,GAAGH,OAAO,CAACI,KAAK,CAAC,gBAAgB,CAAC;EACjD,MAAMC,WAAW,GAAGL,OAAO,CAACI,KAAK,CAAC,kBAAkB,CAAC;EAErD,MAAME,KAAK,GAAGH,SAAS,aAATA,SAAS,gBAAAF,iBAAA,GAATE,SAAS,CAAEI,MAAM,cAAAN,iBAAA,eAAjBA,iBAAA,CAAmBK,KAAK,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAACP,SAAS,CAACI,MAAM,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;EAC9F,MAAMK,OAAO,GAAGN,WAAW,aAAXA,WAAW,gBAAAH,mBAAA,GAAXG,WAAW,CAAEE,MAAM,cAAAL,mBAAA,eAAnBA,mBAAA,CAAqBS,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAACL,WAAW,CAACE,MAAM,CAACI,OAAO,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;EAExG,OAAOH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGH,KAAK,GAAG,EAAE,GAAIK,OAAO,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEwB;EAAqB,CAAC,GAAGzB,WAAW,CAAE0B,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI;IAAEF,oBAAoB,EAAE;EAAG,CAAC,CAAC;;EAEvG;EACA,MAAMG,WAAW,GAAGH,oBAAoB;;EAExC;EACAI,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEL,oBAAoB,CAAC;EAC9EI,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAER,SAAS,CAAC;;EAExD;EACA,MAAMS,YAAY,GAAGT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEU,SAAS,GAAG9B,MAAM,CAACC,QAAQ,CAACmB,SAAS,CAACU,SAAS,CAAC,EAAE,WAAW,CAAC,GAAG9B,MAAM,CAAC,IAAI+B,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC;;EAEhI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,IAAI,CAACL,WAAW,IAAIA,WAAW,CAACM,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACE3B,OAAA,CAAClB,GAAG;MAAC8C,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACR7B,OAAA,CAACX,UAAU;QAACyC,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,GAAC,6BACd,EAACL,YAAY;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACbnC,OAAA,CAACX,UAAU;QAAAwC,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEnC,OAAA,CAAClB,GAAG;IAAC8C,CAAC,EAAE,CAAE;IAAAC,QAAA,gBAIR7B,OAAA,CAACd,cAAc;MAACkD,SAAS,EAAE9C,KAAM;MAAAuC,QAAA,eAC/B7B,OAAA,CAACjB,KAAK;QAAA8C,QAAA,gBACJ7B,OAAA,CAACb,SAAS;UAAA0C,QAAA,eACR7B,OAAA,CAACZ,QAAQ;YAACiD,EAAE,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAT,QAAA,gBAC3C7B,OAAA,CAACf,SAAS;cAAA4C,QAAA,eAAC7B,OAAA;gBAAA6B,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5CnC,OAAA,CAACf,SAAS;cAAA+C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvBnC,OAAA,CAACf,SAAS;cAAA4C,QAAA,eAAC7B,OAAA;gBAAA6B,QAAA,EAAQ;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDnC,OAAA,CAACf,SAAS;cAAA4C,QAAA,eAAC7B,OAAA;gBAAA6B,QAAA,EAAQ;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpDnC,OAAA,CAACf,SAAS;cAAA4C,QAAA,eAAC7B,OAAA;gBAAA6B,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnDnC,OAAA,CAACf,SAAS;cAAA4C,QAAA,eAAC7B,OAAA;gBAAA6B,QAAA,EAAQ;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDnC,OAAA,CAACf,SAAS;cAAA4C,QAAA,eAAC7B,OAAA;gBAAA6B,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZnC,OAAA,CAAChB,SAAS;UAAA6C,QAAA,EACPR,WAAW,CAACkB,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;YAC7B;YACA;YACA,MAAMC,UAAU,GAAGF,GAAG,CAACG,MAAM,KAAK,IAAI,GAAGH,GAAG,CAACG,MAAM,GACjCH,GAAG,CAACI,SAAS,KAAK,IAAI,GAAGJ,GAAG,CAACI,SAAS,GACtCJ,GAAG,CAACK,UAAU,KAAK,IAAI,GAAGL,GAAG,CAACK,UAAU,GAAG,OAAO;YACpE,MAAMC,aAAa,GAAG7C,kBAAkB,CAACyC,UAAU,CAAC;;YAEpD;YACA;YACA,MAAMK,YAAY,GAAGhC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEU,SAAS,GAAG7B,QAAQ,CAACmB,SAAS,CAACU,SAAS,CAAC,GAAG,IAAIC,IAAI,CAAC,CAAC;YACtF,MAAMsB,WAAW,GAAGnD,cAAc,CAACkD,YAAY,CAAC;;YAEhD;YACA;YACA,MAAME,kBAAkB,GAAGvC,IAAI,CAACwC,GAAG,CAAC,EAAE,EAAEF,WAAW,CAAC;YACpD,MAAMG,sBAAsB,GAAGF,kBAAkB,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;;YAE5D;YACA,MAAMG,eAAe,GAAGD,sBAAsB,GAAG,CAAC,GAAGzC,IAAI,CAACwC,GAAG,CAAEJ,aAAa,GAAGK,sBAAsB,GAAI,GAAG,EAAE,GAAG,CAAC,GAAE,CAAC;;YAErH;YACA7B,OAAO,CAACC,GAAG,CAAC,GAAGiB,GAAG,CAACa,IAAI,YAAYb,GAAG,CAACG,MAAM,eAAeH,GAAG,CAACI,SAAS,gBAAgBJ,GAAG,CAACK,UAAU,gBAAgBH,UAAU,eAAeI,aAAa,iBAAiBK,sBAAsB,cAAcC,eAAe,EAAE,CAAC;;YAEpO;YACA,IAAIE,QAAQ,GAAG,SAAS;YACxB,MAAMC,UAAU,GAAGf,GAAG,CAACG,MAAM,KAAK,IAAI,IAAIH,GAAG,CAACI,SAAS,KAAK,IAAI,IAAIJ,GAAG,CAACK,UAAU,KAAK,IAAI,IAAIL,GAAG,CAACgB,KAAK,KAAK,IAAI;YAEjH,IAAIJ,eAAe,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAE,CAAC,MAC/C,IAAIF,eAAe,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAE,CAAC,MACpD,IAAIF,eAAe,GAAG,CAAC,IAAIN,aAAa,GAAG,CAAC,IAAIS,UAAU,EAAE;cAAED,QAAQ,GAAG,OAAO;YAAE,CAAC,CAAC;;YAEzF,oBACEtD,OAAA,CAACZ,QAAQ;cAAAyC,QAAA,gBACP7B,OAAA,CAACf,SAAS;gBAAA4C,QAAA,eACR7B,OAAA,CAAClB,GAAG;kBAAC2E,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAA9B,QAAA,gBAC7C7B,OAAA,CAACR,MAAM;oBAAAqC,QAAA,EAAE,CAACW,GAAG,CAACa,IAAI,IAAI,EAAE,EAAE,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACtCnC,OAAA,CAACX,UAAU;oBAAAwC,QAAA,EAAEW,GAAG,CAACa,IAAI,IAAI;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZnC,OAAA,CAACf,SAAS;gBAACoD,EAAE,EAAE;kBAAEuB,QAAQ,EAAE;gBAAI,CAAE;gBAAA/B,QAAA,eAC/B7B,OAAA,CAACT,cAAc;kBACbuC,OAAO,EAAC,aAAa;kBACrB+B,KAAK,EAAEnD,IAAI,CAACoD,KAAK,CAACV,eAAe,GAAG,CAAC,GAAGA,eAAe,GAAIG,UAAU,GAAG,CAAC,GAAG,CAAE,CAAE,CAAC;kBAAA;kBACjFQ,KAAK,EAAET,QAAS;kBAChBjB,EAAE,EAAE;oBAAE2B,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE,CAAC;oBAAEC,KAAK,EAAE;kBAAQ;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZnC,OAAA,CAACf,SAAS;gBAAA4C,QAAA,EAAEW,GAAG,CAACI,SAAS,IAAI;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjDnC,OAAA,CAACf,SAAS;gBAAA4C,QAAA,EAAEW,GAAG,CAACG,MAAM,IAAI;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3CnC,OAAA,CAACf,SAAS;gBAAA4C,QAAA,EAAEW,GAAG,CAACgB,KAAK,IAAI;cAAI;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CnC,OAAA,CAACf,SAAS;gBAAA4C,QAAA,EAAEW,GAAG,CAACK,UAAU,IAAI;cAAI;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/CnC,OAAA,CAACf,SAAS;gBAAA4C,QAAA,EAAEW,GAAG,CAAC2B,IAAI,IAAI;cAAI;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GAnB5BM,GAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBR,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACjBnC,OAAA,CAACX,UAAU;MAACyC,OAAO,EAAC,SAAS;MAACiC,KAAK,EAAC,MAAM;MAACK,EAAE,EAAE,CAAE;MAACX,OAAO,EAAC,OAAO;MAAA5B,QAAA,GAAC,6CAC/B,eAAA7B,OAAA;QAAA6B,QAAA,EAAQ;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,6DAC9D,eAAAnC,OAAA;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gFAER;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACnB,EAAA,CA1HIF,iBAAiB;EAAA,QACJpB,WAAW,EACKD,WAAW;AAAA;AAAA4E,EAAA,GAFxCvD,iBAAiB;AA4HvBA,iBAAiB,CAACwD,SAAS,GAAG;EAC5BvD,SAAS,EAAElC,SAAS,CAAC0F,KAAK,CAAC;IACzB9C,SAAS,EAAE5C,SAAS,CAAC2F,MAAM;IAC3BC,OAAO,EAAE5F,SAAS,CAAC2F;EACrB,CAAC;AACH,CAAC;AAED,eAAe1D,iBAAiB;AAAC,IAAAuD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}