'use strict';

/**
 * User Service
 *
 * This service provides methods for user-related database operations.
 * It handles CRUD operations for users and specialized operations like leave management.
 */

const bcrypt = require('bcryptjs');
const { db } = require("../models");
const User = db.user;
const DefaultPermissionsService = require('./defaultPermissions.service');

/**
 * Create multiple users at once
 * @param {Array} data - Array of user objects to create
 * @returns {Promise<Array>} Array of created user documents
 */
exports.createManyUsers = async (data) => {
    try {
        const processedData = [];
        for (const item of data) {
            const permissions = item.permissions || DefaultPermissionsService.getDefaultPermissions(item.role);

            processedData.push({
                ...item,
                password: bcrypt.hashSync(item.password, 8),
                permissions
            });
        }

        return await User.insertMany(processedData);
    } catch (error) {
        throw new Error(`Error creating multiple users: ${error.message}`);
    }
}

/**
 * Get users with filtering, pagination, and sorting
 *
 * @param {Object} queries - Query parameters
 * @param {number} queries.limit - Number of results per page
 * @param {number} queries.page - Page number
 * @param {Object} queries.sort - Sort criteria
 * @param {Object} queries.query - Filter criteria
 * @returns {Promise<Object>} Object with pagination info and user data
 */
exports.getUsersByQuery = async (queries) => {
    try {
        // Set default values for pagination and sorting
        const limit = queries.limit ?? 20;
        const page = queries.page ?? 1;
        const skip = limit * (page - 1);
        const sort = queries.sort ?? { createdAt: -1 };
        const query = queries.query ?? {};

        // Fetch users with pagination, sorting, and populate references
        const results = await User
            .find(query)
            .skip(skip)
            .limit(limit)
            .sort(sort)
            .populate({ path: "department", select: "name", options: { strictPopulate: false } })
            .populate({ path: "designation", select: "name", options: { strictPopulate: false } })
            .lean();

        // Ensure department, designation, and role have default values to prevent frontend errors
        const processedResults = results.map(user => ({
            ...user,
            department: user.department || { name: 'N/A' },
            designation: user.designation || { name: 'N/A' },
            role: user.role || 'employee'
        }));

        // Count total matching documents for pagination
        const counts = await User.countDocuments(query);

        // Return formatted response with pagination info
        return {
            query,
            pagination: {
                perPage: limit,
                currentPage: page,
                counts,
                pages: Math.ceil(counts / limit)
            },
            data: processedResults
        };
    } catch (error) {
        throw new Error(`Error fetching users: ${error.message}`);
    }
}

/**
 * Create a new user
 * @param {Object} data - User data
 * @returns {Promise<Object>} Created user document
 */
exports.createUser = async (data) => {
    try {
        if (!data.permissions || !Array.isArray(data.permissions) || data.permissions.length === 0) {
            data.permissions = DefaultPermissionsService.getDefaultPermissions(data.role);
        }
        return await User.create(data);
    } catch (error) {
        throw new Error(`Error creating user: ${error.message}`);
    }
};

/**
 * Get a user by ID
 *
 * @param {string} id - User ID
 * @returns {Promise<Object|null>} User document or null if not found
 */
exports.getUserById = async (id) => {
    try {
        // Find user by ID and populate references
        const user = await User.findById(id)
            .populate({ path: "department", select: "name", options: { strictPopulate: false } })
            .populate({ path: "designation", select: "name", options: { strictPopulate: false } })
            .lean();
        
        if (!user) return null;
        
        // Ensure department, designation, and role have default values
        return {
            ...user,
            department: user.department || { name: 'N/A' },
            designation: user.designation || { name: 'N/A' },
            role: user.role || 'employee'
        };
    } catch (error) {
        // Log error and return null to indicate user not found
        console.error(`Error fetching user by ID ${id}:`, error.message);
        return null;
    }
};

/**
 * Update a user
 * @param {string} id - User ID
 * @param {Object} data - Updated user data
 * @returns {Promise<Object|null>} Updated user document or null if not found
 */
exports.updateUser = async (id, data) => {
    try {
        if (data.role && (!data.permissions || !Array.isArray(data.permissions))) {
            const currentUser = await User.findById(id);
            const currentRole = currentUser.role || 'employee';
            const newRole = data.role || 'employee';

            if (currentRole !== newRole) {
                data.permissions = DefaultPermissionsService.getDefaultPermissions(newRole);
            }
        }

        return await User.findByIdAndUpdate(id, data, { new: true });
    } catch (error) {
        throw new Error(`Error updating user: ${error.message}`);
    }
};

/**
 * Delete a user
 *
 * @param {string} id - User ID
 * @returns {Promise<Object|null>} Deleted user document or null if not found
 */
exports.deleteUser = async (id) => {
    try {
        return await User.findByIdAndDelete(id);
    } catch (error) {
        throw new Error(`Error deleting user: ${error.message}`);
    }
};

/**
 * Update a user's leave information
 *
 * @param {string} id - User ID
 * @param {Object} body - Leave data
 * @param {string} body.type - Leave type ('fullday' or 'halfday')
 * @param {string} body.specifictype - Specific leave type ('paidleave', 'unpaidleave', or other)
 * @returns {Promise<Object>} Updated user document
 */
exports.updateUserLeave = async (id, body) => {
    try {
        // First, get the current user to check available leaves
        const currentUser = await User.findById(id);
        if (!currentUser) {
            throw new Error(`User with ID ${id} not found`);
        }

        // Calculate leave amount based on type
        const leaveAmount = body.type === "fullday" ? 1 : 0.5;
        
        // Check if user has sufficient available leaves
        const currentAvailableLeaves = currentUser.availableLeaves || 0;
        if (currentAvailableLeaves < leaveAmount) {
            throw new Error(`Insufficient leave balance. Available: ${currentAvailableLeaves}, Required: ${leaveAmount}`);
        }

        let update = {};

        // Determine leave amount based on type (full day or half day)
        switch (body.type) {
            case "fullday":
                update = { $inc: { availableLeaves: -1, leaveTaken: 1 } };
                break;
            case "halfday":
                update = { $inc: { availableLeaves: -0.5, leaveTaken: 0.5 } };
                break;
            default:
                throw new Error("Invalid leave type. Must be 'fullday' or 'halfday'.");
        }

        // Initialize increment fields if they don't exist
        update.$inc.paidLeaves = 0;
        update.$inc.unpaidLeaves = 0;
        update.$inc.otherLeaves = 0;

        // Determine specific leave category
        if (body.specifictype === "paidleave") {
            update.$inc.paidLeaves = leaveAmount;
        } else if (body.specifictype === "unpaidleave") {
            update.$inc.unpaidLeaves = leaveAmount;
        } else {
            update.$inc.otherLeaves = leaveAmount;
        }

        // Update user document with leave information, ensuring no negative values
        const result = await User.findOneAndUpdate(
            { 
                _id: id,
                availableLeaves: { $gte: leaveAmount } // Additional safety check
            },
            update,
            { returnOriginal: false, new: true }
        );

        if (!result) {
            throw new Error(`Failed to update leave - insufficient balance or user not found`);
        }

        return result;
    } catch (error) {
        throw new Error(`Error updating user leave: ${error.message}`);
    }
};

/**
 * Update a user's available leaves by admin
 *
 * @param {string} id - User ID
 * @param {Object} body - Request body containing leave data
 * @returns {Promise<Object>} Updated user document
 */
exports.updateUserLeaveAdmin = async (id, body) => {
    try {
        let data;
        
        // Handle different body formats
        if (typeof body === 'string') {
            data = JSON.parse(body);
        } else if (body.body) {
            data = typeof body.body === 'string' ? JSON.parse(body.body) : body.body;
        } else {
            data = body;
        }

        // Validate leave amount
        const leaveAmount = parseFloat(data.leaveAvailable);
        if (isNaN(leaveAmount) || leaveAmount < 0) {
            throw new Error("Invalid leave amount. Must be a positive number.");
        }

        // Update user's available leaves and reset total leaves
        const updateData = {
            availableLeaves: leaveAmount,
            totalLeaves: leaveAmount, // Set total leaves to match available leaves
            updatedAt: new Date()
        };

        const result = await User.findOneAndUpdate(
            { _id: id },
            { $set: updateData },
            { new: true }
        );

        if (!result) {
            throw new Error(`User with ID ${id} not found`);
        }

        console.log(`Updated user ${id} leave balance to ${leaveAmount}`);
        return result;
    } catch (error) {
        console.error("Error in updateUserLeaveAdmin:", error);
        throw new Error(`Error updating user leave by admin: ${error.message}`);
    }
};
