{"ast": null, "code": "import { get } from \"lodash\";\nconst {\n  createSlice\n} = require(\"@reduxjs/toolkit\");\nexport const ProductSlice = createSlice({\n  name: \"product\",\n  initialState: {\n    products: [],\n    pagination: {},\n    product: {},\n    ongoingProductTaskToday: {}\n  },\n  reducers: {\n    createProduct: () => {},\n    deleteProduct: () => {},\n    updateProduct: () => {},\n    getProductById: () => {},\n    getSuccessfullyProducts: (state, action) => {\n      if (action.payload.length === 0) {\n        state.pagination = {};\n        state.products = [];\n      } else {\n        state.products = action.payload.data;\n        state.pagination = action.payload.pagination;\n      }\n    },\n    getSuccessfullyProductById: (state, action) => {\n      if (action.payload.length === 0) {\n        state.pagination = {};\n        state.product = [];\n      } else {\n        state.product = action.payload.data;\n        state.pagination = action.payload.pagination;\n      }\n      console.log(\"State Product \", state.product, action.payload.data);\n    },\n    getSuccessfullyOnGoingProductsTasksToday: (state, action) => {\n      state.ongoingProductTaskToday = action.payload.data;\n    },\n    getProducts: () => {},\n    createTaskByAdmin: () => {},\n    createProductsTaskByUser: () => {},\n    getProductsByUser: () => {},\n    updateTask: () => {},\n    getTasks: () => {},\n    startTask: () => {},\n    stopTask: () => {},\n    pauseTask: () => {},\n    deleteTask: () => {},\n    getOnGoingProductsTasksToday: () => {}\n  }\n});\nexport default ProductSlice;", "map": {"version": 3, "names": ["get", "createSlice", "require", "ProductSlice", "name", "initialState", "products", "pagination", "product", "ongoingProductTaskToday", "reducers", "createProduct", "deleteProduct", "updateProduct", "getProductById", "getSuccessfullyProducts", "state", "action", "payload", "length", "data", "getSuccessfullyProductById", "console", "log", "getSuccessfullyOnGoingProductsTasksToday", "getProducts", "createTaskByAdmin", "createProductsTaskByUser", "getProductsByUser", "updateTask", "getTasks", "startTask", "stopTask", "pauseTask", "deleteTask", "getOnGoingProductsTasksToday"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/ProductSlice.js"], "sourcesContent": ["import { get } from \"lodash\";\r\nconst { createSlice } = require(\"@reduxjs/toolkit\");\r\nexport const ProductSlice = createSlice({\r\n    name:\"product\",\r\n    initialState: {\r\n        products:[],\r\n        pagination:{},\r\n        product :{},\r\n        ongoingProductTaskToday : {}\r\n    },\r\n    reducers: {\r\n        createProduct : () => {},\r\n        deleteProduct: () => {},\r\n        updateProduct: () => {},\r\n        getProductById: () => {},\r\n        getSuccessfullyProducts: (state,action) => {\r\n            if(action.payload.length === 0) {\r\n                state.pagination = {}\r\n                 state.products = []\r\n            } else {\r\n                state.products = action.payload.data\r\n                state.pagination = action.payload.pagination\r\n            }  \r\n        },\r\n        getSuccessfullyProductById: (state,action) => {\r\n            if(action.payload.length === 0) {\r\n                state.pagination = {}\r\n                 state.product = []\r\n            } else {\r\n                state.product = action.payload.data\r\n                state.pagination = action.payload.pagination\r\n            }  \r\n            console.log(\"State Product \",state.product,action.payload.data)\r\n        },\r\n        getSuccessfullyOnGoingProductsTasksToday: (state,action) => {\r\n            state.ongoingProductTaskToday = action.payload.data\r\n        },\r\n        getProducts: () => {},\r\n        createTaskByAdmin: () => {},\r\n        createProductsTaskByUser: () => {},\r\n        getProductsByUser: () => {},\r\n        updateTask: () => {},\r\n        getTasks: () => {},\r\n        startTask: () => {},\r\n        stopTask: () => {},\r\n        pauseTask: () => {},\r\n        deleteTask: () => {},\r\n        getOnGoingProductsTasksToday: () => {}\r\n    }\r\n})\r\nexport default ProductSlice"], "mappings": "AAAA,SAASA,GAAG,QAAQ,QAAQ;AAC5B,MAAM;EAAEC;AAAY,CAAC,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACnD,OAAO,MAAMC,YAAY,GAAGF,WAAW,CAAC;EACpCG,IAAI,EAAC,SAAS;EACdC,YAAY,EAAE;IACVC,QAAQ,EAAC,EAAE;IACXC,UAAU,EAAC,CAAC,CAAC;IACbC,OAAO,EAAE,CAAC,CAAC;IACXC,uBAAuB,EAAG,CAAC;EAC/B,CAAC;EACDC,QAAQ,EAAE;IACNC,aAAa,EAAGA,CAAA,KAAM,CAAC,CAAC;IACxBC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,cAAc,EAAEA,CAAA,KAAM,CAAC,CAAC;IACxBC,uBAAuB,EAAEA,CAACC,KAAK,EAACC,MAAM,KAAK;MACvC,IAAGA,MAAM,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5BH,KAAK,CAACT,UAAU,GAAG,CAAC,CAAC;QACpBS,KAAK,CAACV,QAAQ,GAAG,EAAE;MACxB,CAAC,MAAM;QACHU,KAAK,CAACV,QAAQ,GAAGW,MAAM,CAACC,OAAO,CAACE,IAAI;QACpCJ,KAAK,CAACT,UAAU,GAAGU,MAAM,CAACC,OAAO,CAACX,UAAU;MAChD;IACJ,CAAC;IACDc,0BAA0B,EAAEA,CAACL,KAAK,EAACC,MAAM,KAAK;MAC1C,IAAGA,MAAM,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5BH,KAAK,CAACT,UAAU,GAAG,CAAC,CAAC;QACpBS,KAAK,CAACR,OAAO,GAAG,EAAE;MACvB,CAAC,MAAM;QACHQ,KAAK,CAACR,OAAO,GAAGS,MAAM,CAACC,OAAO,CAACE,IAAI;QACnCJ,KAAK,CAACT,UAAU,GAAGU,MAAM,CAACC,OAAO,CAACX,UAAU;MAChD;MACAe,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAACP,KAAK,CAACR,OAAO,EAACS,MAAM,CAACC,OAAO,CAACE,IAAI,CAAC;IACnE,CAAC;IACDI,wCAAwC,EAAEA,CAACR,KAAK,EAACC,MAAM,KAAK;MACxDD,KAAK,CAACP,uBAAuB,GAAGQ,MAAM,CAACC,OAAO,CAACE,IAAI;IACvD,CAAC;IACDK,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,iBAAiB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC3BC,wBAAwB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAClCC,iBAAiB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC3BC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;IAClBC,SAAS,EAAEA,CAAA,KAAM,CAAC,CAAC;IACnBC,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;IAClBC,SAAS,EAAEA,CAAA,KAAM,CAAC,CAAC;IACnBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,4BAA4B,EAAEA,CAAA,KAAM,CAAC;EACzC;AACJ,CAAC,CAAC;AACF,eAAehC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}