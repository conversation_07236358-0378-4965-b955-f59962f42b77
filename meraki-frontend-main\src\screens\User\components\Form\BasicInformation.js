import React, {useEffect, useState} from "react";
import {
    Box,
    Button,
    Card,
    FormControl,
    Grid,
    InputBase,
    MenuItem,
    Typography,
    useTheme
} from "@mui/material";
import {Autocomplete} from "@mui/lab";
import COUNTRIES from "constants/countries";
import {useDispatch, useSelector} from "react-redux";
import {DepartmentSelector, DesignationSelector, GeneralSelector} from "selectors";
import {DepartmentActions, DesignationActions, GeneralActions, UserActions} from "slices/actions";
import {useFormik} from "formik";
import Input from "components/Input";
import SelectField from "components/SelectField";
import {toast} from "react-toastify";
import PropTypes from "prop-types";
import Can from "../../../../utils/can";
import {actions, features} from "../../../../constants/permission";
import {SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS} from "../../../../constants/workSchedule";

BasicInformation.propTypes = {
    user: PropTypes.object,
    form: PropTypes.object
};

export default function BasicInformation(props) {
    const { user, form } = props;
    const dispatch = useDispatch();
    const theme = useTheme();
    const departments = useSelector(DepartmentSelector.getDepartments());
    const designations = useSelector(DesignationSelector.getDesignations());
    const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));

    const [hoursFormat, setHoursFormat] = useState("decimal"); // "decimal" or "hhmm"

    const countries = COUNTRIES.map(item => ({
        id: item.id,
        name: item.name,
        phoneCode: item.phoneCode,
        flag: item.flag
    }));

    useEffect(() => {
        dispatch(DepartmentActions.getDepartments());
        dispatch(DesignationActions.getDesignations());
    }, []);

    useEffect(() => {
        if (success) {
            toast.success(`${success?.message ?? "Success"}`, {
                    position: "top-right",
                    autoClose: 3000,
                    closeOnClick: true,
                });

            dispatch(GeneralActions.removeSuccess(UserActions.updateUser.type));
        }
    }, [success]);

    // Helper function to convert between decimal and HH:MM formats
    const convertWorkHours = (value, toFormat) => {
        if (!value) {
            return "";
        }

        if (toFormat === "decimal") {
            // Convert from HH:MM to decimal
            if (value.includes(":")) {
                const [hours, minutes] = value.split(":");
                return Number(hours) + (Number(minutes) / 60);
            }
            return value;
        } else {
            // Convert from decimal to HH:MM
            const hours = Math.floor(Number(value));
            const minutes = Math.round((Number(value) - hours) * 60);
            return `${hours}:${minutes.toString().padStart(2, '0')}`;
        }
    };

    const formik = useFormik({
        initialValues: {
            name: user?.name ?? "",
            phoneCode: '',
            phoneNumber: "",
            country: user?.country ?? "",
            city: user?.city ?? "",
            address: user?.address ?? "",
            department: user?.department?._id ?? "",
            designation: user?.designation?._id ?? "",
            workHours: user?.workHours ?? "8.5", // Default to 8.5 hours or use stored value
            workSchedule: user?.workSchedule ?? DEFAULT_WORK_SCHEDULE,
        },
        enableReinitialize: true,
        validateOnChange: true,
        onSubmit: (values) => {
            handleSubmit(values);
        }
    });

    useEffect(() => {
        const code = formik.values.country?.phoneCode;
        const phone = formik.values.phone;

        formik.setFieldValue('phoneCode', code ?? '');
        formik.setFieldValue('phone', phone);
    }, [formik.values.country]);

    useEffect(() => {
        const phone = user.phone;
        const country = countries.find(e => e.name === user.country);

        if (country) {
            formik.setFieldValue('country', country);
        }

        if (phone && country) {
            const code = country.phoneCode;

            formik.setFieldValue('phoneCode', code ?? '');
            formik.setFieldValue('phoneNumber', phone.substring(code.length ?? 0));
        }
    }, [user]);

    const handleSubmit = (values) => {
        // Convert work hours to decimal format for storage
        const workHoursDecimal = hoursFormat === "decimal" ? values.workHours : convertWorkHours(values.workHours, "decimal");

        // Ensure workHours is a number, not a string
        const workHoursNumber = parseFloat(workHoursDecimal);

        const params = {
            id: user._id,
            ...values,
            ...form,
            phone: values.phoneCode + values.phoneNumber,
            workHours: workHoursNumber,
            workSchedule: values.workSchedule
        };

        dispatch(UserActions.updateUser(params));
    }

    const handleWorkHoursChange = (e) => {
        const { value } = e.target;
        formik.setFieldValue('workHours', value);
    };

    const toggleHoursFormat = () => {
        const newFormat = hoursFormat === "decimal" ? "hhmm" : "decimal";
        const convertedValue = convertWorkHours(formik.values.workHours, newFormat);
        setHoursFormat(newFormat);
        formik.setFieldValue('workHours', convertedValue);
    };

    const handleWorkScheduleChange = (field, value) => {
        formik.setFieldValue(`workSchedule.${field}`, value);
    };

    // Helper function to format date for input field
    const formatDateForInput = (dateValue) => {
        if (!dateValue) {
            return new Date().toISOString().split('T')[0]; // Current date
        }

        if (typeof dateValue === 'string') {
            return dateValue.split('T')[0];
        }

        return new Date(dateValue).toISOString().split('T')[0];
    };

    // Helper functions to get work schedule values with defaults
    const getWorkScheduleValue = (field, defaultValue) => {
        const workSchedule = formik.values.workSchedule;
        if (!workSchedule || workSchedule[field] === undefined) {
            return defaultValue;
        }
        return workSchedule[field];
    };

    return (
        <Card>
            <Typography variant='h5' sx={{ mb: 4 }}>Basic Information</Typography>
            <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={2}>
                    <Grid item lg={6} xs={12}>
                        <Input

                            label="Full Name"
                            name='name'
                            value={formik.values.name}
                            onChange={formik.handleChange}
                            error={formik.touched.name && Boolean(formik.errors.name)}
                           helpertext={formik.touched.name ? formik.errors.name : ""}/>
                    </Grid>
                    <Grid item lg={6} xs={12}>
                        <FormControl fullWidth>
                            <Typography variant='caption'>Country</Typography>
                            <Autocomplete
                                disablePortal

                                name='country'
                                options={countries}
                                value={formik.values.country}
                                onChange={(e, val) => {
                                    formik.setFieldValue('country', val);
                                }}
                                error={formik.touched.country && Boolean(formik.errors.country)}
                                helpertext={formik.touched.country ? formik.errors.country : ""}
                                getOptionLabel={(option) => option.name ?? ''}
                                renderOption={(props, option) => (
                                    <Box component="li" sx={{ '& > img': { mr: 2, flexShrink: 0 } }} {...props}>
                                        {option.flag} {option.name}
                                    </Box>
                                )}
                                renderInput={(params) => <InputBase {...params.InputProps} {...params} />}
                            />
                        </FormControl>
                    </Grid>
                    <Grid item lg={6} xs={12}>
                        <FormControl fullWidth>
                            <Typography variant='caption'>Phone Number</Typography>
                            <Box sx={{
                                display: 'flex',
                                gap: 1.5
                            }}>
                                <Box sx={{ width: 80 }}>
                                    <Input
                                        sx={{
                                            textAlign: 'center',
                                            '& .Mui-disabled': {
                                                fillColor: theme.palette.common.black
                                            }
                                        }}

                                        autoComplete='tel-country-code'
                                        name='phoneCode'
                                        startAdornment='+'
                                        type='number'
                                        value={formik.values.phoneCode}
                                        onChange={formik.handleChange}/>
                                </Box>
                                <Input

                                    name='phoneNumber'
                                    value={formik.values.phoneNumber}
                                    onChange={formik.handleChange}
                                    error={formik.touched.phoneNumber && Boolean(formik.errors.phoneNumber)}
                                    helpertext={formik.touched.phoneNumber ? formik.errors.phoneNumber : ""}/>
                            </Box>
                        </FormControl>
                    </Grid>
                    <Grid item lg={6} xs={12}>
                        <Input
                            label="City"
                            name='city'
                            value={formik.values.city}
                            onChange={formik.handleChange}
                            error={formik.touched.city && Boolean(formik.errors.city)}
                            helpertext={formik.touched.city ? formik.errors.city : ""}
                            />
                    </Grid>
                    <Grid item lg={6} xs={12}>
                        <Input
                            label="Address"
                            name='address'
                            value={formik.values.address}
                            onChange={formik.handleChange}
                            error={formik.touched.address && Boolean(formik.errors.address)}
                            helpertext={formik.touched.address ? formik.errors.address : ""}
                            />
                    </Grid>
                    <Grid item lg={6} xs={12}>
                        <SelectField
                            label="Department"
                            name='department'
                            value={formik.values.department}
                            onChange={formik.handleChange}
                            error={formik.touched.department && Boolean(formik.errors.department)}
                            helpertext={formik.touched.department ? formik.errors.department : ""}
                            >
                            {departments.map((item, index) => (
                                <MenuItem key={index} value={item._id}>
                                    {item.name}
                                </MenuItem>
                            ))}
                        </SelectField>
                    </Grid>
                    <Grid item lg={6} xs={12}>
                        <SelectField
                            label="Designation"
                            name='designation'
                            value={formik.values.designation}
                            onChange={formik.handleChange}
                            error={formik.touched.designation && Boolean(formik.errors.designation)}
                           helpertext={formik.touched.designation ? formik.errors.designation : ""}
                            >
                            {designations.map((item, index) => (
                                <MenuItem key={index} value={item._id}>
                                    {item.name}
                                </MenuItem>
                            ))}
                        </SelectField>
                    </Grid>

                    {/* New Work Hours Field */}
                    <Grid item lg={6} xs={12}>
                        <FormControl fullWidth>
                            <Typography variant='caption'>Daily Work Hours</Typography>
                            <Box sx={{
                                display: 'flex',
                                gap: 1.5,
                                alignItems: 'center'
                            }}>
                                <Input

                                    label=""
                                    name='workHours'
                                    value={formik.values.workHours}
                                    onChange={handleWorkHoursChange}
                                    placeholder={hoursFormat === "decimal" ? "e.g. 8.5" : "e.g. 8:30"}
                                    error={formik.touched.workHours && Boolean(formik.errors.workHours)}
                                    helpertext={formik.touched.workHours ? formik.errors.workHours : ""}/>

                                <Button
                                    size="small"
                                    variant="outlined"
                                    onClick={toggleHoursFormat}
                                    >
                                    {hoursFormat === "decimal" ? "Use HH:MM" : "Use Decimal"}
                                </Button>
                            </Box>
                            <Typography variant='caption' sx={{ mt: 1, color: 'text.secondary' }}>
                                {hoursFormat === "decimal" ? "Enter as decimal (e.g., 8.5 for 8 hours 30 minutes)" : "Enter as hours:minutes (e.g., 8:30)"}
                            </Typography>
                            <Typography variant='caption' sx={{ mt: 1, display: 'block', color: 'info.main' }}>
                                Note: This value determines your daily work requirement. Working less than half of this time will be marked as a half-day. Working more will count as overtime.
                            </Typography>
                        </FormControl>
                    </Grid>

                    {/* Work Schedule Fields */}
                    <Grid item lg={6} xs={12}>
                        <SelectField
                            label="Schedule Template"
                            name='workSchedule.scheduleTemplate'
                            value={getWorkScheduleValue('scheduleTemplate', 'day_shift')}
                            onChange={(e) => handleWorkScheduleChange('scheduleTemplate', e.target.value)}
                            >
                            {SCHEDULE_TEMPLATES.map((template) => (
                                <MenuItem key={template.value} value={template.value}>
                                    {template.label}
                                </MenuItem>
                            ))}
                        </SelectField>
                    </Grid>

                    <Grid item lg={6} xs={12}>
                        <Input
                            label="Shift Start"
                            name='workSchedule.shiftStart'
                            type="date"
                            value={formatDateForInput(formik.values.workSchedule?.shiftStart)}
                            onChange={(e) => handleWorkScheduleChange('shiftStart', e.target.value)}
                            />
                    </Grid>

                    <Grid item lg={6} xs={12}>
                        <Input
                            label="Shift End"
                            name='workSchedule.shiftEnd'
                            type="date"
                            value={formatDateForInput(formik.values.workSchedule?.shiftEnd)}
                            onChange={(e) => handleWorkScheduleChange('shiftEnd', e.target.value)}
                            />
                    </Grid>

                    <Grid item lg={6} xs={12}>
                        <SelectField
                            label="Start Time"
                            name='workSchedule.startTime'
                            value={getWorkScheduleValue('startTime', '09:00')}
                            onChange={(e) => handleWorkScheduleChange('startTime', e.target.value)}
                            >
                            {TIME_OPTIONS.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                </MenuItem>
                            ))}
                        </SelectField>
                    </Grid>

                    <Grid item lg={6} xs={12}>
                        <SelectField
                            label="End Time"
                            name='workSchedule.endTime'
                            value={getWorkScheduleValue('endTime', '17:00')}
                            onChange={(e) => handleWorkScheduleChange('endTime', e.target.value)}
                            >
                            {TIME_OPTIONS.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                    {option.label}
                                </MenuItem>
                            ))}
                        </SelectField>
                    </Grid>

                    <Grid item lg={6} xs={12}>
                        <Input
                            label="Minimum Hours"
                            name='workSchedule.minimumHours'
                            type="number"
                            step="0.5"
                            value={getWorkScheduleValue('minimumHours', 8.0)}
                            onChange={(e) => handleWorkScheduleChange('minimumHours', parseFloat(e.target.value))}
                            />
                    </Grid>

                    {Can(actions.readAll, features.user) && (
                        <Grid sx={{ mt: 3 }} item container justifyContent="flex-end">
                            <Button
                                type="submit"
                                color="primary"
                                variant="contained">
                                Submit
                            </Button>
                        </Grid>
                    )}
                </Grid>
            </form>
        </Card>
    )
}