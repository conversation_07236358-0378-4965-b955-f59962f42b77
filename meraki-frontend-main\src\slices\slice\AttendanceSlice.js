import { createSlice } from "@reduxjs/toolkit";

export const AttendanceSlice = createSlice({
    name: "Attendance",
    initialState: {
        attendances: [],
        pagination: {},
        attendance: {},
    },
    reducers: {
        getAttendances: () => { 
        },
        getAttendancesSuccess: (state, action) => {

            state.attendance = {};
            state.attendances = action.payload.data;
            state.pagination = action.payload.pagination;
            console.log("📊 Attendance data received:", action.payload.data?.length, "records");
            console.log("📊 First few records:", action.payload.data?.slice(0, 3));
        },
        getAttendanceById: () => {},
        getAttendanceByIdSuccess: (state, action) => {
            state.attendance = action.payload
        },
        createAttendance: () => {},
        updateAttendance: () => {},
        deleteAttendance: () => {},
        createLunchBreak: () => {},
        updateLunchBreak: () => {},
        getAttendancesByMonth: () => {},
        clearAttendances: (state) => {
            state.attendances = [];
            state.attendance = {};
            state.pagination = {};
        }
    }
});

export default AttendanceSlice;