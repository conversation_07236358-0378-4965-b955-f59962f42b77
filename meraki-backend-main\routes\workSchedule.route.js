'use strict';

const express = require('express');
const router = express.Router();
const multer = require('multer')().none();
const workSchedule = require('../controllers/workSchedule.controller');

module.exports = (app) => {
    // User work schedule management
    router.get("/user/:userId", workSchedule.getUserWorkSchedule);
    router.patch("/user/:userId", multer, workSchedule.updateUserWorkSchedule);
    
    // Work schedule templates
    router.get("/templates", workSchedule.getWorkScheduleTemplates);
    router.post("/templates", multer, workSchedule.createWorkScheduleTemplate);
    
    // Work schedule entries (for specific dates/times)
    router.post("/entry", multer, workSchedule.createWorkScheduleEntry);
    router.get("/entry/:userId/:date", workSchedule.getWorkScheduleEntry);
    router.patch("/entry/:entryId", multer, workSchedule.updateWorkScheduleEntry);
    router.delete("/entry/:entryId", workSchedule.deleteWorkScheduleEntry);
    
    // Get work schedule entries for date range
    router.get("/entries/:userId/:startDate/:endDate", workSchedule.getWorkScheduleEntries);
    
    app.use("/api/work-schedule", router);
};
