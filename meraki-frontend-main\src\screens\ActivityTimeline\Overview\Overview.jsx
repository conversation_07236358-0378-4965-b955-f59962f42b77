import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Box, Button } from '@mui/material';
import MonthlyWorkReport from './component/MonthlyWorkReport';
import DayWorkReport from './component/DayWorkReport';
import WeekWorkReport from './component/WeekWorkReport';
import DayPicker from './TimePickers/DayPicker';
import WeeklyPicker from './TimePickers/WeeklyPicker';
import MonthPicker from './TimePickers/MonthPicker';
import { useDispatch } from 'react-redux';
import { ActivityActions } from '../../../slices/actions';
import dayjs from 'dayjs';

function Overview() {
  const dispatch = useDispatch();
  const [viewOption, setViewOption] = useState('Day');

  // Independent date states for each view to prevent circular dependencies
  const [dayDateRange, setDayDateRange] = useState({
    startDate: dayjs().format('YYYY-MM-DD'),
    endDate: dayjs().format('YYYY-MM-DD')
  });

  const [weekDateRange, setWeekDateRange] = useState({
    startDate: dayjs().startOf('week').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('week').format('YYYY-MM-DD')
  });

  const [monthDateRange, setMonthDateRange] = useState({
    startDate: dayjs().startOf('month').format('YYYY-MM-DD'),
    endDate: dayjs().endOf('month').format('YYYY-MM-DD')
  });

  // Get current date range based on active view
  const getCurrentDateRange = () => {
    switch(viewOption) {
      case 'Day': return dayDateRange;
      case 'Week': return weekDateRange;
      case 'Month': return monthDateRange;
      default: return dayDateRange;
    }
  };

  // Handle date changes from pickers - each picker updates its own state
  const handleDayDateChange = useCallback((newDateRange) => {
    setDayDateRange(newDateRange);
  }, []);

  const handleWeekDateChange = useCallback((newDateRange) => {
    setWeekDateRange(newDateRange);
  }, []);

  const handleMonthDateChange = useCallback((newDateRange) => {
    setMonthDateRange(newDateRange);
  }, []);

  // Reference to track the last fetched data to prevent duplicate API calls
  const lastFetchedRef = useRef({ dateRange: null, view: null });

  // Fetch activity data when date range or view changes
  useEffect(() => {
    const currentDateRange = getCurrentDateRange();
    const currentView = viewOption.toLowerCase();

    // Create a unique key for the current state
    const currentKey = `${currentDateRange.startDate}-${currentDateRange.endDate}-${currentView}`;
    const lastKey = lastFetchedRef.current.dateRange && lastFetchedRef.current.view ? `${lastFetchedRef.current.dateRange.startDate}-${lastFetchedRef.current.dateRange.endDate}-${lastFetchedRef.current.view}` : null;

    // Fetch data when view or date range changes
    if (currentKey !== lastKey && currentDateRange.startDate && currentDateRange.endDate) {
      lastFetchedRef.current = { dateRange: currentDateRange, view: currentView };

      console.log("Overview - Dispatching getUserActivity with:", {
        startDate: currentDateRange.startDate,
        endDate: currentDateRange.endDate,
        view: currentView,
        currentDateRange
      });

      dispatch(ActivityActions.getUserActivity({
        startDate: currentDateRange.startDate,
        endDate: currentDateRange.endDate,
        view: currentView
      }));
    }
  }, [dayDateRange, weekDateRange, monthDateRange, viewOption, dispatch]);

  return (
    <>
      <h1>Overview</h1>

      {/* View options and date picker */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3,
          position: 'relative',
        }}
      >
        {/* Day/Week/Month tabs */}
        <Box
          sx={{
            display: 'flex',
            borderRadius: '4px',
            overflow: 'hidden',
            border: '1px solid #e0e0e0',
          }}
        >
          {['Day', 'Week', 'Month'].map((option) => (
            <Button
              key={option}
              onClick={() => setViewOption(option)}
              sx={{
                bgcolor: viewOption === option ? 'primary.main' : 'transparent',
                color: viewOption === option ? 'white' : 'text.primary',
                borderRadius: 0,
                '&:hover': {
                  bgcolor: viewOption === option ? 'primary.dark' : 'rgba(0, 0, 0, 0.04)',
                },
              }}
            >
              {option}
            </Button>
          ))}
        </Box>

        {/* Date Pickers - Each picker manages its own independent state */}
        {viewOption === 'Day' && (
          <DayPicker
            onChange={handleDayDateChange}
            startDate={dayDateRange.startDate}
            endDate={dayDateRange.endDate}
          />
        )}
        {viewOption === 'Week' && (
          <WeeklyPicker
            onChange={handleWeekDateChange}
            startDate={weekDateRange.startDate}
          />
        )}
        {viewOption === 'Month' && (
          <MonthPicker
            onChange={handleMonthDateChange}
            selectedMonth={monthDateRange.startDate}
          />
        )}
      </Box>

      {/* Conditionally render view with appropriate date range */}
      {viewOption === 'Day' && <DayWorkReport dateRange={dayDateRange} />}
      {viewOption === 'Week' && <WeekWorkReport dateRange={weekDateRange} />}
      {viewOption === 'Month' && <MonthlyWorkReport dateRange={monthDateRange} />}
    </>
  );
}

export default Overview;
