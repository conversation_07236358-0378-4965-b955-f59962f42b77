import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>ield,
  Button,
  MenuItem,
  Typography,
  Box,
  Grid,
  Paper,
  IconButton,
  Dialog,
  Select,
  Chip,
  OutlinedInput,
  InputLabel,
} from "@mui/material";
import SendIcon from "@mui/icons-material/Send";
import { Close } from "@mui/icons-material";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";
import { UserSelector } from "selectors";
import { ProductActions } from "slices/actions";

const priorities = [
  { value: "Low", label: "Low" },
  { value: "Medium", label: "Medium" }, // <-- Updated from 'Intermediate'
  { value: "High", label: "High" },
  { value: "Critical", label: "Critical" }, // <-- Add Critical if supported
];

const billingOptions = [
  { value: "Billable", label: "Billable" },
  { value: "Non-Billable", label: "Non-Billable" },
];

const taskTypes = [
  { value: "Normal", label: "Normal" },
  { value: "Bug", label: "Bug" },
  { value: "Feature", label: "Feature" },
];

function TaskInfoComponent({ taskInfoController, data, productId }) {
  const users = useSelector(UserSelector.getUsers());
  const dispatch = useDispatch();

  const [priority, setPriority] = useState(data.priority || "Intermediate");
  const [advanceOption, setAdvanceOption] = useState(false);
  const [assigneeName, setAssigneeName] = useState([]);
  const [taskTitle, setTaskTitle] = useState(data.taskTitle || "");
  const [assignedHour, setAssignedHour] = useState(data.assignedHour || "00");
  const [assignedMinute, setAssignedMinute] = useState(
    data.assignedMinute || "00"
  );
  const [billingStatus, setBillingStatus] = useState(
    data.billingStatus || "Non-Billable"
  );
  const [taskType, setTaskType] = useState(data.taskType || "Normal");

  useEffect(() => {
    if (data?.assignee?.length > 0 && users?.length > 0) {
      const names = data.assignee.map(
        (id) => users.find((user) => user._id === id)?.name
      );
      setAssigneeName(names);
    }
    console.log("Users Gettting  ", users);
  }, [data, users]);

  function getUserEmail(id) {
    return users.find((user) => user._id === id)?.email || "";
  }

  function updateTask() {
    dispatch(
      ProductActions.updateTask({
        productid: productId,
        taskid: data._id,
        body: {
          taskTitle,
          priority,
          assignedHour,
          assignedMinute,
          billingStatus,
          taskType,
          // Add other fields here as needed
        },
      })
    );
  }

  const handleAssigneeChange = (event) => {
    const {
      target: { value },
    } = event;

    // Ensure value is always an array
    setAssigneeName(typeof value === "string" ? value.split(",") : value);
  };

  return (
    <Dialog open={true} fullWidth maxWidth="sm">
      <Paper elevation={3} sx={{ padding: 3 }}>
        <Box display="flex" justifyContent="flex-end">
          <IconButton onClick={taskInfoController}>
            <Close />
          </IconButton>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom>
              Task Info
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <TextField
              required
              fullWidth
              label="Task Title"
              value={taskTitle}
              onChange={(e) => setTaskTitle(e.target.value)}
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Reporter"
              value={getUserEmail(data.reporter)}
              InputProps={{ readOnly: true }}
            />
          </Grid>

          <Grid item xs={12}>
            <InputLabel id="assignee-label">Assignee</InputLabel>
            <Select
              labelId="assignee-label"
              multiple
              value={assigneeName}
              onChange={handleAssigneeChange}
              input={<OutlinedInput label="Assignee" />}
              style={{overflow:"scroll"}}
              renderValue={(selected) => (
                <div
                  style={{
                    display: "flex",
                    flexWrap: "wrap",
                    gap: "4px",
                    maxHeight: "80px", // Control the visible height
                    overflowY: "auto", // Enable scroll if content overflows
                  }}
                >
                  {selected.map((value) => (
                    <Chip key={value} label={value} style={{ margin: 2 }} />
                  ))}
                </div>
              )}
            >
              {users.map((user) => (
                <MenuItem key={user._id} value={user.name}>
                  {user.name}
                </MenuItem>
              ))}
            </Select>
          </Grid>

          <Grid item xs={12}>
            <TextField
              select
              fullWidth
              label="Priority"
              value={priority}
              onChange={(e) => setPriority(e.target.value)}
            >
              {priorities.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Assigned Time (HH)"
              value={assignedHour}
              onChange={(e) => setAssignedHour(e.target.value)}
            />
          </Grid>

          <Grid item xs={6}>
            <TextField
              fullWidth
              label="Assigned Time (MM)"
              value={assignedMinute}
              onChange={(e) => setAssignedMinute(e.target.value)}
            />
          </Grid>

          {advanceOption ? (
            <>
              <Grid item xs={3}>
                <TextField fullWidth label="Start Time (MM)" />
              </Grid>
              <Grid item xs={3}>
                <TextField fullWidth label="End Time (MM)" />
              </Grid>

              <Grid item xs={3}>
                <TextField
                  select
                  fullWidth
                  label="Billing Status"
                  value={billingStatus}
                  onChange={(e) => setBillingStatus(e.target.value)}
                >
                  {billingOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={3}>
                <TextField
                  select
                  fullWidth
                  label="Task Type"
                  value={taskType}
                  onChange={(e) => setTaskType(e.target.value)}
                >
                  {taskTypes.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </TextField>
              </Grid>

              <Grid item xs={12}>
                <TextField fullWidth label="Additional Notes" />
              </Grid>

              <Grid item xs={12}>
                <TextField type="file" fullWidth />
              </Grid>

              <Grid item xs={12}>
                <Button
                  variant="contained"
                  fullWidth
                  onClick={() => setAdvanceOption(false)}
                >
                  Hide Options -
                </Button>
              </Grid>
            </>
          ) : (
            <Grid item xs={12}>
              <Button
                variant="contained"
                fullWidth
                onClick={() => setAdvanceOption(true)}
              >
                Advanced Options +
              </Button>
            </Grid>
          )}

          <Grid item xs={6}>
            <Button variant="outlined" fullWidth onClick={taskInfoController}>
              Close
            </Button>
          </Grid>

          <Grid item xs={6}>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              onClick={updateTask}
            >
              Update
            </Button>
          </Grid>
        </Grid>

        <Box mt={3}>
          <Typography variant="h6" gutterBottom>
            Comments
          </Typography>
          <TextField
            fullWidth
            label="Add a comment"
            InputProps={{
              endAdornment: (
                <IconButton>
                  <SendIcon />
                </IconButton>
              ),
            }}
          />
        </Box>
      </Paper>
    </Dialog>
  );
}

TaskInfoComponent.propTypes = {
  taskInfoController: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  productId: PropTypes.string.isRequired,
};

export default TaskInfoComponent;
