'use strict';

const LeaveService = require("../services/leave.service");
const UserController = require("../controllers/user.controller")

const mongoose = require("mongoose");

exports.fetchAllLeaves = async (req, res) => {
    try {
        const { keyword, sort, page, limit, user, department, designation } = req.query;
        const queries = {
            page: page ? parseInt(page) : 1,
            limit: limit ? parseInt(limit) : 20,
            sort: { createdAt: -1 }, // Fixed: sort by createdAt instead of name
            query: []
        };

        if (keyword) {
            queries.query.push({ 'userName': { '$regex': '.*' + keyword + '.*', '$options': 'i' } });
        }
        if (sort) {
            const field = sort.split(",");
            queries.sort = {
                [field[0]]: parseInt(field[1])
            };
        }

        if (user) {
            queries.query.push({ 'user._id': mongoose.Types.ObjectId(user) });
        }

        if (department) {
            queries.query.push({ 'user.department': mongoose.Types.ObjectId(department) });
        }

        if (designation) {
            queries.query.push({ 'user.designation': mongoose.Types.ObjectId(designation) });
        }

        const results = await LeaveService.getLeavesByQuery(queries);
        return res.status(200).send(results);
    } catch (error) {
        console.error("Error fetching leaves:", error);
        return res.status(500).send({
            message: "Failed to fetch leaves.",
            error: error.message
        });
    }
}

exports.createLeave = async (req, res) => {
    try {
        const { body } = req;
        
        // Validate required fields
        if (!body.user || !body.start || !body.end) {
            return res.status(400).send({
                message: "Missing required fields: user, start, end"
            });
        }

        const result = await LeaveService.createLeave(body);
        console.log("Create Leave:", result);
        
        if (!result || result.length === 0) {
            return res.status(500).send({
                message: "Failed to create leave."
            });
        }

        return res.status(201).send({
            message: "Leave created successfully.",
            data: result
        });
    } catch (error) {
        console.error("Error creating leave:", error);
        return res.status(500).send({
            message: "Failed to create leave.",
            error: error.message
        });
    }
}

exports.fetchLeaveById = async (req, res) => {
    const { params } = req;

    const result = await LeaveService.getLeaveById(params.id);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send(result);
}

exports.fetchLoggedInLeave = async (req, res) => {
    const { Leave } = req;

    const result = await LeaveService.getLeaveById(Leave);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send(result);
}

exports.updateLeave = async (req, res) => {
    try {
        const { params, body } = req;
        console.log("Update Leave:", body);
        
        // Update leave status and admin approval flag
        const result = await LeaveService.updateLeave(params.id, body);
        
        // Only update user leave balance if leave is approved (status = 1) and not already processed
        if (body.status === 1 && body.updatedByAdmin && result) {
            try {
                await UserController.updateUserLeave(result.user, body);
            } catch (error) {
                console.error("Error updating user leave balance:", error.message);
                // Continue with leave update even if user balance update fails
            }
        }
        
        if (!result) {
            return res.status(404).send({
                message: "Leave not found or update failed."
            });
        }

        return res.status(200).send({
            message: "Leave updated successfully.",
            data: result
        });
    } catch (error) {
        console.error("Error in updateLeave:", error);
        return res.status(500).send({
            message: "Failed to update leave.",
            error: error.message
        });
    }
}

exports.deleteLeave = async (req, res) => {
    const { params } = req;

    const result = await LeaveService.deleteLeave(params.id);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send({
        message: "Successfully proceed data."
    });
}
