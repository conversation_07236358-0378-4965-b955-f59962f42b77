import React, { useEffect, useState } from "react";
import { Box } from "@mui/material";
import PageTitle from "components/PageTitle";
import Widgets from "./components/Widgets";
import UserLeaveInfo from "./components/UserLeaveInfo";
import { useDispatch, useSelector } from "react-redux";
import { UserSelector, AttendanceSelector } from "selectors";
import { getTodayData } from "utils/convertion";
import { AttendanceActions } from "slices/actions";

export default function UserDashboard() {
    const dispatch = useDispatch();
    const profile = useSelector(UserSelector.profile()) || {};
    const attendances = useSelector(AttendanceSelector.getAttendances()) || [];

    const [widget, setWidget] = useState({
        employee: 0,
        attendance: 0,
        expenses: 0
    });

    useEffect(() => {
        if (profile && profile._id) {
            const today = new Date();
            const formattedDate = today.toISOString().split("T")[0];

            dispatch(AttendanceActions.getAttendances({
                user: profile._id,
                date: formattedDate
            }));
        }
    }, [dispatch, profile]);

    useEffect(() => {
        if (profile && profile._id && attendances) {
            // Filter attendances for current user only
            const userAttendances = attendances.filter(item =>
                item.user === profile._id || item.user?._id === profile._id
            );

            const data = userAttendances?.map(item => ({ ...item, date: item.checkIn }));
            const todayAttendance = getTodayData(data);

            setWidget(prev => ({
                ...prev,
                attendance: todayAttendance?.length ?? 0
            }));
        }
    }, [attendances, profile]);

    return (
        <Box>
            <PageTitle title="My Dashboard" />
            <Widgets
                countUser={0}
                widget={{
                    attendance: widget.attendance,
                    expenses: 0,
                    employee: 0
                }}/>
            <UserLeaveInfo />
        </Box>
    );
}