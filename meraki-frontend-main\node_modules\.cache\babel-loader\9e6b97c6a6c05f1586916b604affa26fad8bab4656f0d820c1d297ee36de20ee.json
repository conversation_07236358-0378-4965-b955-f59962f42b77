{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const UserSlice = createSlice({\n  name: \"user\",\n  initialState: {\n    profile: null,\n    users: [],\n    pagination: {},\n    user: {}\n  },\n  reducers: {\n    profileUser: () => {},\n    profileUserSuccess: (state, action) => {\n      state.profile = action.payload;\n    },\n    resetProfile: state => {\n      state.profile = null;\n    },\n    getUsers: () => {},\n    getUsersSuccess: (state, action) => {\n      state.user = {};\n      state.users = action.payload.data;\n      state.pagination = action.payload.pagination;\n    },\n    getUserById: () => {},\n    getUserByIdSuccess: (state, action) => {\n      state.user = action.payload;\n    },\n    createUser: () => {},\n    updateUser: () => {},\n    deleteUser: () => {},\n    updateUserLeaves: () => {},\n    updateUserLeavesDefault: () => {}\n  }\n});\nexport default UserSlice;", "map": {"version": 3, "names": ["createSlice", "UserSlice", "name", "initialState", "profile", "users", "pagination", "user", "reducers", "profileUser", "profileUserSuccess", "state", "action", "payload", "resetProfile", "getUsers", "getUsersSuccess", "data", "getUserById", "getUserByIdSuccess", "createUser", "updateUser", "deleteUser", "updateUserLeaves", "updateUserLeavesDefault"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/UserSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\nexport const UserSlice = createSlice({\r\n    name: \"user\",\r\n    initialState: {\r\n        profile: null,\r\n        users: [],\r\n        pagination: {},\r\n        user: {},\r\n    },\r\n    reducers: {\r\n        profileUser: () => {},\r\n        profileUserSuccess: (state, action) => {\r\n            state.profile = action.payload;\r\n        },\r\n        resetProfile: (state) => {\r\n            state.profile = null;\r\n        },\r\n        getUsers: () => {},\r\n        getUsersSuccess: (state, action) => {\r\n            state.user = {};\r\n\r\n            state.users = action.payload.data;\r\n            state.pagination = action.payload.pagination;\r\n        },\r\n        getUserById: () => {},\r\n        getUserByIdSuccess: (state, action) => {\r\n            state.user = action.payload;\r\n        },\r\n        createUser: () => {},\r\n        updateUser: () => {},\r\n        deleteUser: () => {},\r\n        updateUserLeaves: () => {},\r\n        updateUserLeavesDefault: () => {}\r\n    }\r\n});\r\nexport default UserSlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,MAAMC,SAAS,GAAGD,WAAW,CAAC;EACjCE,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE;IACVC,OAAO,EAAE,IAAI;IACbC,KAAK,EAAE,EAAE;IACTC,UAAU,EAAE,CAAC,CAAC;IACdC,IAAI,EAAE,CAAC;EACX,CAAC;EACDC,QAAQ,EAAE;IACNC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,kBAAkB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACnCD,KAAK,CAACP,OAAO,GAAGQ,MAAM,CAACC,OAAO;IAClC,CAAC;IACDC,YAAY,EAAGH,KAAK,IAAK;MACrBA,KAAK,CAACP,OAAO,GAAG,IAAI;IACxB,CAAC;IACDW,QAAQ,EAAEA,CAAA,KAAM,CAAC,CAAC;IAClBC,eAAe,EAAEA,CAACL,KAAK,EAAEC,MAAM,KAAK;MAChCD,KAAK,CAACJ,IAAI,GAAG,CAAC,CAAC;MAEfI,KAAK,CAACN,KAAK,GAAGO,MAAM,CAACC,OAAO,CAACI,IAAI;MACjCN,KAAK,CAACL,UAAU,GAAGM,MAAM,CAACC,OAAO,CAACP,UAAU;IAChD,CAAC;IACDY,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,kBAAkB,EAAEA,CAACR,KAAK,EAAEC,MAAM,KAAK;MACnCD,KAAK,CAACJ,IAAI,GAAGK,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDO,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC1BC,uBAAuB,EAAEA,CAAA,KAAM,CAAC;EACpC;AACJ,CAAC,CAAC;AACF,eAAevB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}