{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\WorkSchedule\\\\Components\\\\DayWorkSchedule.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Avatar, Box, Typography, Button } from '@mui/material';\nimport AddIcon from '@mui/icons-material/Add';\nimport dayjs from 'dayjs';\nimport PropTypes from 'prop-types';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { UserActions } from 'slices/actions';\nimport { UserSelector } from 'selectors';\nimport WorkScheduleForm from './WorkScheduleForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst hours = Array.from({\n  length: 24\n}, (_, i) => `${i.toString().padStart(2, '0')}:00`);\nconst SLOT_WIDTH = 60;\nconst USER_WIDTH = 200;\nconst ROW_HEIGHT = 60;\nconst DayWorkSchedule = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const users = useSelector(UserSelector.getUsers());\n  const [open, setOpen] = useState(false);\n  const [selectedUser, setSelectedUser] = useState(null);\n  const currentDate = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? dayjs(dateRange.startDate) : dayjs();\n  useEffect(() => {\n    console.log(\"🔄 DayWorkSchedule: Fetching users data\");\n    dispatch(UserActions.getUsers());\n  }, [dispatch]);\n\n  // Debug: Log users data to see what's being returned\n  useEffect(() => {\n    var _users$;\n    console.log(\"🔍 DayWorkSchedule: Users data received:\", {\n      usersCount: (users === null || users === void 0 ? void 0 : users.length) || 0,\n      sampleUser: users === null || users === void 0 ? void 0 : users[0],\n      sampleWorkSchedule: users === null || users === void 0 ? void 0 : (_users$ = users[0]) === null || _users$ === void 0 ? void 0 : _users$.workSchedule\n    });\n  }, [users]);\n  const handleAddSchedule = (user = null) => {\n    setSelectedUser(user);\n    setOpen(true);\n  };\n  const handleClose = () => {\n    setOpen(false);\n    setSelectedUser(null);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h5\",\n        children: currentDate.format(\"dddd, MMMM D, YYYY\")\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        size: \"small\",\n        startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 22\n        }, this),\n        onClick: () => handleAddSchedule(),\n        sx: {\n          bgcolor: '#1976d2'\n        },\n        children: \"Add Schedule\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        overflowX: 'auto',\n        overflowY: 'hidden',\n        border: '1px solid #ccc',\n        borderRadius: 1,\n        backgroundColor: '#fff',\n        pb: 2,\n        WebkitOverflowScrolling: 'touch'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          minWidth: `${USER_WIDTH + hours.length * SLOT_WIDTH}px`,\n          width: `${USER_WIDTH + hours.length * SLOT_WIDTH}px` //\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            position: 'sticky',\n            top: 0,\n            zIndex: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: USER_WIDTH,\n              height: ROW_HEIGHT,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontWeight: 600,\n              fontSize: 13,\n              borderRight: '1px solid #ccc',\n              borderBottom: '1px solid #ccc',\n              backgroundColor: '#f0f0f0',\n              position: 'sticky',\n              left: 0,\n              zIndex: 3\n            },\n            children: \"User\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), hours.map((hour, idx) => /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: SLOT_WIDTH,\n              height: ROW_HEIGHT,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              fontSize: 12,\n              fontWeight: 600,\n              borderRight: '1px solid #ccc',\n              borderBottom: '1px solid #ccc',\n              backgroundColor: '#f0f0f0'\n            },\n            children: hour\n          }, idx, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            maxHeight: 400,\n            overflowY: 'auto'\n          },\n          children: users.map((user, uIdx) => {\n            var _user$name, _user$name$, _user$designation;\n            // Check if user has work schedule\n            const work = user.workSchedule;\n            const hasSchedule = (work === null || work === void 0 ? void 0 : work.startTime) && (work === null || work === void 0 ? void 0 : work.endTime);\n            const currentDateStr = currentDate.format('YYYY-MM-DD');\n\n            // Check if current date falls within the schedule period\n            let isScheduledDate = false;\n            if (hasSchedule) {\n              if (work.shiftStart && work.shiftEnd) {\n                // Convert ISO dates to YYYY-MM-DD format for comparison\n                const shiftStartDate = dayjs(work.shiftStart).format('YYYY-MM-DD');\n                const shiftEndDate = dayjs(work.shiftEnd).format('YYYY-MM-DD');\n\n                // Check if current date is within shift period\n                isScheduledDate = currentDateStr >= shiftStartDate && currentDateStr <= shiftEndDate;\n                console.log(`🔍 DayWorkSchedule: Date comparison for ${user.name}:`, {\n                  currentDateStr,\n                  shiftStartDate,\n                  shiftEndDate,\n                  isScheduledDate\n                });\n              } else {\n                // If no shift dates specified, assume schedule applies to current date\n                isScheduledDate = true;\n              }\n            }\n            const startHour = isScheduledDate ? parseInt(work.startTime.split(':')[0], 10) : null;\n            const endHour = isScheduledDate ? parseInt(work.endTime.split(':')[0], 10) : null;\n\n            // Check if this is a night shift (end time is before start time)\n            const isNightShift = isScheduledDate && endHour < startHour;\n\n            // Debug: Log time calculations\n            if (isScheduledDate) {\n              console.log(`🕐 DayWorkSchedule: Time calculation for ${user.name}:`, {\n                startTime: work.startTime,\n                endTime: work.endTime,\n                startHour,\n                endHour,\n                isNightShift,\n                scheduleTemplate: work.scheduleTemplate\n              });\n            }\n            return /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  width: USER_WIDTH,\n                  minHeight: ROW_HEIGHT,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1,\n                  px: 2,\n                  backgroundColor: '#fff',\n                  borderRight: '1px solid #eee',\n                  borderBottom: '1px solid #eee',\n                  position: 'sticky',\n                  left: 0,\n                  zIndex: 1,\n                  cursor: 'pointer'\n                },\n                onClick: () => handleAddSchedule(user),\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  sx: {\n                    width: 32,\n                    height: 32\n                  },\n                  children: ((_user$name = user.name) === null || _user$name === void 0 ? void 0 : (_user$name$ = _user$name[0]) === null || _user$name$ === void 0 ? void 0 : _user$name$.toUpperCase()) || 'U'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    fontWeight: 600,\n                    fontSize: 13,\n                    children: user.name || 'Unknown User'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 200,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: ((_user$designation = user.designation) === null || _user$designation === void 0 ? void 0 : _user$designation.name) || user.role || 'No Role'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this), hours.map((hour, hIdx) => {\n                const hourNum = parseInt(hour.split(':')[0], 10);\n\n                // Handle both day shift and night shift\n                let isWorkingHour = false;\n                if (isScheduledDate) {\n                  if (isNightShift) {\n                    // Night shift: working hours are from startHour to 24 OR from 0 to endHour\n                    isWorkingHour = hourNum >= startHour || hourNum < endHour;\n                  } else {\n                    // Day shift: working hours are from startHour to endHour\n                    isWorkingHour = hourNum >= startHour && hourNum < endHour;\n                  }\n                }\n                return /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    width: SLOT_WIDTH,\n                    height: ROW_HEIGHT,\n                    borderRight: '1px solid #eee',\n                    borderBottom: '1px solid #eee',\n                    position: 'relative',\n                    backgroundColor: isWorkingHour ? '#4caf50' : '#fafafa',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center'\n                  },\n                  children: isWorkingHour && /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    sx: {\n                      fontSize: '8px',\n                      color: 'white',\n                      fontWeight: 600,\n                      textAlign: 'center',\n                      lineHeight: 1.2\n                    },\n                    children: [work.scheduleTemplate === 'night_shift' ? 'Night' : 'Day', /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 252,\n                      columnNumber: 29\n                    }, this), work.startTime, \"-\", work.endTime]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 27\n                  }, this)\n                }, hIdx, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 226,\n                  columnNumber: 23\n                }, this);\n              })]\n            }, uIdx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(WorkScheduleForm, {\n      open: open,\n      onClose: handleClose,\n      selectedUser: selectedUser,\n      selectedDate: currentDate.format('YYYY-MM-DD')\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 267,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(DayWorkSchedule, \"/13fiwaTeHPYfijFJQzSWM9AVY0=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = DayWorkSchedule;\nDayWorkSchedule.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default DayWorkSchedule;\nvar _c;\n$RefreshReg$(_c, \"DayWorkSchedule\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Avatar", "Box", "Typography", "<PERSON><PERSON>", "AddIcon", "dayjs", "PropTypes", "useDispatch", "useSelector", "UserActions", "UserSelector", "WorkScheduleForm", "jsxDEV", "_jsxDEV", "hours", "Array", "from", "length", "_", "i", "toString", "padStart", "SLOT_WIDTH", "USER_WIDTH", "ROW_HEIGHT", "DayWorkSchedule", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "users", "getUsers", "open", "<PERSON><PERSON><PERSON>", "selected<PERSON>ser", "setSelectedUser", "currentDate", "startDate", "console", "log", "_users$", "usersCount", "sampleUser", "sampleWorkSchedule", "workSchedule", "handleAddSchedule", "user", "handleClose", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "format", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "size", "startIcon", "onClick", "bgcolor", "width", "overflowX", "overflowY", "border", "borderRadius", "backgroundColor", "pb", "WebkitOverflowScrolling", "min<PERSON><PERSON><PERSON>", "position", "top", "zIndex", "height", "fontWeight", "fontSize", "borderRight", "borderBottom", "left", "map", "hour", "idx", "maxHeight", "uIdx", "_user$name", "_user$name$", "_user$designation", "work", "hasSchedule", "startTime", "endTime", "currentDateStr", "isScheduledDate", "shiftStart", "shiftEnd", "shiftStartDate", "shiftEndDate", "name", "startHour", "parseInt", "split", "endHour", "isNightShift", "scheduleTemplate", "minHeight", "gap", "px", "cursor", "toUpperCase", "color", "designation", "role", "hIdx", "hourNum", "isWorkingHour", "textAlign", "lineHeight", "onClose", "selectedDate", "_c", "propTypes", "shape", "string", "endDate", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/WorkSchedule/Components/DayWorkSchedule.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Avatar,\r\n  Box,\r\n  Typography,\r\n  Button\r\n} from '@mui/material';\r\nimport AddIcon from '@mui/icons-material/Add';\r\nimport dayjs from 'dayjs';\r\nimport PropTypes from 'prop-types';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { UserActions } from 'slices/actions';\r\nimport { UserSelector } from 'selectors';\r\nimport WorkScheduleForm from './WorkScheduleForm';\r\n\r\nconst hours = Array.from({ length: 24 }, (_, i) => `${i.toString().padStart(2, '0')}:00`);\r\nconst SLOT_WIDTH = 60;\r\nconst USER_WIDTH = 200;\r\nconst ROW_HEIGHT = 60;\r\n\r\nconst DayWorkSchedule = ({ dateRange }) => {\r\n  const dispatch = useDispatch();\r\n  const users = useSelector(UserSelector.getUsers());\r\n  const [open, setOpen] = useState(false);\r\n  const [selectedUser, setSelectedUser] = useState(null);\r\n\r\n  const currentDate = dateRange?.startDate ? dayjs(dateRange.startDate) : dayjs();\r\n\r\n  useEffect(() => {\r\n    console.log(\"🔄 DayWorkSchedule: Fetching users data\");\r\n    dispatch(UserActions.getUsers());\r\n  }, [dispatch]);\r\n\r\n  // Debug: Log users data to see what's being returned\r\n  useEffect(() => {\r\n    console.log(\"🔍 DayWorkSchedule: Users data received:\", {\r\n      usersCount: users?.length || 0,\r\n      sampleUser: users?.[0],\r\n      sampleWorkSchedule: users?.[0]?.workSchedule\r\n    });\r\n  }, [users]);\r\n\r\n  const handleAddSchedule = (user = null) => {\r\n    setSelectedUser(user);\r\n    setOpen(true);\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setOpen(false);\r\n    setSelectedUser(null);\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      {/* Header */}\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\r\n        <Typography variant=\"h5\">\r\n          {currentDate.format(\"dddd, MMMM D, YYYY\")}\r\n        </Typography>\r\n        <Button\r\n          variant=\"contained\"\r\n          size=\"small\"\r\n          startIcon={<AddIcon />}\r\n          onClick={() => handleAddSchedule()}\r\n          sx={{ bgcolor: '#1976d2' }}\r\n        >\r\n          Add Schedule\r\n        </Button>\r\n      </Box>\r\n\r\n      {/* Schedule Table */}\r\n      <Box sx={{\r\n        width: '100%',\r\n        overflowX: 'auto',\r\n        overflowY: 'hidden',\r\n        border: '1px solid #ccc',\r\n        borderRadius: 1,\r\n        backgroundColor: '#fff',\r\n        pb: 2,\r\n        WebkitOverflowScrolling: 'touch'\r\n      }}>\r\n        <Box sx={{\r\n          minWidth: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px`,\r\n  width: `${USER_WIDTH + (hours.length * SLOT_WIDTH)}px` //\r\n        }}>\r\n          {/* Header Row */}\r\n          <Box sx={{ display: 'flex', position: 'sticky', top: 0, zIndex: 2 }}>\r\n            <Box sx={{\r\n              width: USER_WIDTH,\r\n              height: ROW_HEIGHT,\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              justifyContent: 'center',\r\n              fontWeight: 600,\r\n              fontSize: 13,\r\n              borderRight: '1px solid #ccc',\r\n              borderBottom: '1px solid #ccc',\r\n              backgroundColor: '#f0f0f0',\r\n              position: 'sticky',\r\n              left: 0,\r\n              zIndex: 3\r\n            }}>\r\n              User\r\n            </Box>\r\n            {hours.map((hour, idx) => (\r\n              <Box\r\n                key={idx}\r\n                sx={{\r\n                  width: SLOT_WIDTH,\r\n                  height: ROW_HEIGHT,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  justifyContent: 'center',\r\n                  fontSize: 12,\r\n                  fontWeight: 600,\r\n                  borderRight: '1px solid #ccc',\r\n                  borderBottom: '1px solid #ccc',\r\n                  backgroundColor: '#f0f0f0'\r\n                }}\r\n              >\r\n                {hour}\r\n              </Box>\r\n            ))}\r\n          </Box>\r\n\r\n          {/* User Rows */}\r\n          <Box sx={{ maxHeight: 400, overflowY: 'auto' }}>\r\n            {users.map((user, uIdx) => {\r\n              // Check if user has work schedule\r\n              const work = user.workSchedule;\r\n              const hasSchedule = work?.startTime && work?.endTime;\r\n              const currentDateStr = currentDate.format('YYYY-MM-DD');\r\n\r\n              // Check if current date falls within the schedule period\r\n              let isScheduledDate = false;\r\n              if (hasSchedule) {\r\n                if (work.shiftStart && work.shiftEnd) {\r\n                  // Convert ISO dates to YYYY-MM-DD format for comparison\r\n                  const shiftStartDate = dayjs(work.shiftStart).format('YYYY-MM-DD');\r\n                  const shiftEndDate = dayjs(work.shiftEnd).format('YYYY-MM-DD');\r\n\r\n                  // Check if current date is within shift period\r\n                  isScheduledDate = currentDateStr >= shiftStartDate && currentDateStr <= shiftEndDate;\r\n\r\n                  console.log(`🔍 DayWorkSchedule: Date comparison for ${user.name}:`, {\r\n                    currentDateStr,\r\n                    shiftStartDate,\r\n                    shiftEndDate,\r\n                    isScheduledDate\r\n                  });\r\n                } else {\r\n                  // If no shift dates specified, assume schedule applies to current date\r\n                  isScheduledDate = true;\r\n                }\r\n              }\r\n\r\n              const startHour = isScheduledDate ? parseInt(work.startTime.split(':')[0], 10) : null;\r\n              const endHour = isScheduledDate ? parseInt(work.endTime.split(':')[0], 10) : null;\r\n\r\n              // Check if this is a night shift (end time is before start time)\r\n              const isNightShift = isScheduledDate && endHour < startHour;\r\n\r\n              // Debug: Log time calculations\r\n              if (isScheduledDate) {\r\n                console.log(`🕐 DayWorkSchedule: Time calculation for ${user.name}:`, {\r\n                  startTime: work.startTime,\r\n                  endTime: work.endTime,\r\n                  startHour,\r\n                  endHour,\r\n                  isNightShift,\r\n                  scheduleTemplate: work.scheduleTemplate\r\n                });\r\n              }\r\n\r\n              return (\r\n                <Box key={uIdx} sx={{ display: 'flex' }}>\r\n                  {/* User Info */}\r\n                  <Box\r\n                    sx={{\r\n                      width: USER_WIDTH,\r\n                      minHeight: ROW_HEIGHT,\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      gap: 1,\r\n                      px: 2,\r\n                      backgroundColor: '#fff',\r\n                      borderRight: '1px solid #eee',\r\n                      borderBottom: '1px solid #eee',\r\n                      position: 'sticky',\r\n                      left: 0,\r\n                      zIndex: 1,\r\n                      cursor: 'pointer'\r\n                    }}\r\n                    onClick={() => handleAddSchedule(user)}\r\n                  >\r\n                    <Avatar sx={{ width: 32, height: 32 }}>\r\n                      {user.name?.[0]?.toUpperCase() || 'U'}\r\n                    </Avatar>\r\n                    <Box>\r\n                      <Typography fontWeight={600} fontSize={13}>\r\n                        {user.name || 'Unknown User'}\r\n                      </Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">\r\n                        {user.designation?.name || user.role || 'No Role'}\r\n                      </Typography>\r\n                    </Box>\r\n                  </Box>\r\n\r\n                  {/* Time Slots */}\r\n                  {hours.map((hour, hIdx) => {\r\n                    const hourNum = parseInt(hour.split(':')[0], 10);\r\n\r\n                    // Handle both day shift and night shift\r\n                    let isWorkingHour = false;\r\n                    if (isScheduledDate) {\r\n                      if (isNightShift) {\r\n                        // Night shift: working hours are from startHour to 24 OR from 0 to endHour\r\n                        isWorkingHour = (hourNum >= startHour) || (hourNum < endHour);\r\n                      } else {\r\n                        // Day shift: working hours are from startHour to endHour\r\n                        isWorkingHour = hourNum >= startHour && hourNum < endHour;\r\n                      }\r\n                    }\r\n\r\n                    return (\r\n                      <Box\r\n                        key={hIdx}\r\n                        sx={{\r\n                          width: SLOT_WIDTH,\r\n                          height: ROW_HEIGHT,\r\n                          borderRight: '1px solid #eee',\r\n                          borderBottom: '1px solid #eee',\r\n                          position: 'relative',\r\n                          backgroundColor: isWorkingHour ? '#4caf50' : '#fafafa',\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          justifyContent: 'center'\r\n                        }}\r\n                      >\r\n                        {isWorkingHour && (\r\n                          <Typography\r\n                            variant=\"caption\"\r\n                            sx={{\r\n                              fontSize: '8px',\r\n                              color: 'white',\r\n                              fontWeight: 600,\r\n                              textAlign: 'center',\r\n                              lineHeight: 1.2\r\n                            }}\r\n                          >\r\n                            {work.scheduleTemplate === 'night_shift' ? 'Night' : 'Day'}\r\n                            <br />\r\n                            {work.startTime}-{work.endTime}\r\n                          </Typography>\r\n                        )}\r\n                      </Box>\r\n                    );\r\n                  })}\r\n                </Box>\r\n              );\r\n            })}\r\n          </Box>\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Work Schedule Form */}\r\n      <WorkScheduleForm\r\n        open={open}\r\n        onClose={handleClose}\r\n        selectedUser={selectedUser}\r\n        selectedDate={currentDate.format('YYYY-MM-DD')}\r\n      />\r\n    </Box>\r\n  );\r\n};\r\n\r\nDayWorkSchedule.propTypes = {\r\n  dateRange: PropTypes.shape({\r\n    startDate: PropTypes.string,\r\n    endDate: PropTypes.string\r\n  })\r\n};\r\n\r\nexport default DayWorkSchedule;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,MAAM,EACNC,GAAG,EACHC,UAAU,EACVC,MAAM,QACD,eAAe;AACtB,OAAOC,OAAO,MAAM,yBAAyB;AAC7C,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,YAAY,QAAQ,WAAW;AACxC,OAAOC,gBAAgB,MAAM,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElD,MAAMC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;EAAEC,MAAM,EAAE;AAAG,CAAC,EAAE,CAACC,CAAC,EAAEC,CAAC,KAAK,GAAGA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC;AACzF,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,UAAU,GAAG,GAAG;AACtB,MAAMC,UAAU,GAAG,EAAE;AAErB,MAAMC,eAAe,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACzC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,KAAK,GAAGrB,WAAW,CAACE,YAAY,CAACoB,QAAQ,CAAC,CAAC,CAAC;EAClD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACvC,MAAM,CAACmC,YAAY,EAAEC,eAAe,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMqC,WAAW,GAAGT,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEU,SAAS,GAAG/B,KAAK,CAACqB,SAAS,CAACU,SAAS,CAAC,GAAG/B,KAAK,CAAC,CAAC;EAE/EN,SAAS,CAAC,MAAM;IACdsC,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;IACtDV,QAAQ,CAACnB,WAAW,CAACqB,QAAQ,CAAC,CAAC,CAAC;EAClC,CAAC,EAAE,CAACF,QAAQ,CAAC,CAAC;;EAEd;EACA7B,SAAS,CAAC,MAAM;IAAA,IAAAwC,OAAA;IACdF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;MACtDE,UAAU,EAAE,CAAAX,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEZ,MAAM,KAAI,CAAC;MAC9BwB,UAAU,EAAEZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAG,CAAC,CAAC;MACtBa,kBAAkB,EAAEb,KAAK,aAALA,KAAK,wBAAAU,OAAA,GAALV,KAAK,CAAG,CAAC,CAAC,cAAAU,OAAA,uBAAVA,OAAA,CAAYI;IAClC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACd,KAAK,CAAC,CAAC;EAEX,MAAMe,iBAAiB,GAAGA,CAACC,IAAI,GAAG,IAAI,KAAK;IACzCX,eAAe,CAACW,IAAI,CAAC;IACrBb,OAAO,CAAC,IAAI,CAAC;EACf,CAAC;EAED,MAAMc,WAAW,GAAGA,CAAA,KAAM;IACxBd,OAAO,CAAC,KAAK,CAAC;IACdE,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,oBACErB,OAAA,CAACZ,GAAG;IAAA8C,QAAA,gBAEFlC,OAAA,CAACZ,GAAG;MAAC+C,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,eAAe;QAAEC,UAAU,EAAE,QAAQ;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAL,QAAA,gBACzFlC,OAAA,CAACX,UAAU;QAACmD,OAAO,EAAC,IAAI;QAAAN,QAAA,EACrBZ,WAAW,CAACmB,MAAM,CAAC,oBAAoB;MAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eACb7C,OAAA,CAACV,MAAM;QACLkD,OAAO,EAAC,WAAW;QACnBM,IAAI,EAAC,OAAO;QACZC,SAAS,eAAE/C,OAAA,CAACT,OAAO;UAAAmD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBG,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAAC,CAAE;QACnCI,EAAE,EAAE;UAAEc,OAAO,EAAE;QAAU,CAAE;QAAAf,QAAA,EAC5B;MAED;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN7C,OAAA,CAACZ,GAAG;MAAC+C,EAAE,EAAE;QACPe,KAAK,EAAE,MAAM;QACbC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE,QAAQ;QACnBC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,CAAC;QACfC,eAAe,EAAE,MAAM;QACvBC,EAAE,EAAE,CAAC;QACLC,uBAAuB,EAAE;MAC3B,CAAE;MAAAvB,QAAA,eACAlC,OAAA,CAACZ,GAAG;QAAC+C,EAAE,EAAE;UACPuB,QAAQ,EAAE,GAAGhD,UAAU,GAAIT,KAAK,CAACG,MAAM,GAAGK,UAAW,IAAI;UACjEyC,KAAK,EAAE,GAAGxC,UAAU,GAAIT,KAAK,CAACG,MAAM,GAAGK,UAAW,IAAI,CAAC;QACjD,CAAE;QAAAyB,QAAA,gBAEAlC,OAAA,CAACZ,GAAG;UAAC+C,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAEuB,QAAQ,EAAE,QAAQ;YAAEC,GAAG,EAAE,CAAC;YAAEC,MAAM,EAAE;UAAE,CAAE;UAAA3B,QAAA,gBAClElC,OAAA,CAACZ,GAAG;YAAC+C,EAAE,EAAE;cACPe,KAAK,EAAExC,UAAU;cACjBoD,MAAM,EAAEnD,UAAU;cAClByB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxB0B,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,EAAE;cACZC,WAAW,EAAE,gBAAgB;cAC7BC,YAAY,EAAE,gBAAgB;cAC9BX,eAAe,EAAE,SAAS;cAC1BI,QAAQ,EAAE,QAAQ;cAClBQ,IAAI,EAAE,CAAC;cACPN,MAAM,EAAE;YACV,CAAE;YAAA3B,QAAA,EAAC;UAEH;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EACL5C,KAAK,CAACmE,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,kBACnBtE,OAAA,CAACZ,GAAG;YAEF+C,EAAE,EAAE;cACFe,KAAK,EAAEzC,UAAU;cACjBqD,MAAM,EAAEnD,UAAU;cAClByB,OAAO,EAAE,MAAM;cACfE,UAAU,EAAE,QAAQ;cACpBD,cAAc,EAAE,QAAQ;cACxB2B,QAAQ,EAAE,EAAE;cACZD,UAAU,EAAE,GAAG;cACfE,WAAW,EAAE,gBAAgB;cAC7BC,YAAY,EAAE,gBAAgB;cAC9BX,eAAe,EAAE;YACnB,CAAE;YAAArB,QAAA,EAEDmC;UAAI,GAdAC,GAAG;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAeL,CACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN7C,OAAA,CAACZ,GAAG;UAAC+C,EAAE,EAAE;YAAEoC,SAAS,EAAE,GAAG;YAAEnB,SAAS,EAAE;UAAO,CAAE;UAAAlB,QAAA,EAC5ClB,KAAK,CAACoD,GAAG,CAAC,CAACpC,IAAI,EAAEwC,IAAI,KAAK;YAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAC,iBAAA;YACzB;YACA,MAAMC,IAAI,GAAG5C,IAAI,CAACF,YAAY;YAC9B,MAAM+C,WAAW,GAAG,CAAAD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,SAAS,MAAIF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,OAAO;YACpD,MAAMC,cAAc,GAAG1D,WAAW,CAACmB,MAAM,CAAC,YAAY,CAAC;;YAEvD;YACA,IAAIwC,eAAe,GAAG,KAAK;YAC3B,IAAIJ,WAAW,EAAE;cACf,IAAID,IAAI,CAACM,UAAU,IAAIN,IAAI,CAACO,QAAQ,EAAE;gBACpC;gBACA,MAAMC,cAAc,GAAG5F,KAAK,CAACoF,IAAI,CAACM,UAAU,CAAC,CAACzC,MAAM,CAAC,YAAY,CAAC;gBAClE,MAAM4C,YAAY,GAAG7F,KAAK,CAACoF,IAAI,CAACO,QAAQ,CAAC,CAAC1C,MAAM,CAAC,YAAY,CAAC;;gBAE9D;gBACAwC,eAAe,GAAGD,cAAc,IAAII,cAAc,IAAIJ,cAAc,IAAIK,YAAY;gBAEpF7D,OAAO,CAACC,GAAG,CAAC,2CAA2CO,IAAI,CAACsD,IAAI,GAAG,EAAE;kBACnEN,cAAc;kBACdI,cAAc;kBACdC,YAAY;kBACZJ;gBACF,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL;gBACAA,eAAe,GAAG,IAAI;cACxB;YACF;YAEA,MAAMM,SAAS,GAAGN,eAAe,GAAGO,QAAQ,CAACZ,IAAI,CAACE,SAAS,CAACW,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;YACrF,MAAMC,OAAO,GAAGT,eAAe,GAAGO,QAAQ,CAACZ,IAAI,CAACG,OAAO,CAACU,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI;;YAEjF;YACA,MAAME,YAAY,GAAGV,eAAe,IAAIS,OAAO,GAAGH,SAAS;;YAE3D;YACA,IAAIN,eAAe,EAAE;cACnBzD,OAAO,CAACC,GAAG,CAAC,4CAA4CO,IAAI,CAACsD,IAAI,GAAG,EAAE;gBACpER,SAAS,EAAEF,IAAI,CAACE,SAAS;gBACzBC,OAAO,EAAEH,IAAI,CAACG,OAAO;gBACrBQ,SAAS;gBACTG,OAAO;gBACPC,YAAY;gBACZC,gBAAgB,EAAEhB,IAAI,CAACgB;cACzB,CAAC,CAAC;YACJ;YAEA,oBACE5F,OAAA,CAACZ,GAAG;cAAY+C,EAAE,EAAE;gBAAEC,OAAO,EAAE;cAAO,CAAE;cAAAF,QAAA,gBAEtClC,OAAA,CAACZ,GAAG;gBACF+C,EAAE,EAAE;kBACFe,KAAK,EAAExC,UAAU;kBACjBmF,SAAS,EAAElF,UAAU;kBACrByB,OAAO,EAAE,MAAM;kBACfE,UAAU,EAAE,QAAQ;kBACpBwD,GAAG,EAAE,CAAC;kBACNC,EAAE,EAAE,CAAC;kBACLxC,eAAe,EAAE,MAAM;kBACvBU,WAAW,EAAE,gBAAgB;kBAC7BC,YAAY,EAAE,gBAAgB;kBAC9BP,QAAQ,EAAE,QAAQ;kBAClBQ,IAAI,EAAE,CAAC;kBACPN,MAAM,EAAE,CAAC;kBACTmC,MAAM,EAAE;gBACV,CAAE;gBACFhD,OAAO,EAAEA,CAAA,KAAMjB,iBAAiB,CAACC,IAAI,CAAE;gBAAAE,QAAA,gBAEvClC,OAAA,CAACb,MAAM;kBAACgD,EAAE,EAAE;oBAAEe,KAAK,EAAE,EAAE;oBAAEY,MAAM,EAAE;kBAAG,CAAE;kBAAA5B,QAAA,EACnC,EAAAuC,UAAA,GAAAzC,IAAI,CAACsD,IAAI,cAAAb,UAAA,wBAAAC,WAAA,GAATD,UAAA,CAAY,CAAC,CAAC,cAAAC,WAAA,uBAAdA,WAAA,CAAgBuB,WAAW,CAAC,CAAC,KAAI;gBAAG;kBAAAvD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACT7C,OAAA,CAACZ,GAAG;kBAAA8C,QAAA,gBACFlC,OAAA,CAACX,UAAU;oBAAC0E,UAAU,EAAE,GAAI;oBAACC,QAAQ,EAAE,EAAG;oBAAA9B,QAAA,EACvCF,IAAI,CAACsD,IAAI,IAAI;kBAAc;oBAAA5C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB,CAAC,eACb7C,OAAA,CAACX,UAAU;oBAACmD,OAAO,EAAC,SAAS;oBAAC0D,KAAK,EAAC,gBAAgB;oBAAAhE,QAAA,EACjD,EAAAyC,iBAAA,GAAA3C,IAAI,CAACmE,WAAW,cAAAxB,iBAAA,uBAAhBA,iBAAA,CAAkBW,IAAI,KAAItD,IAAI,CAACoE,IAAI,IAAI;kBAAS;oBAAA1D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAGL5C,KAAK,CAACmE,GAAG,CAAC,CAACC,IAAI,EAAEgC,IAAI,KAAK;gBACzB,MAAMC,OAAO,GAAGd,QAAQ,CAACnB,IAAI,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;;gBAEhD;gBACA,IAAIc,aAAa,GAAG,KAAK;gBACzB,IAAItB,eAAe,EAAE;kBACnB,IAAIU,YAAY,EAAE;oBAChB;oBACAY,aAAa,GAAID,OAAO,IAAIf,SAAS,IAAMe,OAAO,GAAGZ,OAAQ;kBAC/D,CAAC,MAAM;oBACL;oBACAa,aAAa,GAAGD,OAAO,IAAIf,SAAS,IAAIe,OAAO,GAAGZ,OAAO;kBAC3D;gBACF;gBAEA,oBACE1F,OAAA,CAACZ,GAAG;kBAEF+C,EAAE,EAAE;oBACFe,KAAK,EAAEzC,UAAU;oBACjBqD,MAAM,EAAEnD,UAAU;oBAClBsD,WAAW,EAAE,gBAAgB;oBAC7BC,YAAY,EAAE,gBAAgB;oBAC9BP,QAAQ,EAAE,UAAU;oBACpBJ,eAAe,EAAEgD,aAAa,GAAG,SAAS,GAAG,SAAS;oBACtDnE,OAAO,EAAE,MAAM;oBACfE,UAAU,EAAE,QAAQ;oBACpBD,cAAc,EAAE;kBAClB,CAAE;kBAAAH,QAAA,EAEDqE,aAAa,iBACZvG,OAAA,CAACX,UAAU;oBACTmD,OAAO,EAAC,SAAS;oBACjBL,EAAE,EAAE;sBACF6B,QAAQ,EAAE,KAAK;sBACfkC,KAAK,EAAE,OAAO;sBACdnC,UAAU,EAAE,GAAG;sBACfyC,SAAS,EAAE,QAAQ;sBACnBC,UAAU,EAAE;oBACd,CAAE;oBAAAvE,QAAA,GAED0C,IAAI,CAACgB,gBAAgB,KAAK,aAAa,GAAG,OAAO,GAAG,KAAK,eAC1D5F,OAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EACL+B,IAAI,CAACE,SAAS,EAAC,GAAC,EAACF,IAAI,CAACG,OAAO;kBAAA;oBAAArC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpB;gBACb,GA5BIwD,IAAI;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6BN,CAAC;cAEV,CAAC,CAAC;YAAA,GAlFM2B,IAAI;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAmFT,CAAC;UAEV,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA,CAACF,gBAAgB;MACfoB,IAAI,EAAEA,IAAK;MACXwF,OAAO,EAAEzE,WAAY;MACrBb,YAAY,EAAEA,YAAa;MAC3BuF,YAAY,EAAErF,WAAW,CAACmB,MAAM,CAAC,YAAY;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC/B,EAAA,CA9PIF,eAAe;EAAA,QACFlB,WAAW,EACdC,WAAW;AAAA;AAAAiH,EAAA,GAFrBhG,eAAe;AAgQrBA,eAAe,CAACiG,SAAS,GAAG;EAC1BhG,SAAS,EAAEpB,SAAS,CAACqH,KAAK,CAAC;IACzBvF,SAAS,EAAE9B,SAAS,CAACsH,MAAM;IAC3BC,OAAO,EAAEvH,SAAS,CAACsH;EACrB,CAAC;AACH,CAAC;AAED,eAAenG,eAAe;AAAC,IAAAgG,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}