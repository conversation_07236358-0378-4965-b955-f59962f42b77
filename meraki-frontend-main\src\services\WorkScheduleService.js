/**
 * Work Schedule Service
 * 
 * This service handles all work schedule-related API calls including:
 * - Getting user work schedules
 * - Updating user work schedules
 * - Managing work schedule templates
 */

import { del, get, patch, post } from "../utils/api";

/**
 * Get user's work schedule
 *
 * @param {string} userId - User ID
 * @returns {Promise<Object>} Response containing user's work schedule
 */
const GetUserWorkSchedule = async (userId) => {
    try {
        return await get(`work-schedule/user/${userId}`);
    } catch (error) {
        throw new Error(`Failed to fetch user work schedule: ${error.message}`);
    }
};

/**
 * Update user's work schedule
 *
 * @param {string} userId - User ID
 * @param {Object} workSchedule - Work schedule data
 * @returns {Promise<Object>} Response containing update result
 */
const UpdateUserWorkSchedule = async (userId, workSchedule) => {
    try {
        return await patch(`work-schedule/user/${userId}`, { workSchedule });
    } catch (error) {
        throw new Error(`Failed to update user work schedule: ${error.message}`);
    }
};

/**
 * Get work schedule templates
 *
 * @returns {Promise<Object>} Response containing work schedule templates
 */
const GetWorkScheduleTemplates = async () => {
    try {
        return await get(`work-schedule/templates`);
    } catch (error) {
        throw new Error(`Failed to fetch work schedule templates: ${error.message}`);
    }
};

/**
 * Create work schedule template
 *
 * @param {Object} templateData - Template data
 * @returns {Promise<Object>} Response containing created template
 */
const CreateWorkScheduleTemplate = async (templateData) => {
    try {
        return await post(`work-schedule/templates`, templateData);
    } catch (error) {
        throw new Error(`Failed to create work schedule template: ${error.message}`);
    }
};

/**
 * Update work schedule template
 *
 * @param {string} templateId - Template ID
 * @param {Object} templateData - Updated template data
 * @returns {Promise<Object>} Response containing update result
 */
const UpdateWorkScheduleTemplate = async (templateId, templateData) => {
    try {
        return await patch(`work-schedule/templates/${templateId}`, templateData);
    } catch (error) {
        throw new Error(`Failed to update work schedule template: ${error.message}`);
    }
};

/**
 * Delete work schedule template
 *
 * @param {string} templateId - Template ID
 * @returns {Promise<Object>} Response containing deletion result
 */
const DeleteWorkScheduleTemplate = async (templateId) => {
    try {
        return await del(`work-schedule/templates/${templateId}`);
    } catch (error) {
        throw new Error(`Failed to delete work schedule template: ${error.message}`);
    }
};

/**
 * Work Schedule service object with all work schedule-related API methods
 */
export const WorkScheduleService = {
    GetUserWorkSchedule,
    UpdateUserWorkSchedule,
    GetWorkScheduleTemplates,
    CreateWorkScheduleTemplate,
    UpdateWorkScheduleTemplate,
    DeleteWorkScheduleTemplate
};
