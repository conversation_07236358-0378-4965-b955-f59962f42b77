'use strict';
const { db } = require("../models")
const Setting = db.setting;
exports.createSetting = async (data) => {
    return Setting.insertMany(await Promise.all(data));
}
exports.getSetting = async () => await Setting.find();
exports.updateSetting = async (id, data) => await Setting.findByIdAndUpdate(id, data);
exports.addCompamnyLeave = async (id, body) => {
    try {
        let result = await Setting.findById(id);
        if (!result) {
            throw new Error("Document not found");
        }

        // Check for duplicate leaveDate
        const exists = result.companyLeaveArr.some(leave =>
            new Date(leave.leaveDate).getTime() === new Date(body.leaveDate).getTime()
        );

        if (exists) {
            throw new Error("Duplicate leaveDate detected");
        }

        result.companyLeaveArr.push({
            leaveDate: body.leaveDate,
            description: body.description
        });

        return await result.save();
    } catch (error) {
        throw error;
    }
};

exports.clearCompanyLeaves = async (id) => {
    try {
        let result = await Setting.findById(id);
        if (!result) {
            throw new Error("Document not found");
        }
        result.companyLeaveArr = [];
        await result.save();
        let updatedResult = await Setting.findById(id);
        return updatedResult;
    } catch (error) {
        throw error;
    }
};
