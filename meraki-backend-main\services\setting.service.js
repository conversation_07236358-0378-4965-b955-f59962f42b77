'use strict';
const { db } = require("../models")
const Setting = db.setting;
exports.createSetting = async (data) => {
    return Setting.insertMany(await Promise.all(data));
}
exports.getSetting = async () => await Setting.find();
exports.updateSetting = async (id, data) => await Setting.findByIdAndUpdate(id, data);
exports.addCompamnyLeave = async (id,data) => {
    let result = await Setting.findById(id)
    try { 
        if (exists) {
            throw new Error("Duplicate leaveDate detected");
        }
        result.companyLeaveArr.push({
             leaveDate:body["leaveDate"],
            description:body["description"]
        })
        return await result.save()
    }
    catch {
        if (!result) {
            throw new Error("Document not found");
        }
        result.companyLeaveArr = [];
        // Save the document
        await result.save();
        let updatedResult = await Setting.findById(id);
        return updatedResult;
    } catch (error) {
        throw error; // Re-throw the error for further handling if needed
    }
};
