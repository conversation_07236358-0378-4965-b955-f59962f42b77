'use strict';

const { db } = require("../models")
const Department = db.department;


// Create MUltiple
exports.createManyDepartments = async (data) => {
    return Department.insertMany(data);
};

// create
exports.createDepartment = async (data) => await Department.create(data).then((result) => result);

//gRead All With Sorted Data
exports.getDepartmentsByQuery = async (queries) => {
    const limit = queries.limit ?? 20;
    const page = queries.page ?? 1;
    const skip = limit * (page - 1);
    const sort = queries.sort ?? { createdAt: -1 };
    const query = queries.query ?? {};

    const results = await Department.
    find(query).
    skip(skip).
    limit(limit).
    sort(sort)
    const counts = await Department.countDocuments(query);

    return {
        query,
        pagination: {
            perPage: limit,
            currentPage: page,
            counts,
            pages: Math.ceil(counts / limit)
        },
        data: results
    }
}

//Read One
exports.getDepartmentById = async (id) => await Department.findById(id)

//Update One 
exports.updateDepartment = async (id, data) => await Department.findByIdAndUpdate(id, data)

//Delete
exports.deleteDepartment = async (id) => await Department.findByIdAndDelete(id)
