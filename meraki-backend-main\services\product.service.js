"use strict";
const { db } = require("../models");
const Product = db.product;
const Sprint = db.sprint;
const mongoose = require("mongoose");
//Fetch paginated products with sorting.
exports.getProducts = async (queries) => {
  const limit = parseInt(queries?.limit) || 20; // Default limit is 20
  const page = parseInt(queries?.page) || 1; // Default page is 1
  const skip = limit * (page - 1);
  const sort = { createdAt: -1 }; // Sort by newest first
  let results = await Product.find().skip(skip).limit(limit).sort(sort);
  const totalRecords = await Product.countDocuments(); // Get total count
  return {
    pagination: {
      perPage: limit,
      currentPage: page,
      totalRecords,
      totalPages: Math.ceil(totalRecords / limit),
    },
    data: results,
  };
};
// Create a new product.
exports.createProduct = async (body) => {
  const {
    productName,
    description,
    reporter,
    startDate,
    endDate,
    taskTags,
    priority,
    client,
    members,
    visibility,
    sprintId,
  } = body;
  try {
    // Create the product
    let product = await Product.create({
      productName,
      description,
      startDate,
      endDate,
      taskTags,
      priority,
      client,
      reporter,
      members,
      visibility,
    });
    // If sprintId is provided, add the product to the sprint
    if (sprintId && mongoose.Types.ObjectId.isValid(sprintId)) {
      // Check if sprint exists
      const sprint = await db.sprint.findById(sprintId);
      if (sprint) {
        // Update the sprint with the product ID
        sprint.productId = product._id;
        await sprint.save();
      }
    }
    return {
      message: "Product created successfully",
      data: product,
    };
  } catch (error) {
    throw error;
  }
};
//Update product details.
exports.updateProduct = async (id, body) => {
  let existingProduct = await Product.findById(id);
  if (!existingProduct) throw new Error("Product not found");
  // Update fields only if provided in the request
  if (body?.description) existingProduct.description = body.description;
  if (body?.taskTitle) existingProduct.title = body.taskTitle;
  if (body?.startDate) existingProduct.startDate = body.startDate;
  if (body?.endDate) existingProduct.endDate = body.endDate;
  if (body?.members) existingProduct.members = body.members;
  if (body?.sprintId !== undefined) existingProduct.sprintId = body.sprintId;
  await existingProduct.save();
  return existingProduct;
};
//Delete a product.
exports.deleteProduct = async (id) => {
  try {
    const result = await Product.findByIdAndDelete(id);
    if (!result) {
      throw new Error("Product not found");
    }
    return result;
  } catch (error) {
    throw error;
  }
};
//Delete a task from a product.
exports.deleteTask = async (productId, taskId) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error("Invalid product ID format");
    }
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      throw new Error("Invalid task ID format");
    }
    const product = await Product.findById(productId);
    if (!product) {
      throw new Error("Product not found");
    }
    // Find and remove the task
    const taskIndex = product.taskArr.findIndex(task => task._id.toString() === taskId);
    if (taskIndex === -1) {
      throw new Error("Task not found");
    }
    // Remove the task from the array
    product.taskArr.splice(taskIndex, 1);
    await product.save();
    return { message: "Task deleted successfully" };
  } catch (error) {
    throw error;
  }
};
// Get a product by ID.
exports.getProductById = async (id) => {
  let result = await Product.findById(id);
  return { data: result };
};
// Create a new task within a product.
exports.createTask = async (id, body) => {
  try {
    let existingProduct = await Product.findById(id);
    if (!existingProduct) throw new Error("Product not found");
    // Ensure taskArr exists
    if (!existingProduct.taskArr) {
      existingProduct.taskArr = [];
    }
    // Validate and process assignee field
    let assigneeArray = [];
    if (body.assignee) {
      if (Array.isArray(body.assignee)) {
        assigneeArray = body.assignee.filter(id => mongoose.Types.ObjectId.isValid(id));
      } else if (mongoose.Types.ObjectId.isValid(body.assignee)) {
        assigneeArray = [body.assignee];
      }
    }
    // Create task object with proper validation
    let taskObj = {
      taskTitle: body.taskTitle || "Untitled Task",
      assignee: assigneeArray,
      reporter: body.reporter || null,
      priority: body.priority || "Medium",
      description: body.description || "",
      taskType: body.taskType || "Normal",
      billingStatus: body.billingStatus || "Non-Billable",
      taskStatus: "To Do", // Default status
      sprintId: body.sprintId || null,
      addToSprint: body.sprintId ? true : false,
      pauseTimes: [],
      hours: new Map(), // Use Map for better date-wise tracking
      totalHours: 0,
      totalSpent: 0
    };
    // Add task to product
    existingProduct.taskArr.push(taskObj);
    await existingProduct.save();
    // Get the newly created task
    const newTask = existingProduct.taskArr[existingProduct.taskArr.length - 1];
    return {
      message: "Task created successfully",
      data: newTask,
    };
  } catch (error) {
    throw error;
  }
};
//Get products by user ID with tasks assigned to the user.
exports.getProductsByUser = async (id) => {
  try {
    // Find products where user is a member, has visibility, or has assigned tasks
    let result = await Product.find({
      $or: [
        { members: { $in: [id] } }, // User is a member
        { visibility: true }, // Product is visible to all
        { "taskArr.assignee": { $in: [id] } }, // User has assigned tasks
        { reporter: id } // User is the reporter
      ],
    });
    // Filter tasks to show only those assigned to the user or visible tasks
    const filteredResult = result.map(product => {
      const filteredTasks = product.taskArr.filter(task => 
        task.assignee.includes(id) || 
        product.visibility || 
        product.members.includes(id) ||
        task.reporter?.toString() === id
      );
      return {
        ...product.toObject(),
        taskArr: filteredTasks
      };
    });
    return { data: filteredResult };
  } catch (error) {
    throw error;
  }
};
//Update an existing task within a product.
exports.updateTask = async (productId, taskId, body) => {
  if (!mongoose.Types.ObjectId.isValid(productId)) {
    throw new Error("Invalid product ID format");
  }
  const product = await Product.findById(productId);
  if (!product) {
    throw new Error("Product not found");
  }
  if (!product.taskArr || !Array.isArray(product.taskArr)) {
    throw new Error("Product has no taskArr");
  }
  const task = product.taskArr.id(taskId);
  if (!task) {
    throw new Error("Task not found");
  }
  // === Update fields based on body ===
  if (body.taskTitle !== undefined) task.taskTitle = body.taskTitle;
  if (body.priority !== undefined) task.priority = body.priority;
  if (body.billingStatus !== undefined) task.billingStatus = body.billingStatus;
  if (body.taskType !== undefined) task.taskType = body.taskType;
  if (body.description !== undefined) task.description = body.description;
  if (body.taskStatus !== undefined) task.taskStatus = body.taskStatus;
  if (body.totalActiveTime !== undefined) {
    // If totalActiveTime is provided, use it to update totalSpent and totalHours
    const activeTimeHours = body.totalActiveTime / 3600; // Convert seconds to hours
    task.totalSpent = activeTimeHours;
    task.totalHours = activeTimeHours;
  }
  // Handle assignee updates properly
  if (body.assignee !== undefined) {
    if (Array.isArray(body.assignee)) {
      // Replace assignee array with new values
      task.assignee = body.assignee.filter(id => mongoose.Types.ObjectId.isValid(id));
    } else if (mongoose.Types.ObjectId.isValid(body.assignee)) {
      task.assignee = [body.assignee];
    } else {
      task.assignee = [];
    }
  }
  // Handle sprint assignment
  if (body.sprintId !== undefined) {
    // If sprintId is null, remove the task from any sprint
    if (body.sprintId === null) {
      task.sprintId = null;
      task.addToSprint = false;
    }
    // Otherwise, assign to the specified sprint
    else if (mongoose.Types.ObjectId.isValid(body.sprintId)) {
      task.sprintId = body.sprintId;
      task.addToSprint = true; // Always set to true when sprintId is provided
    }
  }
  // Handle assignedHour and assignedMinute into task.hours
  if (body.assignedHour || body.assignedMinute) {
    if (!task.hours) task.hours = new Map();
    task.hours.set('assignedHour', body.assignedHour || "00");
    task.hours.set('assignedMinute', body.assignedMinute || "00");
    task.markModified("hours");
  }
  await product.save();
  return task;
};
//Start a task timer.
exports.startTask = async (productId, taskId, date) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error("Invalid product ID format");
    }
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      throw new Error("Invalid task ID format");
    }
    const product = await Product.findById(productId);
    if (!product) throw new Error("Product not found");
    const task = product.taskArr.id(taskId);
    if (!task) throw new Error("Task not found");
    const startTime = new Date();
    const dateKey = date || startTime.toISOString().split("T")[0];
    if (task.taskStatus === "In Progress") {
      throw new Error("Task is already running");
    }
    if (task.taskStatus === "Pause") {
      // When resuming, track when we resumed
      task.lastResumeTime = startTime;
      // Don't reset the first start time or pause history
    } else {
      // New task - reset tracking data
      task.firstStartTime = startTime; // Track the very first start time
      task.pauseTimes = [];
      task.totalPausedTime = 0; // Reset total paused time in seconds
      // Initialize hours Map for new task
      task.hours = new Map();
    }
    // Set current start time and status
    task.startTime = startTime;
    task.currentDate = dateKey;
    task.taskStatus = "In Progress";
    // Tell Mongoose the nested task was modified
    const taskIndex = product.taskArr.findIndex(
      (t) => t._id.toString() === taskId.toString()
    );
    product.markModified(`taskArr.${taskIndex}`);
    product.markModified(`taskArr.${taskIndex}.startTime`);
    product.markModified(`taskArr.${taskIndex}.firstStartTime`);
    product.markModified(`taskArr.${taskIndex}.taskStatus`);
    product.markModified(`taskArr.${taskIndex}.pauseTimes`);
    product.markModified(`taskArr.${taskIndex}.totalPausedTime`);
    product.markModified(`taskArr.${taskIndex}.hours`);
    product.markModified(`taskArr.${taskIndex}.lastResumeTime`);
    await product.save();
    const taskToReturn = task.toObject
      ? task.toObject()
      : JSON.parse(JSON.stringify(task));
    return taskToReturn;
  } catch (error) {
    throw error;
  }
};
exports.stopTask = async (
  productId,
  taskId,
  elapsedTime,
  date,
  pauseHistory
) => {
  return await exports.submitTask(productId, taskId, elapsedTime, date, pauseHistory);
};
exports.submitTask = async (
  productId,
  taskId,
  elapsedTime,
  date,
  pauseHistory
) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error("Invalid product ID format");
    }
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      throw new Error("Invalid task ID format");
    }
    const product = await Product.findById(productId);
    if (!product) throw new Error("Product not found");
    const task = product.taskArr.id(taskId);
    if (!task) throw new Error("Task not found");
    if (!task.startTime) throw new Error("Task was never started");
    const now = new Date();
    const dateKey = date || now.toISOString().split("T")[0];
    // If this is a direct stop (without pause), add the current session time
    if (task.taskStatus !== "Pause") {
      const currentStartTime = new Date(task.startTime);
      const currentSessionSeconds = Math.max(0, Math.floor((now - currentStartTime) / 1000));
      // Use frontend elapsed time if provided, otherwise calculate from timestamps
      const actualSessionSeconds = (elapsedTime !== undefined && elapsedTime >= 0) ? 
        Math.max(0, elapsedTime) : currentSessionSeconds;
      // Record this final session in pauseTimes for consistency
      task.pauseTimes = task.pauseTimes || [];
      task.pauseTimes.push({
        startTime: currentStartTime,
        pauseTime: now,
        elapsedSeconds: actualSessionSeconds,
        date: dateKey,
      });
      // Add to total active time
      const currentSessionHours = actualSessionSeconds / 3600;
      const currentHours = task.hours.get(dateKey) || 0;
      task.hours.set(dateKey, Math.max(0, currentHours + currentSessionHours));
    }
    // Calculate total time by summing all active sessions from pauseTimes
    let totalTimeSeconds = 0;
    // Sum all elapsed seconds from pauseTimes (this is the most accurate method)
    if (task.pauseTimes && Array.isArray(task.pauseTimes)) {
      totalTimeSeconds = task.pauseTimes.reduce((total, pauseEntry) => {
        const sessionSeconds = Math.max(0, pauseEntry.elapsedSeconds || 0);
        return total + sessionSeconds;
      }, 0);
    } else {
      // Fallback: sum from hours object
      if (task.hours && typeof task.hours === 'object') {
        if (task.hours instanceof Map) {
          task.hours.forEach((value, key) => {
            if (typeof value === "number" && /^\d{4}-\d{2}-\d{2}$/.test(key)) {
              totalTimeSeconds += Math.max(0, value * 3600); // Convert hours to seconds
            }
          });
        } else {
          Object.keys(task.hours).forEach(key => {
            if (typeof task.hours[key] === "number" && /^\d{4}-\d{2}-\d{2}$/.test(key)) {
              totalTimeSeconds += Math.max(0, task.hours[key] * 3600); // Convert hours to seconds
            }
          });
        }
      }
    }
    // Ensure total time is never negative
    totalTimeSeconds = Math.max(0, totalTimeSeconds);
    // Convert total seconds to hours
    const totalHours = totalTimeSeconds / 3600;
    // Update fields
    task.totalSpent = totalHours;
    task.totalHours = totalHours;
    task.endTime = now;
    task.startTime = null;
    task.taskStatus = "Completed";
    // Ensure mongoose knows the nested object was modified
    product.markModified("taskArr");
    const taskIndex = product.taskArr.indexOf(task);
    product.markModified(`taskArr.${taskIndex}.hours`);
    product.markModified(`taskArr.${taskIndex}.pauseTimes`);
    product.markModified(`taskArr.${taskIndex}.totalPausedTime`);
    product.markModified(`taskArr.${taskIndex}.endTime`);
    await product.save();
    // Create a clean object to return
    const taskToReturn = task.toObject
      ? task.toObject()
      : JSON.parse(JSON.stringify(task));
    return taskToReturn;
  } catch (error) {
    throw error;
  }
};
exports.pauseTask = async (
  productId,
  taskId,
  elapsedTime,
  pauseTime,
  date,
  startTime
) => {
  try {
    if (!mongoose.Types.ObjectId.isValid(productId)) {
      throw new Error("Invalid product ID format");
    }
    if (!mongoose.Types.ObjectId.isValid(taskId)) {
      throw new Error("Invalid task ID format");
    }
    const product = await Product.findById(productId);
    if (!product) throw new Error("Product not found");
    const task = product.taskArr.id(taskId);
    if (!task) throw new Error("Task not found");
    if (!task.startTime) throw new Error("Task was never started");
    const pauseTimeDate = pauseTime ? new Date(pauseTime) : new Date();
    const taskStartTime = new Date(task.startTime);
    // Calculate pause duration in seconds - use frontend elapsed time which is accurate
    let pauseDurationSeconds;
    if (elapsedTime !== undefined && elapsedTime >= 0) {
      // Frontend provides the actual elapsed time for this session
      pauseDurationSeconds = Math.max(0, elapsedTime);
    } else {
      // Fallback: Calculate from timestamps (but this might be inaccurate due to timezone/clock differences)
      pauseDurationSeconds = Math.max(0, Math.floor((pauseTimeDate - taskStartTime) / 1000));
    }
    const dateKey = date || pauseTimeDate.toISOString().split("T")[0];
    // Record pause event in pauseTimes array
    task.pauseTimes = task.pauseTimes || [];
    task.pauseTimes.push({
      startTime: taskStartTime,
      pauseTime: pauseTimeDate,
      elapsedSeconds: pauseDurationSeconds,
      date: dateKey,
    });
    // Update total paused time
    task.totalPausedTime = (task.totalPausedTime || 0) + pauseDurationSeconds;
    // Initialize hours as Map if it doesn't exist
    if (!task.hours) {
      task.hours = new Map();
    }
    // Calculate active time for this session in hours
    const activeTimeHours = pauseDurationSeconds / 3600;
    // Store this session's duration in hours object
    const currentHours = task.hours.get(dateKey) || 0;
    task.hours.set(dateKey, currentHours + activeTimeHours);
    task.lastPauseTime = pauseTimeDate;
    task.taskStatus = "Pause";
    // Calculate total hours from active time
    // We'll do a more accurate calculation in stopTask
    let totalHours = 0;
    task.hours.forEach((value, key) => {
      if (typeof value === "number" && /^\d{4}-\d{2}-\d{2}$/.test(key)) {
        totalHours += value;
      }
    });
    task.totalSpent = totalHours;
    task.totalHours = totalHours;
    // Ensure mongoose knows the nested objects were modified
    product.markModified("taskArr");
    const taskIndex = product.taskArr.indexOf(task);
    product.markModified(`taskArr.${taskIndex}.hours`);
    product.markModified(`taskArr.${taskIndex}.pauseTimes`);
    product.markModified(`taskArr.${taskIndex}.totalPausedTime`);
    product.markModified(`taskArr.${taskIndex}.lastPauseTime`);
    product.markModified(`taskArr.${taskIndex}.lastResumeTime`);
    await product.save();
    // Create a clean object to return
    const taskToReturn = task.toObject
      ? task.toObject()
      : JSON.parse(JSON.stringify(task));
    return taskToReturn;
  } catch (error) {
    throw error;
  }
};
exports.deleteCheckOutTime = async (_id) => {
  const updatedDoc = await Activity.findByIdAndUpdate(
    _id,
    { $unset: { checkOutTime: "" } },
    { new: true } // <-- This returns the updated document
  );
  return updatedDoc;
};
exports.todayActivity = async (params) => {
  const startOfDay = new Date();
  startOfDay.setHours(0, 0, 0, 0);
  const endOfDay = new Date();
  endOfDay.setHours(23, 59, 59, 999);
  try {
    const result = await Activity.findOne({
      user: params.id,
      createdAt: { $gte: startOfDay, $lte: endOfDay },
    });
    return result;
  } catch (error) {
    throw new Error("Error fetching today's activity");
  }
};
exports.getOnGoingProductsTasksToday = async () => {
  try {
    const now = new Date();
const startOfDay = new Date(Date.UTC(
  now.getUTCFullYear(),
  now.getUTCMonth(),
  now.getUTCDate(),
  0, 0, 0, 0
));
const endOfDay = new Date(Date.UTC(
  now.getUTCFullYear(),
  now.getUTCMonth(),
  now.getUTCDate(),
  23, 59, 59, 999
));
const products = await Product.find({
  status: "Ongoing",
  taskArr: {
    $elemMatch: {
      taskStatus: { $in: ["In Progress", "Pending", "Pause", "Completed"] },
    }
  }
});
    return {
      data: products,
    };
  } catch (error) {
    throw new Error("Error fetching ongoing products");
  }
};
// Helper function to calculate total hours from the hours object
function calculateTotalHours(hoursObj) {
  if (!hoursObj || typeof hoursObj !== "object") return 0;
  // Filter out non-date keys (like assignedHour and assignedMinute)
  const dateKeys = Object.keys(hoursObj).filter((key) =>
    /^\d{4}-\d{2}-\d{2}$/.test(key)
  );
  // Sum all values for date keys
  return dateKeys.reduce((total, key) => {
    const hours = hoursObj[key];
    return total + (typeof hours === "number" ? hours : 0);
  }, 0);
}
