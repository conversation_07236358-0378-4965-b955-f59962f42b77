{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\Timeline\\\\components\\\\DayView.jsx\";\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport dayjs from 'dayjs';\nimport { Box, Typography, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, Tooltip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DayView = ({\n  data,\n  multiDay = false,\n  dataArray = []\n}) => {\n  // If no data is provided, show a message\n  if (!data && !multiDay || multiDay && (!dataArray || dataArray.length === 0)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '300px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        color: \"text.secondary\",\n        children: \"No data available for the selected date range\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this);\n  }\n\n  // If multiDay is true, use dataArray for rendering multiple days\n  // Make sure we have a valid array to work with\n  let displayData = [data];\n  if (multiDay) {\n    displayData = Array.isArray(dataArray) ? dataArray : [];\n  }\n\n  // Log the data we're working with for debugging\n  console.log(\"DayView rendering with data:\", {\n    multiDay,\n    dataLength: displayData.length,\n    firstItem: displayData.length > 0 ? displayData[0] : null\n  });\n\n  // Format date for display\n  const formatDate = dateString => {\n    if (!dateString) {\n      return '';\n    }\n    const parts = dateString.split(' ');\n    return parts.length > 0 ? parts[0] : '';\n  };\n\n  // Extract day of week from date string\n  const getDayOfWeek = dateString => {\n    if (!dateString) {\n      return '';\n    }\n    const parts = dateString.split(' ');\n    return parts.length > 1 ? parts[parts.length - 1] : '';\n  };\n\n  // Function to render detailed timeline bar with hour divisions\n  const renderTimelineBar = row => {\n    // Debug: Log the row data to understand the structure\n    console.log(\"🔍 DayView Timeline Bar - Row data:\", row);\n\n    // If row is undefined or has no work time, show empty bar with hour divisions\n    if (!row || !row.atwork || row.atwork === \"--\") {\n      console.log(\"❌ DayView Timeline Bar - No work data, showing empty bar\");\n      return renderEmptyTimelineBar();\n    }\n    console.log(\"✅ DayView Timeline Bar - Processing work data:\", {\n      atwork: row.atwork,\n      clockin: row.clockin,\n      clockout: row.clockout,\n      productivity: row.productivity,\n      idle: row.idle,\n      private: row.private\n    });\n\n    // Ensure all days show similar working details by setting minimum values for activity types\n    // This ensures that even if a day has 0 idle or break time, it will still show those segments\n    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility\n\n    // Parse time values - handle both numbers and formatted strings\n    const parseTime = timeStr => {\n      if (!timeStr || timeStr === \"--\") {\n        return 0;\n      }\n\n      // If it's already a number, return it\n      if (typeof timeStr === 'number') {\n        return timeStr;\n      }\n\n      // If it's a string, try to parse it\n      if (typeof timeStr === 'string') {\n        // Handle formatted strings like \"2h 30m\" or \"45m\"\n        const match = timeStr.match(/(?:(\\d+)h\\s*)?(?:(\\d+)m)?/);\n        if (match) {\n          const hours = parseInt(match[1], 10) || 0;\n          const minutes = parseInt(match[2], 10) || 0;\n          return hours * 60 + minutes; // Return total minutes\n        }\n      }\n      return 0;\n    };\n\n    // Parse clock in/out times to determine active period\n    const parseClockTime = timeStr => {\n      if (!timeStr || timeStr === \"--\") {\n        return null;\n      }\n      const [time, period] = timeStr.split(' ');\n      const [hours, minutes] = time.split(':').map(Number);\n      let hour = hours;\n      if (period === 'PM' && hours !== 12) {\n        hour += 12;\n      } else if (period === 'AM' && hours === 12) {\n        hour = 0;\n      }\n      return {\n        hour,\n        minutes\n      };\n    };\n\n    // Get clock in/out times\n    const clockIn = parseClockTime(row.clockin);\n    const clockOut = parseClockTime(row.clockout);\n\n    // Calculate start and end hours for the active period\n    const startHour = clockIn ? clockIn.hour : 9; // Default to 9 AM if no clock in\n    const endHour = clockOut ? clockOut.hour : 17; // Default to 5 PM if no clock out\n\n    // Format time for tooltips\n    const formatTimeForTooltip = minutes => {\n      if (minutes === 0) {\n        return \"0m\";\n      }\n      const hours = Math.floor(minutes / 60);\n      const mins = minutes % 60;\n      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\n    };\n\n    // Create hour divisions for the timeline\n    const hourDivisions = [];\n    for (let i = 0; i < 24; i++) {\n      const isActive = i >= startHour && i <= endHour;\n\n      // Only show tooltips for active hours, remove tooltips from inactive areas\n      if (isActive) {\n        const hourLabel = i < 12 ? `${i === 0 ? 12 : i}AM` : `${i === 12 ? 12 : i - 12}PM`;\n        hourDivisions.push(/*#__PURE__*/_jsxDEV(Tooltip, {\n          title: hourLabel,\n          arrow: true,\n          placement: \"top\",\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: `${100 / 24}%`,\n              height: '100%',\n              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\n              backgroundColor: 'rgba(0,0,0,0.03)',\n              position: 'relative'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this));\n      } else {\n        hourDivisions.push(/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: `${100 / 24}%`,\n            height: '100%',\n            borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\n            backgroundColor: 'transparent',\n            position: 'relative'\n          }\n        }, i, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this));\n      }\n    }\n\n    // Parse activity data - use correct field names from TimelineNew.jsx\n    const atWorkMinutes = parseTime(row.atwork);\n\n    // Ensure minimum values for all activity types to make them visible\n    // This ensures all days show similar working details\n    let productivityMinutes = parseTime(row.productivitytime);\n    let idleMinutes = parseTime(row.idletime);\n    let privateMinutes = parseTime(row.privatetime);\n    console.log(\"📊 DayView Timeline Bar - Parsed minutes:\", {\n      atWorkMinutes,\n      productivityMinutes,\n      idleMinutes,\n      privateMinutes,\n      rawData: {\n        productivitytime: row.productivitytime,\n        idletime: row.idletime,\n        privatetime: row.privatetime\n      }\n    });\n\n    // If we have work time but no activity breakdown, ensure minimum values\n    if (atWorkMinutes > 0) {\n      // Ensure at least some productivity time is shown\n      if (productivityMinutes === 0) {\n        productivityMinutes = MIN_ACTIVITY_MINUTES;\n      }\n\n      // Ensure at least some idle time is shown\n      if (idleMinutes === 0) {\n        idleMinutes = MIN_ACTIVITY_MINUTES;\n      }\n\n      // Ensure at least some break time is shown\n      if (privateMinutes === 0) {\n        privateMinutes = MIN_ACTIVITY_MINUTES;\n      }\n    }\n\n    // Calculate activity bars\n    const activityBars = [];\n\n    // Only add activity bars if we have clock in time\n    if (clockIn) {\n      // Calculate position and width based on clock in/out times\n      const startPercent = (clockIn.hour * 60 + clockIn.minutes) / (24 * 60) * 100;\n      const endPercent = clockOut ? (clockOut.hour * 60 + clockOut.minutes) / (24 * 60) * 100 : Math.min(100, startPercent + atWorkMinutes / (24 * 60) * 100);\n      const width = endPercent - startPercent;\n\n      // Calculate activity percentages\n      const totalActiveMinutes = atWorkMinutes;\n\n      // Ensure minimum visibility for each activity type if it exists\n      const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists\n\n      // Calculate initial percentages\n      let productivityPercent = totalActiveMinutes > 0 ? productivityMinutes / totalActiveMinutes * 100 : 0;\n      let idlePercent = totalActiveMinutes > 0 ? idleMinutes / totalActiveMinutes * 100 : 0;\n      let privatePercent = totalActiveMinutes > 0 ? privateMinutes / totalActiveMinutes * 100 : 0;\n\n      // Ensure minimum visibility for activities that exist\n      if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {\n        productivityPercent = minVisibilityPercent;\n      }\n      if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {\n        idlePercent = minVisibilityPercent;\n      }\n      if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {\n        privatePercent = minVisibilityPercent;\n      }\n\n      // Normalize percentages to ensure they sum to 100%\n      const newTotalPercent = productivityPercent + idlePercent + privatePercent;\n      if (newTotalPercent > 0) {\n        const scaleFactor = 100 / newTotalPercent;\n        productivityPercent *= scaleFactor;\n        idlePercent *= scaleFactor;\n        privatePercent *= scaleFactor;\n      }\n\n      // Add activity bars\n      activityBars.push(/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          left: `${startPercent}%`,\n          top: 0,\n          height: '100%',\n          width: `${width}%`,\n          display: 'flex',\n          overflow: 'hidden',\n          borderRadius: '3px',\n          border: '1px solid rgba(0,0,0,0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            width: '100%',\n            height: '100%'\n          },\n          children: [atWorkMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Time at Work: ${atWorkMinutes < 60 ? `${atWorkMinutes}m` : `${Math.floor(atWorkMinutes / 60)}h ${Math.floor(atWorkMinutes % 60)}m`}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: 'absolute',\n                width: '100%',\n                height: '100%',\n                backgroundColor: '#E8F5E9',\n                // Light green background for total work time\n                zIndex: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this), productivityMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Productive: ${productivityMinutes < 60 ? `${productivityMinutes}m` : `${Math.floor(productivityMinutes / 60)}h ${Math.floor(productivityMinutes % 60)}m`}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${productivityPercent}%`,\n                height: '100%',\n                backgroundColor: '#2E7D32',\n                // Dark green\n                position: 'relative',\n                zIndex: 1,\n                '&::after': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '1px',\n                  height: '100%',\n                  backgroundColor: 'rgba(0,0,0,0.1)'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 15\n          }, this), idleMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Idle: ${idleMinutes < 60 ? `${idleMinutes}m` : `${Math.floor(idleMinutes / 60)}h ${Math.floor(idleMinutes % 60)}m`}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${idlePercent}%`,\n                height: '100%',\n                backgroundColor: '#FFC107',\n                // Yellow\n                position: 'relative',\n                zIndex: 1,\n                '&::after': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  right: 0,\n                  width: '1px',\n                  height: '100%',\n                  backgroundColor: 'rgba(0,0,0,0.1)'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 15\n          }, this), privateMinutes > 0 && /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: `Break: ${privateMinutes < 60 ? `${privateMinutes}m` : `${Math.floor(privateMinutes / 60)}h ${Math.floor(privateMinutes % 60)}m`}`,\n            arrow: true,\n            placement: \"top\",\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: `${privatePercent}%`,\n                height: '100%',\n                backgroundColor: '#F44336',\n                // Red\n                position: 'relative',\n                zIndex: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, \"activity-container\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height: '20px',\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        borderRadius: '5px',\n        overflow: 'hidden',\n        position: 'relative',\n        display: 'flex'\n      },\n      children: [hourDivisions, activityBars]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render empty timeline bar with hour divisions\n  const renderEmptyTimelineBar = () => {\n    const hourDivisions = [];\n    for (let i = 0; i < 24; i++) {\n      // Remove tooltips from empty boxes\n      hourDivisions.push(/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          width: `${100 / 24}%`,\n          height: '100%',\n          borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\n          backgroundColor: 'transparent'\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this));\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        height: '20px',\n        width: '100%',\n        backgroundColor: '#f5f5f5',\n        borderRadius: '5px',\n        overflow: 'hidden',\n        position: 'relative',\n        display: 'flex'\n      },\n      children: hourDivisions\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 379,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      width: '100%',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: '100%',\n        overflowX: 'auto',\n        overflowY: 'hidden',\n        position: 'relative',\n        border: '1px solid #e0e0e0',\n        borderRadius: '4px',\n        paddingBottom: '16px',\n        // Add padding to ensure scrollbar is visible\n        WebkitOverflowScrolling: 'touch' // Smooth scrolling on iOS\n      },\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        size: \"small\",\n        sx: {\n          minWidth: displayData.length > 1 ? '1500px' : '100%',\n          // Force minimum width\n          tableLayout: 'fixed',\n          bgcolor: 'background.paper',\n          borderCollapse: 'separate'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#f5f5f5'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                width: '180px',\n                minWidth: '180px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 2,\n                backgroundColor: '#f5f5f5'\n              },\n              children: \"Date\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => {\n              // Extract day and date for better formatting\n              const dateOnly = formatDate(day.date);\n              const dayOfWeek = getDayOfWeek(day.date);\n              return /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                sx: {\n                  fontWeight: 'bold'\n                },\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  children: [/*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"body2\",\n                    children: dateOnly\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 437,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    variant: \"caption\",\n                    color: \"text.secondary\",\n                    children: dayOfWeek\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 438,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 436,\n                  columnNumber: 21\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 435,\n                columnNumber: 19\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: [/*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                padding: '8px 16px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Timeline\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                padding: '8px 16px'\n              },\n              children: renderTimelineBar(day)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                padding: '8px 16px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: 'white'\n              },\n              children: \"At Work\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => {\n              // Parse the date to determine status\n              const dayDate = dayjs(day.date, 'DD-MM-YYYY HH:mm:ss');\n              const today = dayjs();\n              const isFuture = dayDate.isAfter(today, 'day');\n              const isPast = dayDate.isBefore(today, 'day');\n              const isSunday = dayDate.day() === 0;\n              const hasNoActivity = day.atwork === \"--\" && day.clockin === \"--\";\n              return /*#__PURE__*/_jsxDEV(TableCell, {\n                align: \"center\",\n                sx: {\n                  padding: '8px 16px'\n                },\n                children: (() => {\n                  if (isSunday) {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"error.main\",\n                      children: \"Holiday\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 32\n                    }, this);\n                  } else if (isFuture) {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"text.secondary\",\n                      children: \"--:--\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 488,\n                      columnNumber: 32\n                    }, this);\n                  } else if (isPast && hasNoActivity) {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"warning.main\",\n                      children: \"Absent\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 490,\n                      columnNumber: 32\n                    }, this);\n                  } else if (day.atwork !== \"--\") {\n                    return /*#__PURE__*/_jsxDEV(Typography, {\n                      variant: \"body2\",\n                      color: \"success.main\",\n                      children: day.atwork\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 32\n                    }, this);\n                  } else {\n                    return \"-\";\n                  }\n                })()\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 483,\n                columnNumber: 19\n              }, this);\n            })]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                padding: '8px 16px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Productivity Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                padding: '8px 16px'\n              },\n              children: day.productivitytime !== \"--\" ? day.productivitytime : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 513,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                padding: '8px 16px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: 'white'\n              },\n              children: \"Idle Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 521,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                padding: '8px 16px'\n              },\n              children: day.idletime !== \"--\" ? day.idletime : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 530,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 520,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                padding: '8px 16px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Private Time\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 538,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                padding: '8px 16px'\n              },\n              children: day.privatetime !== \"--\" ? day.privatetime : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 547,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                padding: '8px 16px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: 'white'\n              },\n              children: \"Clock In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                padding: '8px 16px'\n              },\n              children: day.clockin !== \"--\" ? day.clockin : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 564,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: '#fafafa'\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              sx: {\n                fontWeight: 'bold',\n                padding: '8px 16px',\n                position: 'sticky',\n                left: 0,\n                zIndex: 1,\n                backgroundColor: '#fafafa'\n              },\n              children: \"Clock Out\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this), displayData.map((day, index) => /*#__PURE__*/_jsxDEV(TableCell, {\n              align: \"center\",\n              sx: {\n                padding: '8px 16px'\n              },\n              children: day.clockout && day.clockout !== \"--\" ? day.clockout : \"-\"\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), displayData.length > 1 && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'center',\n        mt: 1,\n        color: 'text.secondary',\n        fontSize: '0.75rem'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 394,\n    columnNumber: 5\n  }, this);\n};\n_c = DayView;\nDayView.propTypes = {\n  data: PropTypes.object,\n  multiDay: PropTypes.bool,\n  dataArray: PropTypes.array\n};\nexport default DayView;\nvar _c;\n$RefreshReg$(_c, \"DayView\");", "map": {"version": 3, "names": ["React", "PropTypes", "dayjs", "Box", "Typography", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Paper", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "data", "multiDay", "dataArray", "length", "sx", "display", "justifyContent", "alignItems", "height", "children", "variant", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "displayData", "Array", "isArray", "console", "log", "dataLength", "firstItem", "formatDate", "dateString", "parts", "split", "getDayOfWeek", "renderTimelineBar", "row", "atwork", "renderEmptyTimelineBar", "clockin", "clockout", "productivity", "idle", "private", "MIN_ACTIVITY_MINUTES", "parseTime", "timeStr", "match", "hours", "parseInt", "minutes", "parseClockTime", "time", "period", "map", "Number", "hour", "clockIn", "clockOut", "startHour", "endHour", "formatTimeForTooltip", "Math", "floor", "mins", "hourDivisions", "i", "isActive", "hourLabel", "push", "title", "arrow", "placement", "width", "borderRight", "backgroundColor", "position", "atWorkMinutes", "productivityMinutes", "productivitytime", "idleMinutes", "idletime", "privateMinutes", "privatetime", "rawData", "activityBars", "startPercent", "endPercent", "min", "totalActiveMinutes", "minVisibilityPercent", "productivityPercent", "idlePercent", "privatePercent", "newTotalPercent", "scaleFactor", "left", "top", "overflow", "borderRadius", "border", "zIndex", "content", "right", "style", "overflowX", "overflowY", "paddingBottom", "WebkitOverflowScrolling", "size", "min<PERSON><PERSON><PERSON>", "tableLayout", "bgcolor", "borderCollapse", "fontWeight", "day", "index", "dateOnly", "date", "dayOfWeek", "align", "padding", "dayDate", "today", "isFuture", "isAfter", "isPast", "isBefore", "is<PERSON><PERSON><PERSON>", "hasNoActivity", "mt", "fontSize", "_c", "propTypes", "object", "bool", "array", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/Timeline/components/DayView.jsx"], "sourcesContent": ["import React from 'react';\r\nimport PropTypes from 'prop-types';\r\nimport dayjs from 'dayjs';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Paper,\r\n  Tooltip\r\n} from '@mui/material';\r\n\r\nconst DayView = ({ data, multiDay = false, dataArray = [] }) => {\r\n  // If no data is provided, show a message\r\n  if ((!data && !multiDay) || (multiDay && (!dataArray || dataArray.length === 0))) {\r\n    return (\r\n      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>\r\n        <Typography variant=\"h6\" color=\"text.secondary\">No data available for the selected date range</Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  // If multiDay is true, use dataArray for rendering multiple days\r\n  // Make sure we have a valid array to work with\r\n  let displayData = [data];\r\n  if (multiDay) {\r\n    displayData = Array.isArray(dataArray) ? dataArray : [];\r\n  }\r\n\r\n  // Log the data we're working with for debugging\r\n  console.log(\"DayView rendering with data:\", {\r\n    multiDay,\r\n    dataLength: displayData.length,\r\n    firstItem: displayData.length > 0 ? displayData[0] : null\r\n  });\r\n\r\n  // Format date for display\r\n  const formatDate = (dateString) => {\r\n    if (!dateString) { return ''; }\r\n    const parts = dateString.split(' ');\r\n    return parts.length > 0 ? parts[0] : '';\r\n  };\r\n\r\n  // Extract day of week from date string\r\n  const getDayOfWeek = (dateString) => {\r\n    if (!dateString) { return ''; }\r\n    const parts = dateString.split(' ');\r\n    return parts.length > 1 ? parts[parts.length - 1] : '';\r\n  };\r\n\r\n  // Function to render detailed timeline bar with hour divisions\r\n  const renderTimelineBar = (row) => {\r\n    // Debug: Log the row data to understand the structure\r\n    console.log(\"🔍 DayView Timeline Bar - Row data:\", row);\r\n\r\n    // If row is undefined or has no work time, show empty bar with hour divisions\r\n    if (!row || !row.atwork || row.atwork === \"--\") {\r\n      console.log(\"❌ DayView Timeline Bar - No work data, showing empty bar\");\r\n      return renderEmptyTimelineBar();\r\n    }\r\n\r\n    console.log(\"✅ DayView Timeline Bar - Processing work data:\", {\r\n      atwork: row.atwork,\r\n      clockin: row.clockin,\r\n      clockout: row.clockout,\r\n      productivity: row.productivity,\r\n      idle: row.idle,\r\n      private: row.private\r\n    });\r\n\r\n    // Ensure all days show similar working details by setting minimum values for activity types\r\n    // This ensures that even if a day has 0 idle or break time, it will still show those segments\r\n    const MIN_ACTIVITY_MINUTES = 1; // Minimum minutes to ensure visibility\r\n\r\n    // Parse time values - handle both numbers and formatted strings\r\n    const parseTime = (timeStr) => {\r\n      if (!timeStr || timeStr === \"--\") { return 0; }\r\n\r\n      // If it's already a number, return it\r\n      if (typeof timeStr === 'number') { return timeStr; }\r\n\r\n      // If it's a string, try to parse it\r\n      if (typeof timeStr === 'string') {\r\n        // Handle formatted strings like \"2h 30m\" or \"45m\"\r\n        const match = timeStr.match(/(?:(\\d+)h\\s*)?(?:(\\d+)m)?/);\r\n        if (match) {\r\n          const hours = parseInt(match[1], 10) || 0;\r\n          const minutes = parseInt(match[2], 10) || 0;\r\n          return (hours * 60) + minutes; // Return total minutes\r\n        }\r\n      }\r\n\r\n      return 0;\r\n    };\r\n\r\n    // Parse clock in/out times to determine active period\r\n    const parseClockTime = (timeStr) => {\r\n      if (!timeStr || timeStr === \"--\") { return null; }\r\n      const [time, period] = timeStr.split(' ');\r\n      const [hours, minutes] = time.split(':').map(Number);\r\n      let hour = hours;\r\n      if (period === 'PM' && hours !== 12) {\r\n        hour += 12;\r\n      } else if (period === 'AM' && hours === 12) {\r\n        hour = 0;\r\n      }\r\n      return { hour, minutes };\r\n    };\r\n\r\n    // Get clock in/out times\r\n    const clockIn = parseClockTime(row.clockin);\r\n    const clockOut = parseClockTime(row.clockout);\r\n\r\n    // Calculate start and end hours for the active period\r\n    const startHour = clockIn ? clockIn.hour : 9; // Default to 9 AM if no clock in\r\n    const endHour = clockOut ? clockOut.hour : 17; // Default to 5 PM if no clock out\r\n\r\n    // Format time for tooltips\r\n    const formatTimeForTooltip = (minutes) => {\r\n      if (minutes === 0) {\r\n        return \"0m\";\r\n      }\r\n      const hours = Math.floor(minutes / 60);\r\n      const mins = minutes % 60;\r\n      return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;\r\n    };\r\n\r\n    // Create hour divisions for the timeline\r\n    const hourDivisions = [];\r\n    for (let i = 0; i < 24; i++) {\r\n      const isActive = i >= startHour && i <= endHour;\r\n\r\n      // Only show tooltips for active hours, remove tooltips from inactive areas\r\n      if (isActive) {\r\n        const hourLabel = i < 12 ? `${i === 0 ? 12 : i}AM` : `${i === 12 ? 12 : i - 12}PM`;\r\n        hourDivisions.push(\r\n          <Tooltip key={i} title={hourLabel} arrow placement=\"top\">\r\n            <Box sx={{\r\n              width: `${100/24}%`,\r\n              height: '100%',\r\n              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\r\n              backgroundColor: 'rgba(0,0,0,0.03)',\r\n              position: 'relative'\r\n            }} />\r\n          </Tooltip>\r\n        );\r\n      } else {\r\n        hourDivisions.push(\r\n          <Box\r\n            key={i}\r\n            sx={{\r\n              width: `${100/24}%`,\r\n              height: '100%',\r\n              borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\r\n              backgroundColor: 'transparent',\r\n              position: 'relative'\r\n            }}\r\n          />\r\n        );\r\n      }\r\n    }\r\n\r\n    // Parse activity data - use correct field names from TimelineNew.jsx\r\n    const atWorkMinutes = parseTime(row.atwork);\r\n\r\n    // Ensure minimum values for all activity types to make them visible\r\n    // This ensures all days show similar working details\r\n    let productivityMinutes = parseTime(row.productivitytime);\r\n    let idleMinutes = parseTime(row.idletime);\r\n    let privateMinutes = parseTime(row.privatetime);\r\n\r\n    console.log(\"📊 DayView Timeline Bar - Parsed minutes:\", {\r\n      atWorkMinutes,\r\n      productivityMinutes,\r\n      idleMinutes,\r\n      privateMinutes,\r\n      rawData: {\r\n        productivitytime: row.productivitytime,\r\n        idletime: row.idletime,\r\n        privatetime: row.privatetime\r\n      }\r\n    });\r\n\r\n    // If we have work time but no activity breakdown, ensure minimum values\r\n    if (atWorkMinutes > 0) {\r\n      // Ensure at least some productivity time is shown\r\n      if (productivityMinutes === 0) {\r\n        productivityMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n\r\n      // Ensure at least some idle time is shown\r\n      if (idleMinutes === 0) {\r\n        idleMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n\r\n      // Ensure at least some break time is shown\r\n      if (privateMinutes === 0) {\r\n        privateMinutes = MIN_ACTIVITY_MINUTES;\r\n      }\r\n    }\r\n\r\n    // Calculate activity bars\r\n    const activityBars = [];\r\n\r\n    // Only add activity bars if we have clock in time\r\n    if (clockIn) {\r\n      // Calculate position and width based on clock in/out times\r\n      const startPercent = ((clockIn.hour * 60) + clockIn.minutes) / ((24 * 60)) * 100;\r\n      const endPercent = clockOut ? ((clockOut.hour * 60) + clockOut.minutes) / ((24 * 60)) * 100 : Math.min(100, startPercent + ((atWorkMinutes / ((24 * 60))) * 100));\r\n      const width = endPercent - startPercent;\r\n\r\n      // Calculate activity percentages\r\n      const totalActiveMinutes = atWorkMinutes;\r\n\r\n      // Ensure minimum visibility for each activity type if it exists\r\n      const minVisibilityPercent = 5; // Minimum 5% visibility for any activity type that exists\r\n\r\n      // Calculate initial percentages\r\n      let productivityPercent = totalActiveMinutes > 0 ? (productivityMinutes / totalActiveMinutes) * 100 : 0;\r\n      let idlePercent = totalActiveMinutes > 0 ? (idleMinutes / totalActiveMinutes) * 100 : 0;\r\n      let privatePercent = totalActiveMinutes > 0 ? (privateMinutes / totalActiveMinutes) * 100 : 0;\r\n\r\n      // Ensure minimum visibility for activities that exist\r\n      if (productivityMinutes > 0 && productivityPercent < minVisibilityPercent) {\r\n        productivityPercent = minVisibilityPercent;\r\n      }\r\n\r\n      if (idleMinutes > 0 && idlePercent < minVisibilityPercent) {\r\n        idlePercent = minVisibilityPercent;\r\n      }\r\n\r\n      if (privateMinutes > 0 && privatePercent < minVisibilityPercent) {\r\n        privatePercent = minVisibilityPercent;\r\n      }\r\n\r\n      // Normalize percentages to ensure they sum to 100%\r\n      const newTotalPercent = productivityPercent + idlePercent + privatePercent;\r\n      if (newTotalPercent > 0) {\r\n        const scaleFactor = 100 / newTotalPercent;\r\n        productivityPercent *= scaleFactor;\r\n        idlePercent *= scaleFactor;\r\n        privatePercent *= scaleFactor;\r\n      }\r\n\r\n      // Add activity bars\r\n      activityBars.push(\r\n        <Box\r\n          key=\"activity-container\"\r\n          sx={{\r\n            position: 'absolute',\r\n            left: `${startPercent}%`,\r\n            top: 0,\r\n            height: '100%',\r\n            width: `${width}%`,\r\n            display: 'flex',\r\n            overflow: 'hidden',\r\n            borderRadius: '3px',\r\n            border: '1px solid rgba(0,0,0,0.1)'\r\n          }}\r\n        >\r\n          {/* Activity segments */}\r\n          <Box sx={{ display: 'flex', width: '100%', height: '100%' }}>\r\n            {/* Time at Work (light green background) - only show if there's work time */}\r\n            {atWorkMinutes > 0 && (\r\n              <Tooltip title={`Time at Work: ${atWorkMinutes < 60 ? `${atWorkMinutes}m` : `${Math.floor(atWorkMinutes/60)}h ${Math.floor(atWorkMinutes%60)}m`}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  position: 'absolute',\r\n                  width: '100%',\r\n                  height: '100%',\r\n                  backgroundColor: '#E8F5E9', // Light green background for total work time\r\n                  zIndex: 0\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Productivity time (dark green) */}\r\n            {productivityMinutes > 0 && (\r\n              <Tooltip title={`Productive: ${productivityMinutes < 60 ? `${productivityMinutes}m` : `${Math.floor(productivityMinutes/60)}h ${Math.floor(productivityMinutes%60)}m`}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${productivityPercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#2E7D32', // Dark green\r\n                  position: 'relative',\r\n                  zIndex: 1,\r\n                  '&::after': {\r\n                    content: '\"\"',\r\n                    position: 'absolute',\r\n                    top: 0,\r\n                    right: 0,\r\n                    width: '1px',\r\n                    height: '100%',\r\n                    backgroundColor: 'rgba(0,0,0,0.1)'\r\n                  }\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Idle time (yellow) */}\r\n            {idleMinutes > 0 && (\r\n              <Tooltip title={`Idle: ${idleMinutes < 60 ? `${idleMinutes}m` : `${Math.floor(idleMinutes/60)}h ${Math.floor(idleMinutes%60)}m`}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${idlePercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#FFC107', // Yellow\r\n                  position: 'relative',\r\n                  zIndex: 1,\r\n                  '&::after': {\r\n                    content: '\"\"',\r\n                    position: 'absolute',\r\n                    top: 0,\r\n                    right: 0,\r\n                    width: '1px',\r\n                    height: '100%',\r\n                    backgroundColor: 'rgba(0,0,0,0.1)'\r\n                  }\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n\r\n            {/* Private/break time (red) */}\r\n            {privateMinutes > 0 && (\r\n              <Tooltip title={`Break: ${privateMinutes < 60 ? `${privateMinutes}m` : `${Math.floor(privateMinutes/60)}h ${Math.floor(privateMinutes%60)}m`}`} arrow placement=\"top\">\r\n                <Box sx={{\r\n                  width: `${privatePercent}%`,\r\n                  height: '100%',\r\n                  backgroundColor: '#F44336', // Red\r\n                  position: 'relative',\r\n                  zIndex: 1\r\n                }} />\r\n              </Tooltip>\r\n            )}\r\n          </Box>\r\n        </Box>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{\r\n        height: '20px',\r\n        width: '100%',\r\n        backgroundColor: '#f5f5f5',\r\n        borderRadius: '5px',\r\n        overflow: 'hidden',\r\n        position: 'relative',\r\n        display: 'flex'\r\n      }}>\r\n        {/* Hour divisions */}\r\n        {hourDivisions}\r\n\r\n        {/* Activity bars */}\r\n        {activityBars}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  // Render empty timeline bar with hour divisions\r\n  const renderEmptyTimelineBar = () => {\r\n    const hourDivisions = [];\r\n    for (let i = 0; i < 24; i++) {\r\n      // Remove tooltips from empty boxes\r\n      hourDivisions.push(\r\n        <Box\r\n          key={i}\r\n          sx={{\r\n            width: `${100/24}%`,\r\n            height: '100%',\r\n            borderRight: i < 23 ? '1px solid rgba(0,0,0,0.1)' : 'none',\r\n            backgroundColor: 'transparent'\r\n          }}\r\n        />\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box sx={{\r\n        height: '20px',\r\n        width: '100%',\r\n        backgroundColor: '#f5f5f5',\r\n        borderRadius: '5px',\r\n        overflow: 'hidden',\r\n        position: 'relative',\r\n        display: 'flex'\r\n      }}>\r\n        {hourDivisions}\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box sx={{\r\n      width: '100%',\r\n      position: 'relative',\r\n    }}>\r\n      {/* Wrapper div with explicit overflow-x to ensure scrollbar appears */}\r\n      <div style={{\r\n        width: '100%',\r\n        overflowX: 'auto',\r\n        overflowY: 'hidden',\r\n        position: 'relative',\r\n        border: '1px solid #e0e0e0',\r\n        borderRadius: '4px',\r\n        paddingBottom: '16px', // Add padding to ensure scrollbar is visible\r\n        WebkitOverflowScrolling: 'touch', // Smooth scrolling on iOS\r\n      }}>\r\n        {/* Table without container to avoid nested scrollable elements */}\r\n        <Table\r\n          size=\"small\"\r\n          sx={{\r\n            minWidth: displayData.length > 1 ? '1500px' : '100%', // Force minimum width\r\n            tableLayout: 'fixed',\r\n            bgcolor: 'background.paper',\r\n            borderCollapse: 'separate',\r\n          }}>\r\n          <TableHead>\r\n            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                width: '180px',\r\n                minWidth: '180px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 2,\r\n                backgroundColor: '#f5f5f5'\r\n              }}>Date</TableCell>\r\n              {displayData.map((day, index) => {\r\n                // Extract day and date for better formatting\r\n                const dateOnly = formatDate(day.date);\r\n                const dayOfWeek = getDayOfWeek(day.date);\r\n\r\n                return (\r\n                  <TableCell key={index} align=\"center\" sx={{ fontWeight: 'bold' }}>\r\n                    <Box>\r\n                      <Typography variant=\"body2\">{dateOnly}</Typography>\r\n                      <Typography variant=\"caption\" color=\"text.secondary\">{dayOfWeek}</Typography>\r\n                    </Box>\r\n                  </TableCell>\r\n                );\r\n              })}\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {/* Timeline row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                padding: '8px 16px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 1,\r\n                backgroundColor: '#fafafa'\r\n              }}>Timeline</TableCell>\r\n              {displayData.map((day, index) => (\r\n                <TableCell key={index} sx={{ padding: '8px 16px' }}>\r\n                  {renderTimelineBar(day)}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* At Work row */}\r\n            <TableRow>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                padding: '8px 16px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 1,\r\n                backgroundColor: 'white'\r\n              }}>At Work</TableCell>\r\n              {displayData.map((day, index) => {\r\n                // Parse the date to determine status\r\n                const dayDate = dayjs(day.date, 'DD-MM-YYYY HH:mm:ss');\r\n                const today = dayjs();\r\n                const isFuture = dayDate.isAfter(today, 'day');\r\n                const isPast = dayDate.isBefore(today, 'day');\r\n                const isSunday = dayDate.day() === 0;\r\n                const hasNoActivity = day.atwork === \"--\" && day.clockin === \"--\";\r\n\r\n                return (\r\n                  <TableCell key={index} align=\"center\" sx={{ padding: '8px 16px' }}>\r\n                    {(() => {\r\n                      if (isSunday) {\r\n                        return <Typography variant=\"body2\" color=\"error.main\">Holiday</Typography>;\r\n                      } else if (isFuture) {\r\n                        return <Typography variant=\"body2\" color=\"text.secondary\">--:--</Typography>;\r\n                      } else if (isPast && hasNoActivity) {\r\n                        return <Typography variant=\"body2\" color=\"warning.main\">Absent</Typography>;\r\n                      } else if (day.atwork !== \"--\") {\r\n                        return <Typography variant=\"body2\" color=\"success.main\">{day.atwork}</Typography>;\r\n                      } else {\r\n                        return \"-\";\r\n                      }\r\n                    })()}\r\n                  </TableCell>\r\n                );\r\n              })}\r\n            </TableRow>\r\n\r\n            {/* Productivity Time row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                padding: '8px 16px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 1,\r\n                backgroundColor: '#fafafa'\r\n              }}>Productivity Time</TableCell>\r\n              {displayData.map((day, index) => (\r\n                <TableCell key={index} align=\"center\" sx={{ padding: '8px 16px' }}>\r\n                  {day.productivitytime !== \"--\" ? day.productivitytime : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Idle Time row */}\r\n            <TableRow>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                padding: '8px 16px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 1,\r\n                backgroundColor: 'white'\r\n              }}>Idle Time</TableCell>\r\n              {displayData.map((day, index) => (\r\n                <TableCell key={index} align=\"center\" sx={{ padding: '8px 16px' }}>\r\n                  {day.idletime !== \"--\" ? day.idletime : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Private Time row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                padding: '8px 16px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 1,\r\n                backgroundColor: '#fafafa'\r\n              }}>Private Time</TableCell>\r\n              {displayData.map((day, index) => (\r\n                <TableCell key={index} align=\"center\" sx={{ padding: '8px 16px' }}>\r\n                  {day.privatetime !== \"--\" ? day.privatetime : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Clock In row */}\r\n            <TableRow>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                padding: '8px 16px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 1,\r\n                backgroundColor: 'white'\r\n              }}>Clock In</TableCell>\r\n              {displayData.map((day, index) => (\r\n                <TableCell key={index} align=\"center\" sx={{ padding: '8px 16px' }}>\r\n                  {day.clockin !== \"--\" ? day.clockin : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n\r\n            {/* Clock Out row */}\r\n            <TableRow sx={{ backgroundColor: '#fafafa' }}>\r\n              <TableCell sx={{\r\n                fontWeight: 'bold',\r\n                padding: '8px 16px',\r\n                position: 'sticky',\r\n                left: 0,\r\n                zIndex: 1,\r\n                backgroundColor: '#fafafa'\r\n              }}>Clock Out</TableCell>\r\n              {displayData.map((day, index) => (\r\n                <TableCell key={index} align=\"center\" sx={{ padding: '8px 16px' }}>\r\n                  {day.clockout && day.clockout !== \"--\" ? day.clockout : \"-\"}\r\n                </TableCell>\r\n              ))}\r\n            </TableRow>\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n      {/* Scrollbar indicator */}\r\n      {displayData.length > 1 && (\r\n        <Box sx={{\r\n          display: 'flex',\r\n          justifyContent: 'center',\r\n          mt: 1,\r\n          color: 'text.secondary',\r\n          fontSize: '0.75rem'\r\n        }}>\r\n\r\n        </Box>\r\n      )}\r\n    </Box>\r\n  );\r\n};\r\n\r\nDayView.propTypes = {\r\n  data: PropTypes.object,\r\n  multiDay: PropTypes.bool,\r\n  dataArray: PropTypes.array\r\n};\r\n\r\nexport default DayView;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,KAAK,EACLC,OAAO,QACF,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ,GAAG,KAAK;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAC9D;EACA,IAAK,CAACF,IAAI,IAAI,CAACC,QAAQ,IAAMA,QAAQ,KAAK,CAACC,SAAS,IAAIA,SAAS,CAACC,MAAM,KAAK,CAAC,CAAE,EAAE;IAChF,oBACEL,OAAA,CAACX,GAAG;MAACiB,EAAE,EAAE;QAAEC,OAAO,EAAE,MAAM;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAQ,CAAE;MAAAC,QAAA,eAC5FX,OAAA,CAACV,UAAU;QAACsB,OAAO,EAAC,IAAI;QAACC,KAAK,EAAC,gBAAgB;QAAAF,QAAA,EAAC;MAA6C;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG,CAAC;EAEV;;EAEA;EACA;EACA,IAAIC,WAAW,GAAG,CAAChB,IAAI,CAAC;EACxB,IAAIC,QAAQ,EAAE;IACZe,WAAW,GAAGC,KAAK,CAACC,OAAO,CAAChB,SAAS,CAAC,GAAGA,SAAS,GAAG,EAAE;EACzD;;EAEA;EACAiB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;IAC1CnB,QAAQ;IACRoB,UAAU,EAAEL,WAAW,CAACb,MAAM;IAC9BmB,SAAS,EAAEN,WAAW,CAACb,MAAM,GAAG,CAAC,GAAGa,WAAW,CAAC,CAAC,CAAC,GAAG;EACvD,CAAC,CAAC;;EAEF;EACA,MAAMO,UAAU,GAAIC,UAAU,IAAK;IACjC,IAAI,CAACA,UAAU,EAAE;MAAE,OAAO,EAAE;IAAE;IAC9B,MAAMC,KAAK,GAAGD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC;IACnC,OAAOD,KAAK,CAACtB,MAAM,GAAG,CAAC,GAAGsB,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE;EACzC,CAAC;;EAED;EACA,MAAME,YAAY,GAAIH,UAAU,IAAK;IACnC,IAAI,CAACA,UAAU,EAAE;MAAE,OAAO,EAAE;IAAE;IAC9B,MAAMC,KAAK,GAAGD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC;IACnC,OAAOD,KAAK,CAACtB,MAAM,GAAG,CAAC,GAAGsB,KAAK,CAACA,KAAK,CAACtB,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;EACxD,CAAC;;EAED;EACA,MAAMyB,iBAAiB,GAAIC,GAAG,IAAK;IACjC;IACAV,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAES,GAAG,CAAC;;IAEvD;IACA,IAAI,CAACA,GAAG,IAAI,CAACA,GAAG,CAACC,MAAM,IAAID,GAAG,CAACC,MAAM,KAAK,IAAI,EAAE;MAC9CX,OAAO,CAACC,GAAG,CAAC,0DAA0D,CAAC;MACvE,OAAOW,sBAAsB,CAAC,CAAC;IACjC;IAEAZ,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;MAC5DU,MAAM,EAAED,GAAG,CAACC,MAAM;MAClBE,OAAO,EAAEH,GAAG,CAACG,OAAO;MACpBC,QAAQ,EAAEJ,GAAG,CAACI,QAAQ;MACtBC,YAAY,EAAEL,GAAG,CAACK,YAAY;MAC9BC,IAAI,EAAEN,GAAG,CAACM,IAAI;MACdC,OAAO,EAAEP,GAAG,CAACO;IACf,CAAC,CAAC;;IAEF;IACA;IACA,MAAMC,oBAAoB,GAAG,CAAC,CAAC,CAAC;;IAEhC;IACA,MAAMC,SAAS,GAAIC,OAAO,IAAK;MAC7B,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;QAAE,OAAO,CAAC;MAAE;;MAE9C;MACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAAE,OAAOA,OAAO;MAAE;;MAEnD;MACA,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;QAC/B;QACA,MAAMC,KAAK,GAAGD,OAAO,CAACC,KAAK,CAAC,2BAA2B,CAAC;QACxD,IAAIA,KAAK,EAAE;UACT,MAAMC,KAAK,GAAGC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;UACzC,MAAMG,OAAO,GAAGD,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;UAC3C,OAAQC,KAAK,GAAG,EAAE,GAAIE,OAAO,CAAC,CAAC;QACjC;MACF;MAEA,OAAO,CAAC;IACV,CAAC;;IAED;IACA,MAAMC,cAAc,GAAIL,OAAO,IAAK;MAClC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,IAAI,EAAE;QAAE,OAAO,IAAI;MAAE;MACjD,MAAM,CAACM,IAAI,EAAEC,MAAM,CAAC,GAAGP,OAAO,CAACb,KAAK,CAAC,GAAG,CAAC;MACzC,MAAM,CAACe,KAAK,EAAEE,OAAO,CAAC,GAAGE,IAAI,CAACnB,KAAK,CAAC,GAAG,CAAC,CAACqB,GAAG,CAACC,MAAM,CAAC;MACpD,IAAIC,IAAI,GAAGR,KAAK;MAChB,IAAIK,MAAM,KAAK,IAAI,IAAIL,KAAK,KAAK,EAAE,EAAE;QACnCQ,IAAI,IAAI,EAAE;MACZ,CAAC,MAAM,IAAIH,MAAM,KAAK,IAAI,IAAIL,KAAK,KAAK,EAAE,EAAE;QAC1CQ,IAAI,GAAG,CAAC;MACV;MACA,OAAO;QAAEA,IAAI;QAAEN;MAAQ,CAAC;IAC1B,CAAC;;IAED;IACA,MAAMO,OAAO,GAAGN,cAAc,CAACf,GAAG,CAACG,OAAO,CAAC;IAC3C,MAAMmB,QAAQ,GAAGP,cAAc,CAACf,GAAG,CAACI,QAAQ,CAAC;;IAE7C;IACA,MAAMmB,SAAS,GAAGF,OAAO,GAAGA,OAAO,CAACD,IAAI,GAAG,CAAC,CAAC,CAAC;IAC9C,MAAMI,OAAO,GAAGF,QAAQ,GAAGA,QAAQ,CAACF,IAAI,GAAG,EAAE,CAAC,CAAC;;IAE/C;IACA,MAAMK,oBAAoB,GAAIX,OAAO,IAAK;MACxC,IAAIA,OAAO,KAAK,CAAC,EAAE;QACjB,OAAO,IAAI;MACb;MACA,MAAMF,KAAK,GAAGc,IAAI,CAACC,KAAK,CAACb,OAAO,GAAG,EAAE,CAAC;MACtC,MAAMc,IAAI,GAAGd,OAAO,GAAG,EAAE;MACzB,OAAOF,KAAK,GAAG,CAAC,GAAG,GAAGA,KAAK,KAAKgB,IAAI,GAAG,GAAG,GAAGA,IAAI,GAAG;IACtD,CAAC;;IAED;IACA,MAAMC,aAAa,GAAG,EAAE;IACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B,MAAMC,QAAQ,GAAGD,CAAC,IAAIP,SAAS,IAAIO,CAAC,IAAIN,OAAO;;MAE/C;MACA,IAAIO,QAAQ,EAAE;QACZ,MAAMC,SAAS,GAAGF,CAAC,GAAG,EAAE,GAAG,GAAGA,CAAC,KAAK,CAAC,GAAG,EAAE,GAAGA,CAAC,IAAI,GAAG,GAAGA,CAAC,KAAK,EAAE,GAAG,EAAE,GAAGA,CAAC,GAAG,EAAE,IAAI;QAClFD,aAAa,CAACI,IAAI,cAChBhE,OAAA,CAACF,OAAO;UAASmE,KAAK,EAAEF,SAAU;UAACG,KAAK;UAACC,SAAS,EAAC,KAAK;UAAAxD,QAAA,eACtDX,OAAA,CAACX,GAAG;YAACiB,EAAE,EAAE;cACP8D,KAAK,EAAE,GAAG,GAAG,GAAC,EAAE,GAAG;cACnB1D,MAAM,EAAE,MAAM;cACd2D,WAAW,EAAER,CAAC,GAAG,EAAE,GAAG,2BAA2B,GAAG,MAAM;cAC1DS,eAAe,EAAE,kBAAkB;cACnCC,QAAQ,EAAE;YACZ;UAAE;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC,GAPO4C,CAAC;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQN,CACX,CAAC;MACH,CAAC,MAAM;QACL2C,aAAa,CAACI,IAAI,cAChBhE,OAAA,CAACX,GAAG;UAEFiB,EAAE,EAAE;YACF8D,KAAK,EAAE,GAAG,GAAG,GAAC,EAAE,GAAG;YACnB1D,MAAM,EAAE,MAAM;YACd2D,WAAW,EAAER,CAAC,GAAG,EAAE,GAAG,2BAA2B,GAAG,MAAM;YAC1DS,eAAe,EAAE,aAAa;YAC9BC,QAAQ,EAAE;UACZ;QAAE,GAPGV,CAAC;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQP,CACH,CAAC;MACH;IACF;;IAEA;IACA,MAAMuD,aAAa,GAAGhC,SAAS,CAACT,GAAG,CAACC,MAAM,CAAC;;IAE3C;IACA;IACA,IAAIyC,mBAAmB,GAAGjC,SAAS,CAACT,GAAG,CAAC2C,gBAAgB,CAAC;IACzD,IAAIC,WAAW,GAAGnC,SAAS,CAACT,GAAG,CAAC6C,QAAQ,CAAC;IACzC,IAAIC,cAAc,GAAGrC,SAAS,CAACT,GAAG,CAAC+C,WAAW,CAAC;IAE/CzD,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE;MACvDkD,aAAa;MACbC,mBAAmB;MACnBE,WAAW;MACXE,cAAc;MACdE,OAAO,EAAE;QACPL,gBAAgB,EAAE3C,GAAG,CAAC2C,gBAAgB;QACtCE,QAAQ,EAAE7C,GAAG,CAAC6C,QAAQ;QACtBE,WAAW,EAAE/C,GAAG,CAAC+C;MACnB;IACF,CAAC,CAAC;;IAEF;IACA,IAAIN,aAAa,GAAG,CAAC,EAAE;MACrB;MACA,IAAIC,mBAAmB,KAAK,CAAC,EAAE;QAC7BA,mBAAmB,GAAGlC,oBAAoB;MAC5C;;MAEA;MACA,IAAIoC,WAAW,KAAK,CAAC,EAAE;QACrBA,WAAW,GAAGpC,oBAAoB;MACpC;;MAEA;MACA,IAAIsC,cAAc,KAAK,CAAC,EAAE;QACxBA,cAAc,GAAGtC,oBAAoB;MACvC;IACF;;IAEA;IACA,MAAMyC,YAAY,GAAG,EAAE;;IAEvB;IACA,IAAI5B,OAAO,EAAE;MACX;MACA,MAAM6B,YAAY,GAAG,CAAE7B,OAAO,CAACD,IAAI,GAAG,EAAE,GAAIC,OAAO,CAACP,OAAO,KAAM,EAAE,GAAG,EAAE,CAAE,GAAG,GAAG;MAChF,MAAMqC,UAAU,GAAG7B,QAAQ,GAAG,CAAEA,QAAQ,CAACF,IAAI,GAAG,EAAE,GAAIE,QAAQ,CAACR,OAAO,KAAM,EAAE,GAAG,EAAE,CAAE,GAAG,GAAG,GAAGY,IAAI,CAAC0B,GAAG,CAAC,GAAG,EAAEF,YAAY,GAAKT,aAAa,IAAK,EAAE,GAAG,EAAE,CAAE,GAAI,GAAI,CAAC;MACjK,MAAMJ,KAAK,GAAGc,UAAU,GAAGD,YAAY;;MAEvC;MACA,MAAMG,kBAAkB,GAAGZ,aAAa;;MAExC;MACA,MAAMa,oBAAoB,GAAG,CAAC,CAAC,CAAC;;MAEhC;MACA,IAAIC,mBAAmB,GAAGF,kBAAkB,GAAG,CAAC,GAAIX,mBAAmB,GAAGW,kBAAkB,GAAI,GAAG,GAAG,CAAC;MACvG,IAAIG,WAAW,GAAGH,kBAAkB,GAAG,CAAC,GAAIT,WAAW,GAAGS,kBAAkB,GAAI,GAAG,GAAG,CAAC;MACvF,IAAII,cAAc,GAAGJ,kBAAkB,GAAG,CAAC,GAAIP,cAAc,GAAGO,kBAAkB,GAAI,GAAG,GAAG,CAAC;;MAE7F;MACA,IAAIX,mBAAmB,GAAG,CAAC,IAAIa,mBAAmB,GAAGD,oBAAoB,EAAE;QACzEC,mBAAmB,GAAGD,oBAAoB;MAC5C;MAEA,IAAIV,WAAW,GAAG,CAAC,IAAIY,WAAW,GAAGF,oBAAoB,EAAE;QACzDE,WAAW,GAAGF,oBAAoB;MACpC;MAEA,IAAIR,cAAc,GAAG,CAAC,IAAIW,cAAc,GAAGH,oBAAoB,EAAE;QAC/DG,cAAc,GAAGH,oBAAoB;MACvC;;MAEA;MACA,MAAMI,eAAe,GAAGH,mBAAmB,GAAGC,WAAW,GAAGC,cAAc;MAC1E,IAAIC,eAAe,GAAG,CAAC,EAAE;QACvB,MAAMC,WAAW,GAAG,GAAG,GAAGD,eAAe;QACzCH,mBAAmB,IAAII,WAAW;QAClCH,WAAW,IAAIG,WAAW;QAC1BF,cAAc,IAAIE,WAAW;MAC/B;;MAEA;MACAV,YAAY,CAAChB,IAAI,cACfhE,OAAA,CAACX,GAAG;QAEFiB,EAAE,EAAE;UACFiE,QAAQ,EAAE,UAAU;UACpBoB,IAAI,EAAE,GAAGV,YAAY,GAAG;UACxBW,GAAG,EAAE,CAAC;UACNlF,MAAM,EAAE,MAAM;UACd0D,KAAK,EAAE,GAAGA,KAAK,GAAG;UAClB7D,OAAO,EAAE,MAAM;UACfsF,QAAQ,EAAE,QAAQ;UAClBC,YAAY,EAAE,KAAK;UACnBC,MAAM,EAAE;QACV,CAAE;QAAApF,QAAA,eAGFX,OAAA,CAACX,GAAG;UAACiB,EAAE,EAAE;YAAEC,OAAO,EAAE,MAAM;YAAE6D,KAAK,EAAE,MAAM;YAAE1D,MAAM,EAAE;UAAO,CAAE;UAAAC,QAAA,GAEzD6D,aAAa,GAAG,CAAC,iBAChBxE,OAAA,CAACF,OAAO;YAACmE,KAAK,EAAE,iBAAiBO,aAAa,GAAG,EAAE,GAAG,GAAGA,aAAa,GAAG,GAAG,GAAGf,IAAI,CAACC,KAAK,CAACc,aAAa,GAAC,EAAE,CAAC,KAAKf,IAAI,CAACC,KAAK,CAACc,aAAa,GAAC,EAAE,CAAC,GAAG,EAAG;YAACN,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAxD,QAAA,eACtKX,OAAA,CAACX,GAAG;cAACiB,EAAE,EAAE;gBACPiE,QAAQ,EAAE,UAAU;gBACpBH,KAAK,EAAE,MAAM;gBACb1D,MAAM,EAAE,MAAM;gBACd4D,eAAe,EAAE,SAAS;gBAAE;gBAC5B0B,MAAM,EAAE;cACV;YAAE;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGAwD,mBAAmB,GAAG,CAAC,iBACtBzE,OAAA,CAACF,OAAO;YAACmE,KAAK,EAAE,eAAeQ,mBAAmB,GAAG,EAAE,GAAG,GAAGA,mBAAmB,GAAG,GAAG,GAAGhB,IAAI,CAACC,KAAK,CAACe,mBAAmB,GAAC,EAAE,CAAC,KAAKhB,IAAI,CAACC,KAAK,CAACe,mBAAmB,GAAC,EAAE,CAAC,GAAG,EAAG;YAACP,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAxD,QAAA,eAC5LX,OAAA,CAACX,GAAG;cAACiB,EAAE,EAAE;gBACP8D,KAAK,EAAE,GAAGkB,mBAAmB,GAAG;gBAChC5E,MAAM,EAAE,MAAM;gBACd4D,eAAe,EAAE,SAAS;gBAAE;gBAC5BC,QAAQ,EAAE,UAAU;gBACpByB,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE;kBACVC,OAAO,EAAE,IAAI;kBACb1B,QAAQ,EAAE,UAAU;kBACpBqB,GAAG,EAAE,CAAC;kBACNM,KAAK,EAAE,CAAC;kBACR9B,KAAK,EAAE,KAAK;kBACZ1D,MAAM,EAAE,MAAM;kBACd4D,eAAe,EAAE;gBACnB;cACF;YAAE;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGA0D,WAAW,GAAG,CAAC,iBACd3E,OAAA,CAACF,OAAO;YAACmE,KAAK,EAAE,SAASU,WAAW,GAAG,EAAE,GAAG,GAAGA,WAAW,GAAG,GAAG,GAAGlB,IAAI,CAACC,KAAK,CAACiB,WAAW,GAAC,EAAE,CAAC,KAAKlB,IAAI,CAACC,KAAK,CAACiB,WAAW,GAAC,EAAE,CAAC,GAAG,EAAG;YAACT,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAxD,QAAA,eACtJX,OAAA,CAACX,GAAG;cAACiB,EAAE,EAAE;gBACP8D,KAAK,EAAE,GAAGmB,WAAW,GAAG;gBACxB7E,MAAM,EAAE,MAAM;gBACd4D,eAAe,EAAE,SAAS;gBAAE;gBAC5BC,QAAQ,EAAE,UAAU;gBACpByB,MAAM,EAAE,CAAC;gBACT,UAAU,EAAE;kBACVC,OAAO,EAAE,IAAI;kBACb1B,QAAQ,EAAE,UAAU;kBACpBqB,GAAG,EAAE,CAAC;kBACNM,KAAK,EAAE,CAAC;kBACR9B,KAAK,EAAE,KAAK;kBACZ1D,MAAM,EAAE,MAAM;kBACd4D,eAAe,EAAE;gBACnB;cACF;YAAE;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV,EAGA4D,cAAc,GAAG,CAAC,iBACjB7E,OAAA,CAACF,OAAO;YAACmE,KAAK,EAAE,UAAUY,cAAc,GAAG,EAAE,GAAG,GAAGA,cAAc,GAAG,GAAG,GAAGpB,IAAI,CAACC,KAAK,CAACmB,cAAc,GAAC,EAAE,CAAC,KAAKpB,IAAI,CAACC,KAAK,CAACmB,cAAc,GAAC,EAAE,CAAC,GAAG,EAAG;YAACX,KAAK;YAACC,SAAS,EAAC,KAAK;YAAAxD,QAAA,eACnKX,OAAA,CAACX,GAAG;cAACiB,EAAE,EAAE;gBACP8D,KAAK,EAAE,GAAGoB,cAAc,GAAG;gBAC3B9E,MAAM,EAAE,MAAM;gBACd4D,eAAe,EAAE,SAAS;gBAAE;gBAC5BC,QAAQ,EAAE,UAAU;gBACpByB,MAAM,EAAE;cACV;YAAE;cAAAlF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACV;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC,GApFF,oBAAoB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqFrB,CACP,CAAC;IACH;IAEA,oBACEjB,OAAA,CAACX,GAAG;MAACiB,EAAE,EAAE;QACPI,MAAM,EAAE,MAAM;QACd0D,KAAK,EAAE,MAAM;QACbE,eAAe,EAAE,SAAS;QAC1BwB,YAAY,EAAE,KAAK;QACnBD,QAAQ,EAAE,QAAQ;QAClBtB,QAAQ,EAAE,UAAU;QACpBhE,OAAO,EAAE;MACX,CAAE;MAAAI,QAAA,GAECiD,aAAa,EAGboB,YAAY;IAAA;MAAAlE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV,CAAC;;EAED;EACA,MAAMgB,sBAAsB,GAAGA,CAAA,KAAM;IACnC,MAAM2B,aAAa,GAAG,EAAE;IACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;MAC3B;MACAD,aAAa,CAACI,IAAI,cAChBhE,OAAA,CAACX,GAAG;QAEFiB,EAAE,EAAE;UACF8D,KAAK,EAAE,GAAG,GAAG,GAAC,EAAE,GAAG;UACnB1D,MAAM,EAAE,MAAM;UACd2D,WAAW,EAAER,CAAC,GAAG,EAAE,GAAG,2BAA2B,GAAG,MAAM;UAC1DS,eAAe,EAAE;QACnB;MAAE,GANGT,CAAC;QAAA/C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOP,CACH,CAAC;IACH;IAEA,oBACEjB,OAAA,CAACX,GAAG;MAACiB,EAAE,EAAE;QACPI,MAAM,EAAE,MAAM;QACd0D,KAAK,EAAE,MAAM;QACbE,eAAe,EAAE,SAAS;QAC1BwB,YAAY,EAAE,KAAK;QACnBD,QAAQ,EAAE,QAAQ;QAClBtB,QAAQ,EAAE,UAAU;QACpBhE,OAAO,EAAE;MACX,CAAE;MAAAI,QAAA,EACCiD;IAAa;MAAA9C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC;EAEV,CAAC;EAED,oBACEjB,OAAA,CAACX,GAAG;IAACiB,EAAE,EAAE;MACP8D,KAAK,EAAE,MAAM;MACbG,QAAQ,EAAE;IACZ,CAAE;IAAA5D,QAAA,gBAEAX,OAAA;MAAKmG,KAAK,EAAE;QACV/B,KAAK,EAAE,MAAM;QACbgC,SAAS,EAAE,MAAM;QACjBC,SAAS,EAAE,QAAQ;QACnB9B,QAAQ,EAAE,UAAU;QACpBwB,MAAM,EAAE,mBAAmB;QAC3BD,YAAY,EAAE,KAAK;QACnBQ,aAAa,EAAE,MAAM;QAAE;QACvBC,uBAAuB,EAAE,OAAO,CAAE;MACpC,CAAE;MAAA5F,QAAA,eAEAX,OAAA,CAACT,KAAK;QACJiH,IAAI,EAAC,OAAO;QACZlG,EAAE,EAAE;UACFmG,QAAQ,EAAEvF,WAAW,CAACb,MAAM,GAAG,CAAC,GAAG,QAAQ,GAAG,MAAM;UAAE;UACtDqG,WAAW,EAAE,OAAO;UACpBC,OAAO,EAAE,kBAAkB;UAC3BC,cAAc,EAAE;QAClB,CAAE;QAAAjG,QAAA,gBACFX,OAAA,CAACL,SAAS;UAAAgB,QAAA,eACRX,OAAA,CAACJ,QAAQ;YAACU,EAAE,EAAE;cAAEgE,eAAe,EAAE;YAAU,CAAE;YAAA3D,QAAA,gBAC3CX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBzC,KAAK,EAAE,OAAO;gBACdqC,QAAQ,EAAE,OAAO;gBACjBlC,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAClBC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,KAAK;cAC/B;cACA,MAAMC,QAAQ,GAAGvF,UAAU,CAACqF,GAAG,CAACG,IAAI,CAAC;cACrC,MAAMC,SAAS,GAAGrF,YAAY,CAACiF,GAAG,CAACG,IAAI,CAAC;cAExC,oBACEjH,OAAA,CAACP,SAAS;gBAAa0H,KAAK,EAAC,QAAQ;gBAAC7G,EAAE,EAAE;kBAAEuG,UAAU,EAAE;gBAAO,CAAE;gBAAAlG,QAAA,eAC/DX,OAAA,CAACX,GAAG;kBAAAsB,QAAA,gBACFX,OAAA,CAACV,UAAU;oBAACsB,OAAO,EAAC,OAAO;oBAAAD,QAAA,EAAEqG;kBAAQ;oBAAAlG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC,eACnDjB,OAAA,CAACV,UAAU;oBAACsB,OAAO,EAAC,SAAS;oBAACC,KAAK,EAAC,gBAAgB;oBAAAF,QAAA,EAAEuG;kBAAS;oBAAApG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1E;cAAC,GAJQ8F,KAAK;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKV,CAAC;YAEhB,CAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZjB,OAAA,CAACR,SAAS;UAAAmB,QAAA,gBAERX,OAAA,CAACJ,QAAQ;YAACU,EAAE,EAAE;cAAEgE,eAAe,EAAE;YAAU,CAAE;YAAA3D,QAAA,gBAC3CX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBO,OAAO,EAAE,UAAU;gBACnB7C,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACtBC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,kBAC1B/G,OAAA,CAACP,SAAS;cAAaa,EAAE,EAAE;gBAAE8G,OAAO,EAAE;cAAW,CAAE;cAAAzG,QAAA,EAChDmB,iBAAiB,CAACgF,GAAG;YAAC,GADTC,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXjB,OAAA,CAACJ,QAAQ;YAAAe,QAAA,gBACPX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBO,OAAO,EAAE,UAAU;gBACnB7C,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAO;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACrBC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,KAAK;cAC/B;cACA,MAAMM,OAAO,GAAGjI,KAAK,CAAC0H,GAAG,CAACG,IAAI,EAAE,qBAAqB,CAAC;cACtD,MAAMK,KAAK,GAAGlI,KAAK,CAAC,CAAC;cACrB,MAAMmI,QAAQ,GAAGF,OAAO,CAACG,OAAO,CAACF,KAAK,EAAE,KAAK,CAAC;cAC9C,MAAMG,MAAM,GAAGJ,OAAO,CAACK,QAAQ,CAACJ,KAAK,EAAE,KAAK,CAAC;cAC7C,MAAMK,QAAQ,GAAGN,OAAO,CAACP,GAAG,CAAC,CAAC,KAAK,CAAC;cACpC,MAAMc,aAAa,GAAGd,GAAG,CAAC9E,MAAM,KAAK,IAAI,IAAI8E,GAAG,CAAC5E,OAAO,KAAK,IAAI;cAEjE,oBACElC,OAAA,CAACP,SAAS;gBAAa0H,KAAK,EAAC,QAAQ;gBAAC7G,EAAE,EAAE;kBAAE8G,OAAO,EAAE;gBAAW,CAAE;gBAAAzG,QAAA,EAC/D,CAAC,MAAM;kBACN,IAAIgH,QAAQ,EAAE;oBACZ,oBAAO3H,OAAA,CAACV,UAAU;sBAACsB,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,YAAY;sBAAAF,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAC5E,CAAC,MAAM,IAAIsG,QAAQ,EAAE;oBACnB,oBAAOvH,OAAA,CAACV,UAAU;sBAACsB,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,gBAAgB;sBAAAF,QAAA,EAAC;oBAAK;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAC9E,CAAC,MAAM,IAAIwG,MAAM,IAAIG,aAAa,EAAE;oBAClC,oBAAO5H,OAAA,CAACV,UAAU;sBAACsB,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAAC;oBAAM;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAC7E,CAAC,MAAM,IAAI6F,GAAG,CAAC9E,MAAM,KAAK,IAAI,EAAE;oBAC9B,oBAAOhC,OAAA,CAACV,UAAU;sBAACsB,OAAO,EAAC,OAAO;sBAACC,KAAK,EAAC,cAAc;sBAAAF,QAAA,EAAEmG,GAAG,CAAC9E;oBAAM;sBAAAlB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAa,CAAC;kBACnF,CAAC,MAAM;oBACL,OAAO,GAAG;kBACZ;gBACF,CAAC,EAAE;cAAC,GAbU8F,KAAK;gBAAAjG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAcV,CAAC;YAEhB,CAAC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXjB,OAAA,CAACJ,QAAQ;YAACU,EAAE,EAAE;cAAEgE,eAAe,EAAE;YAAU,CAAE;YAAA3D,QAAA,gBAC3CX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBO,OAAO,EAAE,UAAU;gBACnB7C,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAiB;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC/BC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,kBAC1B/G,OAAA,CAACP,SAAS;cAAa0H,KAAK,EAAC,QAAQ;cAAC7G,EAAE,EAAE;gBAAE8G,OAAO,EAAE;cAAW,CAAE;cAAAzG,QAAA,EAC/DmG,GAAG,CAACpC,gBAAgB,KAAK,IAAI,GAAGoC,GAAG,CAACpC,gBAAgB,GAAG;YAAG,GAD7CqC,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXjB,OAAA,CAACJ,QAAQ;YAAAe,QAAA,gBACPX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBO,OAAO,EAAE,UAAU;gBACnB7C,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACvBC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,kBAC1B/G,OAAA,CAACP,SAAS;cAAa0H,KAAK,EAAC,QAAQ;cAAC7G,EAAE,EAAE;gBAAE8G,OAAO,EAAE;cAAW,CAAE;cAAAzG,QAAA,EAC/DmG,GAAG,CAAClC,QAAQ,KAAK,IAAI,GAAGkC,GAAG,CAAClC,QAAQ,GAAG;YAAG,GAD7BmC,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXjB,OAAA,CAACJ,QAAQ;YAACU,EAAE,EAAE;cAAEgE,eAAe,EAAE;YAAU,CAAE;YAAA3D,QAAA,gBAC3CX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBO,OAAO,EAAE,UAAU;gBACnB7C,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAY;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EAC1BC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,kBAC1B/G,OAAA,CAACP,SAAS;cAAa0H,KAAK,EAAC,QAAQ;cAAC7G,EAAE,EAAE;gBAAE8G,OAAO,EAAE;cAAW,CAAE;cAAAzG,QAAA,EAC/DmG,GAAG,CAAChC,WAAW,KAAK,IAAI,GAAGgC,GAAG,CAAChC,WAAW,GAAG;YAAG,GADnCiC,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXjB,OAAA,CAACJ,QAAQ;YAAAe,QAAA,gBACPX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBO,OAAO,EAAE,UAAU;gBACnB7C,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACtBC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,kBAC1B/G,OAAA,CAACP,SAAS;cAAa0H,KAAK,EAAC,QAAQ;cAAC7G,EAAE,EAAE;gBAAE8G,OAAO,EAAE;cAAW,CAAE;cAAAzG,QAAA,EAC/DmG,GAAG,CAAC5E,OAAO,KAAK,IAAI,GAAG4E,GAAG,CAAC5E,OAAO,GAAG;YAAG,GAD3B6E,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAGXjB,OAAA,CAACJ,QAAQ;YAACU,EAAE,EAAE;cAAEgE,eAAe,EAAE;YAAU,CAAE;YAAA3D,QAAA,gBAC3CX,OAAA,CAACP,SAAS;cAACa,EAAE,EAAE;gBACbuG,UAAU,EAAE,MAAM;gBAClBO,OAAO,EAAE,UAAU;gBACnB7C,QAAQ,EAAE,QAAQ;gBAClBoB,IAAI,EAAE,CAAC;gBACPK,MAAM,EAAE,CAAC;gBACT1B,eAAe,EAAE;cACnB,CAAE;cAAA3D,QAAA,EAAC;YAAS;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,EACvBC,WAAW,CAAC+B,GAAG,CAAC,CAAC6D,GAAG,EAAEC,KAAK,kBAC1B/G,OAAA,CAACP,SAAS;cAAa0H,KAAK,EAAC,QAAQ;cAAC7G,EAAE,EAAE;gBAAE8G,OAAO,EAAE;cAAW,CAAE;cAAAzG,QAAA,EAC/DmG,GAAG,CAAC3E,QAAQ,IAAI2E,GAAG,CAAC3E,QAAQ,KAAK,IAAI,GAAG2E,GAAG,CAAC3E,QAAQ,GAAG;YAAG,GAD7C4E,KAAK;cAAAjG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEV,CACZ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAELC,WAAW,CAACb,MAAM,GAAG,CAAC,iBACrBL,OAAA,CAACX,GAAG;MAACiB,EAAE,EAAE;QACPC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBqH,EAAE,EAAE,CAAC;QACLhH,KAAK,EAAE,gBAAgB;QACvBiH,QAAQ,EAAE;MACZ;IAAE;MAAAhH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEG,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC8G,EAAA,GA1kBI9H,OAAO;AA4kBbA,OAAO,CAAC+H,SAAS,GAAG;EAClB9H,IAAI,EAAEf,SAAS,CAAC8I,MAAM;EACtB9H,QAAQ,EAAEhB,SAAS,CAAC+I,IAAI;EACxB9H,SAAS,EAAEjB,SAAS,CAACgJ;AACvB,CAAC;AAED,eAAelI,OAAO;AAAC,IAAA8H,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}