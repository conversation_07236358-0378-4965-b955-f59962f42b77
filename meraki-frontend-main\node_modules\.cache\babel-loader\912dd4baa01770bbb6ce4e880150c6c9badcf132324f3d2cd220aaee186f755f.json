{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const ActivitySlice = createSlice({\n  name: \"Activity\",\n  initialState: {\n    activityArr: [],\n    // For single user activity (legacy)\n    multiUserActivityArr: [] // For multi-user activity data (new)\n  },\n  reducers: {\n    createTodayGoal: () => {},\n    createTodayStatus: () => {},\n    getUserActivitySuccessfull: (state, action) => {\n      var _action$payload;\n      console.log(\"🔍 ActivitySlice - Received payload:\", action.payload);\n      console.log(\"🔍 ActivitySlice - Payload length:\", ((_action$payload = action.payload) === null || _action$payload === void 0 ? void 0 : _action$payload.length) || 0);\n      if (action.payload.length === 0) {\n        state.activityArr = [];\n        state.multiUserActivityArr = [];\n        console.log(\"🔍 ActivitySlice - Empty payload, clearing arrays\");\n      } else {\n        // Check if this is multi-user data vs single-user data\n        // Multi-user data has 'name' and 'email' properties and can have different view structures:\n        // - Day view: has 'clockin', 'clockout', 'atwork' etc.\n        // - Week view: has 'weekData' array and 'total'\n        // - Month view: has 'worked', 'focus', 'productive' etc.\n        // Single-user data has 'user' and 'checkInTime' properties\n        const firstItem = action.payload[0];\n        const isMultiUserData = action.payload.length > 0 && Object.prototype.hasOwnProperty.call(firstItem, 'name') && Object.prototype.hasOwnProperty.call(firstItem, 'email') && !Object.prototype.hasOwnProperty.call(firstItem, 'user');\n        console.log(\"🔍 ActivitySlice - Checking data type - First item:\", firstItem);\n        console.log(\"🔍 ActivitySlice - Is multi-user data:\", isMultiUserData);\n        if (isMultiUserData) {\n          state.multiUserActivityArr = action.payload;\n          console.log(\"🔍 ActivitySlice - REDUCER MULTI-USER ACTIVITY LOG \", action.payload);\n          console.log(\"🔍 ActivitySlice - Multi-user count:\", action.payload.length);\n        } else {\n          state.activityArr = action.payload;\n          console.log(\"🔍 ActivitySlice - REDUCER SINGLE-USER ACTIVITY LOG \", action.payload);\n        }\n      }\n    },\n    getUserActivity: () => {},\n    checkOutStatusUpdate: () => {},\n    breakStartRed: () => {},\n    breakEndRed: () => {},\n    lateCheckIn: () => {},\n    earlyCheckOut: () => {},\n    idelStartRed: () => {},\n    idelEndRed: () => {},\n    productivityStatusRed: () => {},\n    overLimitBreakRed: () => {},\n    eraseActivity: (state = []) => {\n      state.activityArr = [];\n      state.multiUserActivityArr = [];\n    },\n    createTimelineRequest: () => {},\n    updateTimelineRequest: () => {},\n    getTimelineRequests: () => {}\n  }\n});\nexport default ActivitySlice;", "map": {"version": 3, "names": ["createSlice", "ActivitySlice", "name", "initialState", "activityArr", "multiUserActivityArr", "reducers", "createTodayGoal", "createTodayStatus", "getUserActivitySuccessfull", "state", "action", "_action$payload", "console", "log", "payload", "length", "firstItem", "isMultiUserData", "Object", "prototype", "hasOwnProperty", "call", "getUserActivity", "checkOutStatusUpdate", "breakStartRed", "breakEndRed", "lateCheckIn", "earlyCheckOut", "idelStartRed", "idelEndRed", "productivityStatusRed", "overLimitBreakRed", "eraseActivity", "createTimelineRequest", "updateTimelineRequest", "getTimelineRequests"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/ActivitySlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\n\r\nexport const ActivitySlice = createSlice({\r\n    name: \"Activity\",\r\n    initialState: {\r\n        activityArr: [], // For single user activity (legacy)\r\n        multiUserActivityArr: [], // For multi-user activity data (new)\r\n    },\r\n    reducers: {\r\n        createTodayGoal: () => {\r\n        },\r\n        createTodayStatus: () => {\r\n        },\r\n        getUserActivitySuccessfull: (state,action) => {\r\n            console.log(\"🔍 ActivitySlice - Received payload:\", action.payload);\r\n            console.log(\"🔍 ActivitySlice - Payload length:\", action.payload?.length || 0);\r\n\r\n            if(action.payload.length === 0) {\r\n                state.activityArr = []\r\n                state.multiUserActivityArr = []\r\n                console.log(\"🔍 ActivitySlice - Empty payload, clearing arrays\");\r\n            } else {\r\n                // Check if this is multi-user data vs single-user data\r\n                // Multi-user data has 'name' and 'email' properties and can have different view structures:\r\n                // - Day view: has 'clockin', 'clockout', 'atwork' etc.\r\n                // - Week view: has 'weekData' array and 'total'\r\n                // - Month view: has 'worked', 'focus', 'productive' etc.\r\n                // Single-user data has 'user' and 'checkInTime' properties\r\n                const firstItem = action.payload[0];\r\n                const isMultiUserData = action.payload.length > 0 &&\r\n                    Object.prototype.hasOwnProperty.call(firstItem, 'name') &&\r\n                    Object.prototype.hasOwnProperty.call(firstItem, 'email') &&\r\n                    !Object.prototype.hasOwnProperty.call(firstItem, 'user');\r\n\r\n                console.log(\"🔍 ActivitySlice - Checking data type - First item:\", firstItem);\r\n                console.log(\"🔍 ActivitySlice - Is multi-user data:\", isMultiUserData);\r\n\r\n                if (isMultiUserData) {\r\n                    state.multiUserActivityArr = action.payload;\r\n                    console.log(\"🔍 ActivitySlice - REDUCER MULTI-USER ACTIVITY LOG \", action.payload);\r\n                    console.log(\"🔍 ActivitySlice - Multi-user count:\", action.payload.length);\r\n                } else {\r\n                    state.activityArr = action.payload;\r\n                    console.log(\"🔍 ActivitySlice - REDUCER SINGLE-USER ACTIVITY LOG \", action.payload);\r\n                }\r\n            }\r\n\r\n        },\r\n        getUserActivity: () => {\r\n        },\r\n        checkOutStatusUpdate: () => {},\r\n        breakStartRed: () => {},\r\n        breakEndRed: () => {},\r\n        lateCheckIn: () => {},\r\n        earlyCheckOut: () => {},\r\n        idelStartRed: () => {},\r\n        idelEndRed: () => {},\r\n        productivityStatusRed: () => {},\r\n        overLimitBreakRed: () => {},\r\n        eraseActivity: (state = []) => {\r\n            state.activityArr = []\r\n            state.multiUserActivityArr = []\r\n        },\r\n        createTimelineRequest: () => {},\r\n        updateTimelineRequest: () => {},\r\n        getTimelineRequests: () => {}\r\n    }\r\n});\r\n\r\nexport default ActivitySlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAG9C,OAAO,MAAMC,aAAa,GAAGD,WAAW,CAAC;EACrCE,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAE;IACVC,WAAW,EAAE,EAAE;IAAE;IACjBC,oBAAoB,EAAE,EAAE,CAAE;EAC9B,CAAC;EACDC,QAAQ,EAAE;IACNC,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,iBAAiB,EAAEA,CAAA,KAAM,CACzB,CAAC;IACDC,0BAA0B,EAAEA,CAACC,KAAK,EAACC,MAAM,KAAK;MAAA,IAAAC,eAAA;MAC1CC,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,MAAM,CAACI,OAAO,CAAC;MACnEF,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE,EAAAF,eAAA,GAAAD,MAAM,CAACI,OAAO,cAAAH,eAAA,uBAAdA,eAAA,CAAgBI,MAAM,KAAI,CAAC,CAAC;MAE9E,IAAGL,MAAM,CAACI,OAAO,CAACC,MAAM,KAAK,CAAC,EAAE;QAC5BN,KAAK,CAACN,WAAW,GAAG,EAAE;QACtBM,KAAK,CAACL,oBAAoB,GAAG,EAAE;QAC/BQ,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;MACpE,CAAC,MAAM;QACH;QACA;QACA;QACA;QACA;QACA;QACA,MAAMG,SAAS,GAAGN,MAAM,CAACI,OAAO,CAAC,CAAC,CAAC;QACnC,MAAMG,eAAe,GAAGP,MAAM,CAACI,OAAO,CAACC,MAAM,GAAG,CAAC,IAC7CG,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,SAAS,EAAE,MAAM,CAAC,IACvDE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,SAAS,EAAE,OAAO,CAAC,IACxD,CAACE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACL,SAAS,EAAE,MAAM,CAAC;QAE5DJ,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEG,SAAS,CAAC;QAC7EJ,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEI,eAAe,CAAC;QAEtE,IAAIA,eAAe,EAAE;UACjBR,KAAK,CAACL,oBAAoB,GAAGM,MAAM,CAACI,OAAO;UAC3CF,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEH,MAAM,CAACI,OAAO,CAAC;UAClFF,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEH,MAAM,CAACI,OAAO,CAACC,MAAM,CAAC;QAC9E,CAAC,MAAM;UACHN,KAAK,CAACN,WAAW,GAAGO,MAAM,CAACI,OAAO;UAClCF,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAEH,MAAM,CAACI,OAAO,CAAC;QACvF;MACJ;IAEJ,CAAC;IACDQ,eAAe,EAAEA,CAAA,KAAM,CACvB,CAAC;IACDC,oBAAoB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC9BC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,WAAW,EAAEA,CAAA,KAAM,CAAC,CAAC;IACrBC,aAAa,EAAEA,CAAA,KAAM,CAAC,CAAC;IACvBC,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;IACtBC,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAC;IACpBC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,iBAAiB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC3BC,aAAa,EAAEA,CAACvB,KAAK,GAAG,EAAE,KAAK;MAC3BA,KAAK,CAACN,WAAW,GAAG,EAAE;MACtBM,KAAK,CAACL,oBAAoB,GAAG,EAAE;IACnC,CAAC;IACD6B,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,qBAAqB,EAAEA,CAAA,KAAM,CAAC,CAAC;IAC/BC,mBAAmB,EAAEA,CAAA,KAAM,CAAC;EAChC;AACJ,CAAC,CAAC;AAEF,eAAenC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}