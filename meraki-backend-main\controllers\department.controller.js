'use strict';

const DepartmentService = require("../services/department.service");

exports.fetchAllDepartments = async (req, res) => {
    // Get user from the request (set by auth middleware)
    const { user } = req;

    // Check if user is authenticated
    if (!user) {
        return res.status(401).send('Authentication required');
    }

    // Allow all authenticated users to view departments
    // This is a read-only operation that should be available to all users

    const { keyword, sort, page, limit } = req.query;
    const queries = {
        page: page ? parseInt(page) : 1,
        limit: limit ? parseInt(limit) : 20,
        sort: { name: -1 },
        query: {}
    };
    console.log("Filter keyworkds ",keyword)
    if (page) {
        queries.skip = limit;
    }

    if (keyword) {
        queries.query.name = { '$regex': '.*' + keyword + '.*', '$options': 'i' };
    }

    if (sort) {
        const field = sort.split(",");
        queries.sort = {
            [field[0]]: parseInt(field[1])
        };
    }

    const results = await DepartmentService.getDepartmentsByQuery(queries);

    return res.status(200).send(results);
}

exports.createDepartment = async (req, res) => {
    // Get user from the request (set by auth middleware)
    const { user, body } = req;

    // Check if user has admin or HR role
    if (!user || !Array.isArray(user.role) || !(user.role.includes('admin') || user.role.includes('humanresource'))) {
        return res.status(401).send('You have no permission to access this feature!');
    }

    const result = await DepartmentService.createDepartment(body);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send({
        message: "Successfully proceed data."
    });
}

exports.fetchDepartmentById = async (req, res) => {
    // Get user from the request (set by auth middleware)
    const { user, params } = req;

    // Check if user has admin or HR role
    if (!user || !Array.isArray(user.role) || !(user.role.includes('admin') || user.role.includes('humanresource'))) {
        return res.status(401).send('You have no permission to access this feature!');
    }

    const result = await DepartmentService.getDepartmentById(params.id);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send(result);
}

exports.fetchLoggedInDepartment = async (req, res) => {
    // Get user from the request (set by auth middleware)
    const { user } = req;

    if (!user) {
        return res.status(401).send('Authentication required');
    }

    // Get the department ID from the user object
    const departmentId = user.department;

    if (!departmentId) {
        return res.status(404).send({
            message: "User has no assigned department"
        });
    }

    const result = await DepartmentService.getDepartmentById(departmentId);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send(result);
}

exports.updateDepartment = async (req, res) => {
    // Get user from the request (set by auth middleware)
    const { user, params, body } = req;

    // Check if user has admin or HR role
    if (!user || !Array.isArray(user.role) || !(user.role.includes('admin') || user.role.includes('humanresource'))) {
        return res.status(401).send('You have no permission to access this feature!');
    }

    const result = await DepartmentService.updateDepartment(params.id, body);

    if (!result || result.n === 0) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send({
        message: "Successfully proceed data."
    });
}

exports.deleteDepartment = async (req, res) => {
    // Get user from the request (set by auth middleware)
    const { user, params } = req;

    // Check if user has admin or HR role
    if (!user || !Array.isArray(user.role) || !(user.role.includes('admin') || user.role.includes('humanresource'))) {
        return res.status(401).send('You have no permission to access this feature!');
    }

    const result = await DepartmentService.deleteDepartment(params.id);

    if (!result) {
        return res.status(500).send({
            message: "Failed to proceed data."
        })
    }

    return res.status(200).send({
        message: "Successfully proceed data."
    });
}
