# Final cleanup script to remove any remaining console logs
Write-Host "Starting final cleanup of console logs..."

# Frontend cleanup
Write-Host "Checking frontend for remaining console logs..."
$frontendPath = "meraki-frontend-main\src"
$frontendFiles = Get-ChildItem -Path $frontendPath -Recurse -Include "*.js" -File

foreach ($file in $frontendFiles) {
    $content = Get-Content $file.FullName -Raw
    if ($content -match 'console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)') {
        Write-Host "Found console logs in: $($file.FullName)"
        # Remove all console statements
        $content = $content -replace '(?m)^\s*console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)\s*\([^;]*\);\s*$', ''
        $content = $content -replace '(?m)^\s*//\s*console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)\s*\([^;]*\);\s*$', ''
        $content = $content -replace 'console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)\s*\([^)]*\);\s*', ''
        
        # Remove empty lines
        $content = $content -replace '(?m)^\s*$\r?\n', ''
        
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Cleaned: $($file.FullName)"
    }
}

# Backend cleanup (excluding index.js)
Write-Host "Checking backend for remaining console logs..."
$backendPath = "meraki-backend-main"
$backendFiles = Get-ChildItem -Path $backendPath -Recurse -Include "*.js" -File | Where-Object { $_.Name -ne "index.js" -and $_.FullName -notlike "*node_modules*" }

foreach ($file in $backendFiles) {
    $content = Get-Content $file.FullName -Raw
    if ($content -match 'console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)') {
        Write-Host "Found console logs in: $($file.FullName)"
        # Remove all console statements
        $content = $content -replace '(?m)^\s*console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)\s*\([^;]*\);\s*$', ''
        $content = $content -replace '(?m)^\s*//\s*console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)\s*\([^;]*\);\s*$', ''
        $content = $content -replace 'console\.(log|warn|error|info|debug|table|group|groupEnd|trace|time|timeEnd)\s*\([^)]*\);\s*', ''
        
        # Remove empty lines
        $content = $content -replace '(?m)^\s*$\r?\n', ''
        
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "Cleaned: $($file.FullName)"
    }
}

Write-Host "Final cleanup completed!"
