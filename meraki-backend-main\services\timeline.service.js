const { db } = require("../models");
const { count } = require("../models/user.model");
const Timeline = db.timeline
const Activity = db.activity
exports.updateTimelineRequest = async (body) => {
    try {
        const id = body.id;
        // Update the Timeline document
        const result = await Timeline.findByIdAndUpdate(id, {
            status: body.status,
            updatedByAdmin: body.updatedByAdmin
        }, { new: true }); // { new: true } returns the updated document
        let fromTimeDate;
        let toTimeDate;
        // Handling fromTime and toTime
        if (body.fromTime instanceof Date) {
            fromTimeDate = body.fromTime;
            toTimeDate = body.toTime;
        } else {
            fromTimeDate = new Date(body.fromTime);
            toTimeDate = new Date(body.toTime);
        }
        const formattedFromTimeDate = fromTimeDate.toISOString().split('T')[0];
        // Find activity results matching the date
        const activityResults = await Activity.find({
            $expr: {
                $eq: [
                    { $dateToString: { format: "%Y-%m-%d", date: "$checkInTime" } },
                    formattedFromTimeDate
                ]
            }
        }).exec();
        // Helper function to update productivity filled
        const updateProductivityFilled = (productivityHistory, fromTimeDate, toTimeDate, name) => {
            for (let i = 0; i < productivityHistory.length; i++) {
                let element = productivityHistory[i];
                let arr = element.slotHours.split('-');
                let slotStartHour = parseInt(arr[0]);
                let slotEndHour = parseInt(arr[2]);
                let fromHour = fromTimeDate.getHours();
                let toHour = toTimeDate.getHours();
                // Check if the current slot falls within the given time range
                if (slotStartHour >= fromHour && slotEndHour >= toHour) {
                    if (fromHour === toHour) {
                        // Same hour: directly calculate the minutes
                        if (name === "Private") {
                            element.productivityFilled += Math.floor((toTimeDate - fromTimeDate) / (1000 * 60));
                            element.totalSlotFilled += Math.floor((toTimeDate - fromTimeDate) / (1000 * 60));
                        } else {
                            element.productivityFilled += Math.floor((toTimeDate - fromTimeDate) / (1000 * 60));
                        }
                    } else {
                        if (slotStartHour === fromHour && slotStartHour < toHour) {
                            // First hour slot: calculate till the end of the hour
                            let middlePart = new Date(fromTimeDate);
                            middlePart.setMinutes(59);
                            middlePart.setSeconds(59);
                            if (name === "Private") {
                                element.productivityFilled += Math.floor((middlePart - fromTimeDate) / (1000 * 60));
                                element.totalSlotFilled += Math.floor((middlePart - fromTimeDate) / (1000 * 60));
                            } else {
                                element.productivityFilled += Math.floor((middlePart - fromTimeDate) / (1000 * 60));
                            }
                        } else if (slotStartHour >= fromHour && slotEndHour <= toHour) {
                            // Full hour slot in between: add 60 minutes
                            if (name === "Private") {
                                element.productivityFilled += 60;
                                element.totalSlotFilled += 60;
                            } else {
                                element.productivityFilled += 60;
                            }
                        } else if (slotStartHour === toHour) {
                            // Last hour slot: calculate from the start of the hour
                            let middlePart = new Date(toTimeDate);
                            middlePart.setMinutes(0);
                            middlePart.setSeconds(0);
                            if (name === "Private") {
                                element.productivityFilled += Math.floor((toTimeDate - middlePart) / (1000 * 60));
                                element.totalSlotFilled += Math.floor((toTimeDate - middlePart) / (1000 * 60));
                            } else {
                                element.productivityFilled += Math.floor((toTimeDate - middlePart) / (1000 * 60));
                            }
                        }
                    }
                }
            }
        };
        // Iterate through each activity result
        for (let activityResult of activityResults) {
            switch (body.requestFrom) {
                case "Idel":
                    let count = activityResult.idelHistory.findIndex((element) => {
                        return new Date(element.idelStartedTime).setMilliseconds(0) <= fromTimeDate.setMilliseconds(0) && toTimeDate.setMilliseconds(0) <= new Date(element.idelEndedTime).setMilliseconds(0);
                    });
                    let requestTimeLine = {};
                    // Remove the element if found and update the data
                    if (count !== -1) {
                        const fromTimeDateWithoutTime = new Date(fromTimeDate.setMilliseconds(0));
                        const toTimeDateWithoutTime = new Date(toTimeDate.setMilliseconds(0));
                        const idelStartedTimeWithoutTime = new Date(new Date(activityResult.idelHistory[count].idelStartedTime).setMilliseconds(0));
                        const idelEndedTimeWithoutTime = new Date(new Date(activityResult.idelHistory[count].idelEndedTime).setMilliseconds(0));
                        if (idelStartedTimeWithoutTime.getTime() === fromTimeDateWithoutTime.getTime() &&
                            idelEndedTimeWithoutTime.getTime() === toTimeDateWithoutTime.getTime()) {
                            requestTimeLine = {
                                startTimeline: activityResult.idelHistory[count].idelStartedTime,
                                endTimeline: activityResult.idelHistory[count].idelEndedTime
                            };
                            activityResult.idelHistory.splice(count, 1);
                        } else {
                            requestTimeLine = {
                                startTimeline: fromTimeDate,
                                endTimeline: toTimeDate
                            };
                            if(idelStartedTimeWithoutTime.setSeconds(0) === fromTimeDateWithoutTime.setSeconds(0)) {
                                console.log("Best one")
                                activityResult.idelHistory[count].idelStartedTime = toTimeDateWithoutTime;
                            } else if (idelEndedTimeWithoutTime.setSeconds(0)=== toTimeDateWithoutTime.setSeconds(0)) {
                                    activityResult.idelHistory.push(tempRequest);
                        }
                        activityResult.timelineRequestHistory.push(requestTimeLine);
                    }
                    updateProductivityFilled(activityResult.productivityHistory, fromTimeDate, toTimeDate, "Idel");
                    break;
                case "Private":
                    });
                    let requestTimeLineBreak = {};
                    // Remove the element if found and update the data
                    if (countPrivate !== -1) {
                        const fromTimeDateWithoutTime = new Date(fromTimeDate.setMilliseconds(0));
                        const toTimeDateWithoutTime = new Date(toTimeDate.setMilliseconds(0));
                        const breakStartedTimeWithoutTime = new Date(new Date(activityResult.breaksHistory[countPrivate].breakStartedTime).setMilliseconds(0));
                        const breakEndedTimeWithoutTime = new Date(new Date(activityResult.breaksHistory[countPrivate].breakEndedTime).setMilliseconds(0));
                        if (breakStartedTimeWithoutTime.getTime() === fromTimeDateWithoutTime.getTime() &&
                        breakEndedTimeWithoutTime.getTime() === toTimeDateWithoutTime.getTime()) {
                        requestTimeLineBreak = {
                            startTimeline: activityResult.breaksHistory[countPrivate].breakStartedTime,
                            endTimeline: activityResult.breaksHistory[countPrivate].breakEndedTime
                        };
                        activityResult.breaksHistory.splice(countPrivate, 1);
                        } else {
                            requestTimeLineBreak = {
                                startTimeline: fromTimeDate,
                                endTimeline: toTimeDate
                            };
                            if(breakStartedTimeWithoutTime.setSeconds(0) === fromTimeDateWithoutTime.setSeconds(0)) {
                                console.log("Best one")
                                activityResult.breaksHistory[countPrivate].breakStartedTime = toTimeDateWithoutTime;
                            } else if (breakEndedTimeWithoutTime.setSeconds(0)=== toTimeDateWithoutTime.setSeconds(0)) {
                                    activityResult.breaksHistory.push(tempRequest);
                            // activityResult.breaksHistory.splice(countPrivate, 1);
                        }
                        activityResult.timelineRequestHistory.push(requestTimeLineBreak);
                    }
                    updateProductivityFilled(activityResult.productivityHistory, fromTimeDate, toTimeDate, "Private");
                    break;
            }
        }
        return result;
    } catch (error) {
        return new Error("Error occurred while updating the timeline.");
    }
};
exports.createTimelineRequest = async (body) => {
      try {
    const page = queries.page
        // Get the start and end of the given date (ignoring time)
        const startOfDay = new Date(inputDate.setHours(0, 0, 0, 0)); // 00:00:00
        const endOfDay = new Date(inputDate.setHours(23, 59, 59, 999)); // 23:59:59
        // Query to filter by date and user ID
        const result = await Timeline.find({
            createdAt: { $gte: startOfDay, $lte: endOfDay }, // Match date range
            userId: id // Match user ID
        });
        return { data: result };
    } catch (error) {
        return new Error("Error fetching data");
    }
};
exports.deleteTimelineRequest = async (id) => {
    try {
        const result = await Timeline.findByIdAndDelete(id)
        return {data : result}
      }
      catch {
        return new Error("Error")
      }
}
exports.updateTaskTimelineRequest = async (id, taskId, body) => {
    try {
        // First, get the timeline request to access task details and time information
        const timelineRequest = await Timeline.findOne({
            _id: id,
            "taskDetails._id": taskId
        });
        if (!timelineRequest) {
            throw new Error("Timeline request not found");
        }
        // Update the timeline request status
        const result = await Timeline.findOneAndUpdate({
            _id: id,
            "taskDetails._id": taskId
        }, {
            "taskDetails.$.status": body.status
        }, { new: true });
        // If the request is being approved, add the time to the task
        if (body.status === "approved" && timelineRequest.fromTime && timelineRequest.toTime) {
            // Calculate the approved time in hours
            const fromTime = new Date(timelineRequest.fromTime);
            const toTime = new Date(timelineRequest.toTime);
            const timeDiffMs = toTime - fromTime;
            const approvedHours = timeDiffMs / (1000 * 60 * 60); // Convert to hours
            // Find the task in the product and update its time tracking
            const Product = db.product;
            const products = await Product.find({ "taskArr._id": taskId });
            for (const product of products) {
                const task = product.taskArr.id(taskId);
                if (task) {
                    // Add approved time to total hours
                    task.totalHours = (task.totalHours || 0) + approvedHours;
                    task.totalSpent = (task.totalSpent || 0) + approvedHours;
                    // Add to daily hours tracking (use the date from fromTime)
                    const dateKey = fromTime.toISOString().split('T')[0];
                    if (!task.hours) task.hours = new Map();
                    const currentDayHours = task.hours.get(dateKey) || 0;
                    task.hours.set(dateKey, currentDayHours + approvedHours);
                    // Mark the nested object as modified for Mongoose
                    product.markModified("taskArr");
                    const taskIndex = product.taskArr.indexOf(task);
                    product.markModified(`taskArr.${taskIndex}.hours`);
                    product.markModified(`taskArr.${taskIndex}.totalHours`);
                    product.markModified(`taskArr.${taskIndex}.totalSpent`);
                    await product.save();
                    break; // Task found and updated, exit loop
                }
            }
        }
        return { data: result };
    } catch (error) {
        throw new Error(`Error updating task timeline request: ${error.message}`);
    }
}
exports.deleteTaskTimelineRequest = async (id, taskId) => {
  try {
    // Find the document first
    const timeline = await Timeline.findById(id);
    if (!timeline) {
      return { error: "Timeline not found" };
    }
    // Check if taskDetails._id matches the taskId
    if (timeline.taskDetails && timeline.taskDetails._id.toString() === taskId.toString()) {
      const result = await Timeline.findByIdAndUpdate(
        id,
        { $unset: { taskDetails: "" } },
        { new: true }
      );
      return { data: result };
    } else {
      return { error: "taskDetails does not match given taskId" };
    }
  } catch (error) {
    return { error: "Error deleting taskDetails" };
  }
};
