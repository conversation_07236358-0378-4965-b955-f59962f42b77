import dayjs from 'dayjs';
import { DEFAULT_WORK_SCHEDULE } from 'constants/workSchedule';

/**
 * Work Schedule Utilities
 * Handles multiple schedule types with priority-based resolution
 */
export class WorkScheduleUtils {
  /**
   * Get effective work schedule for a user at a specific date/time
   * Priority: Time-Specific > Daily > Weekly > Default
   */
  static getEffectiveSchedule(user, targetDate, targetTime = null) {
    const schedules = user.workSchedules || [];
    const defaultSchedule = user.workSchedule || DEFAULT_WORK_SCHEDULE;
    
    // Convert target date to dayjs for comparison
    const target = dayjs(targetDate);
    
    // Find applicable schedules
    const applicableSchedules = schedules.filter(schedule => {
      return this.isScheduleApplicable(schedule, target, targetTime);
    });
    
    // Sort by priority (highest first)
    applicableSchedules.sort((a, b) => (b.priority || 1) - (a.priority || 1));
    
    // Return highest priority schedule or default
    return applicableSchedules.length > 0 ? applicableSchedules[0] : defaultSchedule;
  }
  
  /**
   * Check if a schedule is applicable for the given date/time
   */
  static isScheduleApplicable(schedule, targetDate, targetTime) {
    const scheduleStart = dayjs(schedule.effectiveFrom);
    const scheduleEnd = dayjs(schedule.effectiveTo);

    // Check date range - must be within effective dates
    if (targetDate.isBefore(scheduleStart, 'day') || targetDate.isAfter(scheduleEnd, 'day')) {
      return false;
    }

    // Check schedule type specific conditions
    switch (schedule.type) {
      case 'time_specific':
        return this.isTimeSpecificApplicable(schedule, targetDate, targetTime);
      case 'daily':
        return this.isDailyApplicable(schedule, targetDate);
      case 'weekly':
        return this.isWeeklyApplicable(schedule, targetDate);
      default:
        return true;
    }
  }
  
  /**
   * Check if time-specific schedule applies
   */
  static isTimeSpecificApplicable(schedule, targetDate, targetTime) {
    if (!targetTime) { return false }
    
    const scheduleDate = dayjs(schedule.specificDate);
    if (!targetDate.isSame(scheduleDate, 'day')) { return false }
    
    const timeStart = this.parseTime(schedule.startTime);
    const timeEnd = this.parseTime(schedule.endTime);
    const target = this.parseTime(targetTime);
    
    return target >= timeStart && target <= timeEnd;
  }
  
  /**
   * Check if daily schedule applies
   */
  static isDailyApplicable(schedule, targetDate) {
    // For daily schedules, must match the specific date exactly
    if (schedule.specificDate) {
      return targetDate.isSame(dayjs(schedule.specificDate), 'day');
    }
    // If no specific date, apply to the effective date range
    return true;
  }
  
  /**
   * Check if weekly schedule applies
   */
  static isWeeklyApplicable(schedule, targetDate) {
    if (schedule.daysOfWeek && schedule.daysOfWeek.length > 0) {
      const dayOfWeek = targetDate.day(); // 0 = Sunday, 1 = Monday, etc.
      return schedule.daysOfWeek.includes(dayOfWeek);
    }
    return true;
  }
  
  /**
   * Parse time string to minutes for comparison
   */
  static parseTime(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return (hours * 60) + minutes;
  }
  
  /**
   * Validate check-in/check-out against schedule
   */
  static validateCheckInOut(user, checkTime, action = 'checkin') {
    const checkDate = dayjs(checkTime);
    const timeString = checkDate.format('HH:mm');
    
    const effectiveSchedule = this.getEffectiveSchedule(
      user, 
      checkDate.format('YYYY-MM-DD'), 
      timeString
    );
    
    const scheduleStart = this.parseTime(effectiveSchedule.startTime);
    const scheduleEnd = this.parseTime(effectiveSchedule.endTime);
    const checkTimeMinutes = this.parseTime(timeString);
    
    const result = {
      isValid: false,
      schedule: effectiveSchedule,
      message: '',
      allowedRange: `${effectiveSchedule.startTime} - ${effectiveSchedule.endTime}`,
      scheduleType: effectiveSchedule.type || 'default'
    };
    
    if (action === 'checkin') {
      // Allow check-in 15 minutes before start time
      const earlyBuffer = 15;
      const allowedStart = scheduleStart - earlyBuffer;
      
      if (checkTimeMinutes >= allowedStart && checkTimeMinutes <= scheduleEnd) {
        result.isValid = true;
        result.message = 'Check-in allowed';
      } else if (checkTimeMinutes < allowedStart) {
        result.message = `Too early. Check-in allowed from ${this.minutesToTime(allowedStart)}`;
      } else {
        result.message = `Too late. Work hours end at ${effectiveSchedule.endTime}`;
      }
    } else if (action === 'checkout') {
      // Allow check-out 30 minutes after end time
      const lateBuffer = 30;
      const allowedEnd = scheduleEnd + lateBuffer;
      
      if (checkTimeMinutes >= scheduleStart && checkTimeMinutes <= allowedEnd) {
        result.isValid = true;
        result.message = 'Check-out allowed';
      } else if (checkTimeMinutes < scheduleStart) {
        result.message = `Too early. Work starts at ${effectiveSchedule.startTime}`;
      } else {
        result.message = `Very late check-out. Consider overtime approval.`;
        result.isValid = true; // Allow but flag for review
      }
    }
    
    return result;
  }
  
  /**
   * Convert minutes to time string
   */
  static minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
  
  /**
   * Get all schedules for a user in a date range
   */
  static getUserSchedulesInRange(user, startDate, endDate) {
    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const schedules = [];
    
    let current = start;
    while (current.isBefore(end) || current.isSame(end, 'day')) {
      const dateStr = current.format('YYYY-MM-DD');
      const effectiveSchedule = this.getEffectiveSchedule(user, dateStr);
      
      schedules.push({
        date: dateStr,
        schedule: effectiveSchedule,
        dayOfWeek: current.format('dddd')
      });
      
      current = current.add(1, 'day');
    }
    
    return schedules;
  }
  
  /**
   * Create a new work schedule entry
   */
  static createScheduleEntry(userId, scheduleData) {
    return {
      id: `schedule_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      userId,
      type: scheduleData.type || 'daily',
      priority: this.getSchedulePriority(scheduleData.type),
      scheduleTemplate: scheduleData.scheduleTemplate,
      startTime: scheduleData.startTime,
      endTime: scheduleData.endTime,
      minimumHours: scheduleData.minimumHours,
      effectiveFrom: scheduleData.effectiveFrom,
      effectiveTo: scheduleData.effectiveTo,
      specificDate: scheduleData.specificDate,
      daysOfWeek: scheduleData.daysOfWeek,
      description: scheduleData.description,
      createdAt: new Date().toISOString(),
      isActive: true
    };
  }
  
  /**
   * Get priority for schedule type
   */
  static getSchedulePriority(type) {
    const priorities = {
      'default': 1,
      'weekly': 2,
      'daily': 3,
      'time_specific': 4
    };
    return priorities[type] || 1;
  }
  
  /**
   * Calculate hours between two times (handles overnight shifts)
   */
  static calculateHours(startTime, endTime) {
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);
    
    let startMinutes = (startHour * 60) + startMin;
    let endMinutes = (endHour * 60) + endMin;
    
    // Handle overnight shifts (night shift)
    if (endMinutes <= startMinutes) {
      endMinutes += (24 * 60); // Add 24 hours
    }
    
    const diffMinutes = endMinutes - startMinutes;
    return (diffMinutes / 60).toFixed(2);
  }
}

export default WorkScheduleUtils;
