import React, { useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Checkbox,
  FormControlLabel,
  Grid
} from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import Input from 'components/Input';
import SelectField from 'components/SelectField';
import { UserActions, GeneralActions } from 'slices/actions';
import { GeneralSelector, UserSelector } from 'selectors';
import { SCHEDULE_TEMPLATES, DEFAULT_WORK_SCHEDULE, TIME_OPTIONS } from 'constants/workSchedule';

const WorkScheduleForm = ({ open, onClose, selectedUser, selectedDate }) => {
  const dispatch = useDispatch();
  const users = useSelector(UserSelector.getUsers());
  const success = useSelector(GeneralSelector.success(UserActions.updateUser.type));

  useEffect(() => {
    if (success) {
      toast.success('Work schedule updated successfully!', {
        position: "top-right",
        autoClose: 3000,
        closeOnClick: true,
      });
      onClose();
      dispatch(GeneralActions.removeSuccess([UserActions.updateUser.type]));
    }
  }, [success, onClose, dispatch]);

  const formik = useFormik({
    initialValues: {
      selectedUser: selectedUser?._id || '',
      scheduleTemplate: DEFAULT_WORK_SCHEDULE.scheduleTemplate,
      shiftStart: selectedDate || dayjs().format('YYYY-MM-DD'),
      shiftEnd: selectedDate || dayjs().format('YYYY-MM-DD'),
      startTime: DEFAULT_WORK_SCHEDULE.startTime,
      endTime: DEFAULT_WORK_SCHEDULE.endTime,
      minimumHours: DEFAULT_WORK_SCHEDULE.minimumHours,
      saveAsTemplate: false,
      templateName: '',
      repeatShift: false,
      daysToRepeat: [],
      repeatEndDate: dayjs().add(7, 'days').format('YYYY-MM-DD')
    },
    onSubmit: (values) => handleSubmit(values)
  });

  const handleSubmit = (values) => {
    const targetUser = users.find(user => user._id === values.selectedUser);
    if (!targetUser) {
      toast.error('Please select a user');
      return;
    }

    // Calculate hours
    const calculatedHours = calculateHours(values.startTime, values.endTime);

    // Create work schedule object
    const workSchedule = {
      scheduleTemplate: values.scheduleTemplate,
      shiftStart: new Date(values.shiftStart),
      shiftEnd: new Date(values.shiftEnd),
      startTime: values.startTime,
      endTime: values.endTime,
      minimumHours: parseFloat(values.minimumHours) || calculatedHours
    };

    const params = {
      id: targetUser._id,
      workSchedule: workSchedule
    };

    dispatch(UserActions.updateUser(params));
  };

  const calculateHours = (startTime, endTime) => {
    const [startHour, startMin] = startTime.split(':').map(Number);
    const [endHour, endMin] = endTime.split(':').map(Number);
    
    let startMinutes = (startHour * 60) + startMin;
    let endMinutes = (endHour * 60) + endMin;
    
    // Handle overnight shifts
    if (endMinutes <= startMinutes) {
      endMinutes += (24 * 60);
    }
    
    const diffMinutes = endMinutes - startMinutes;
    return (diffMinutes / 60).toFixed(2);
  };

  const handleFieldChange = (field, value) => {
    formik.setFieldValue(field, value);
    
    // Auto-calculate hours when time changes
    if (field === 'startTime' || field === 'endTime') {
      const startTime = field === 'startTime' ? value : formik.values.startTime;
      const endTime = field === 'endTime' ? value : formik.values.endTime;
      
      if (startTime && endTime) {
        const calculatedHours = calculateHours(startTime, endTime);
        formik.setFieldValue('minimumHours', calculatedHours);
      }
    }
    
    // Auto-detect night shift
    if (field === 'startTime') {
      const hour = parseInt(value.split(':')[0], 10);
      if (hour >= 22 || hour < 6) {
        formik.setFieldValue('scheduleTemplate', 'night_shift');
      } else {
        formik.setFieldValue('scheduleTemplate', 'day_shift');
      }
    }
  };

  const daysOfWeek = [
    { value: 1, label: 'MO' },
    { value: 2, label: 'TU' },
    { value: 3, label: 'WE' },
    { value: 4, label: 'TH' },
    { value: 5, label: 'FR' },
    { value: 6, label: 'SA' },
    { value: 0, label: 'SU' }
  ];

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Typography variant="h6">Add Schedule</Typography>
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 2 }}>
          {/* User Selection */}
          <SelectField
            label="Select Teams"
            name="selectedUser"
            value={formik.values.selectedUser}
            onChange={(e) => handleFieldChange('selectedUser', e.target.value)}
            required
          >
            <MenuItem value="">Select teams</MenuItem>
            {users.map((user) => (
              <MenuItem key={user._id} value={user._id}>
                {user.name || 'Unknown User'}
              </MenuItem>
            ))}
          </SelectField>

          {/* Members Selection - Placeholder */}
          <SelectField
            label="Members"
            name="members"
            value=""
            disabled
          >
            <MenuItem value="">Members</MenuItem>
          </SelectField>

          {/* Schedule Template */}
          <SelectField
            label="Schedule Template"
            name="scheduleTemplate"
            value={formik.values.scheduleTemplate}
            onChange={(e) => handleFieldChange('scheduleTemplate', e.target.value)}
            required
          >
            {SCHEDULE_TEMPLATES.map((template) => (
              <MenuItem key={template.value} value={template.value}>
                {template.label}
              </MenuItem>
            ))}
          </SelectField>

          {/* Shift Dates */}
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Input
                label="Shift Starts"
                name="shiftStart"
                type="date"
                value={formik.values.shiftStart}
                onChange={(e) => handleFieldChange('shiftStart', e.target.value)}
                required
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={6}>
              <Input
                label="Shift Ends"
                name="shiftEnd"
                type="date"
                value={formik.values.shiftEnd}
                onChange={(e) => handleFieldChange('shiftEnd', e.target.value)}
                required
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>

          {/* Time Range */}
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <SelectField
                label="Start Time"
                name="startTime"
                value={formik.values.startTime}
                onChange={(e) => handleFieldChange('startTime', e.target.value)}
                required
              >
                {TIME_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </SelectField>
            </Grid>
            <Grid item xs={6}>
              <SelectField
                label="End Time"
                name="endTime"
                value={formik.values.endTime}
                onChange={(e) => handleFieldChange('endTime', e.target.value)}
                required
              >
                {TIME_OPTIONS.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </SelectField>
            </Grid>
          </Grid>

          {/* Minimum Hours */}
          <Input
            label="Minimum Hours"
            name="minimumHours"
            type="number"
            step="0.1"
            value={formik.values.minimumHours}
            onChange={(e) => handleFieldChange('minimumHours', e.target.value)}
            required
            helperText={`Calculated: ${calculateHours(formik.values.startTime, formik.values.endTime)} hours`}
          />

          {/* Save as Template */}
          <FormControlLabel
            control={
              <Checkbox
                checked={formik.values.saveAsTemplate}
                onChange={(e) => handleFieldChange('saveAsTemplate', e.target.checked)}
              />
            }
            label="Save as template"
          />

          {formik.values.saveAsTemplate && (
            <Input
              label="Schedule template name"
              name="templateName"
              value={formik.values.templateName}
              onChange={(e) => handleFieldChange('templateName', e.target.value)}
              placeholder="Schedule Name"
            />
          )}

          {/* Repeat Shift */}
          <FormControlLabel
            control={
              <Checkbox
                checked={formik.values.repeatShift}
                onChange={(e) => handleFieldChange('repeatShift', e.target.checked)}
              />
            }
            label="Repeat Shift"
          />

          {formik.values.repeatShift && (
            <>
              <Typography variant="body2">Days to repeat</Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                {daysOfWeek.map((day) => (
                  <FormControlLabel
                    key={day.value}
                    control={
                      <Checkbox
                        checked={formik.values.daysToRepeat.includes(day.value)}
                        onChange={(e) => {
                          const newDays = e.target.checked ? [...formik.values.daysToRepeat, day.value] : formik.values.daysToRepeat.filter(d => d !== day.value);
                          handleFieldChange('daysToRepeat', newDays);
                        }}
                      />
                    }
                    label={day.label}
                  />
                ))}
              </Box>

              <Input
                label="Repeat end date"
                name="repeatEndDate"
                type="date"
                value={formik.values.repeatEndDate}
                onChange={(e) => handleFieldChange('repeatEndDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="secondary">
          Close
        </Button>
        <Button 
          onClick={formik.handleSubmit} 
          variant="contained" 
          color="primary"
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

WorkScheduleForm.propTypes = {
  open: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  selectedUser: PropTypes.object,
  selectedDate: PropTypes.string
};

export default WorkScheduleForm;
