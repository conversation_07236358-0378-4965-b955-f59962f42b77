import React, { useEffect, useState, lazy, Suspense } from "react";
import { Route, Switch, Redirect } from "react-router-dom";
import PermissionRoute from "components/PermissionRoute";
import { useSelector } from "react-redux";
import { UserSelector } from "./selectors";
import { features } from "constants/permission";
import LoadingScreen from "./components/LoadingScreen";

// Layouts
import MainLayout from "./layouts/MainLayout";
import AuthLayout from "./layouts/AuthLayout";

// Eagerly loaded components (small and frequently used)
import NotFound from "components/NotFound";
import AccessDenied from "components/AccessDenied";

// Lazy loaded components
const Login = lazy(() => import("./screens/Auth/Login"));
const Dashboard = lazy(() => import("./screens/Dashboard"));
const Profile = lazy(() => import("./screens/Profile"));

// User management
const User = lazy(() => import("screens/User"));
const CreateUser = lazy(() => import("./screens/User/Create"));
const FormUser = lazy(() => import("./screens/User/Form"));

// Department
const Department = lazy(() => import("screens/Department"));
const FormDepartment = lazy(() => import("./screens/Department/Form"));

// Designation
const Designation = lazy(() => import("./screens/Designation"));
const FormDesignation = lazy(() => import("./screens/Designation/Form"));

// Attendance
const Attendance = lazy(() => import("./screens/Attendance"));
const FormAttendance = lazy(() => import("./screens/Attendance/Form"));

// Expenses
const Expenses = lazy(() => import("./screens/Expenses"));
const FormExpenses = lazy(() => import("./screens/Expenses/Form"));

// Reports
const Report = lazy(() => import("./screens/Report"));

// Leave management
const Leaves = lazy(() => import("./screens/Leave"));
const FormLeave = lazy(() => import("./screens/Leave/Form"));
const LeaveReport = lazy(() => import("screens/LeaveReport/LeaveReport"));
const Approval = lazy(() => import("screens/LeaveApproval/Approval"));
const LeaveConfiguration = lazy(() => import("screens/LeaveConfiguration/LeaveConfiguration"));
const LeaveCalendar = lazy(() => import("screens/LeaveCalendar/LeaveCalendar"));

// Settings
const Setting = lazy(() => import("./screens/Setting"));

// Timeline
const Timeline = lazy(() => import("screens/Timeline"));
const Overivew = lazy(() => import("screens/ActivityTimeline/Overview/Overview"));
const TimeRequest = lazy(() => import("screens/ActivityTimeline/TimeRequest/TimeRequest"));
const TaskRequest = lazy(() => import("screens/ActivityTimeline/TaskRequest/TaskRequest"));
const WorkSchedule = lazy(() => import("screens/ActivityTimeline/WorkSchedule/WorkSchedule"));

// Project management
const ProductList = lazy(() => import("screens/Product/ProductList"));
const ProductOverview = lazy(() => import("screens/Product/ProductOverview"));
const ProductTimesheet = lazy(() => import("screens/Product/ProductTimesheet"));
const Client = lazy(() => import("screens/Client/Client"));
const Tasklist = lazy(() => import("screens/Product/Tasklist"));
const ProductListStaff = lazy(() => import("screens/Product/Staff/ProductListStaff"));
const TaskHistoryAdmin = lazy(() => import("screens/Product/TaskHistoryAdmin"));
const TaskListWithNote = lazy(() => import("screens/Product/Staff/TaskListWithNote"));

// Sprint management
const SprintPage = lazy(() => import("screens/Sprints/pages/SprintPage"));
const UserSprintPage = lazy(() => import("screens/Sprints/pages/UserSprintPage"));
const SprintTasksPage = lazy(() => import("screens/Sprints/pages/SprintTasksPage"));

const PrivateRoutes = [
    // User routes
    {
        path: "/app/user",
        component: User,
        permission: { feat: features.user, act: 'read' }
    },
    {
        path: "/app/user/create",
        component: CreateUser,
        permission: { feat: features.user, act: 'create' }
    },
    {
        path: "/app/user/update/:id",
        component: FormUser,
        permission: { feat: features.user, act: 'update' }
    },

    // Department routes
    {
        path: "/app/department",
        component: Department,
        permission: { feat: features.department, act: 'read' }
    },
    {
        path: "/app/department/create",
        component: FormDepartment,
        permission: { feat: features.department, act: 'create' }
    },
    {
        path: "/app/department/update/:id",
        component: FormDepartment,
        permission: { feat: features.department, act: 'update' }
    },

    // Designation routes
    {
        path: "/app/designation",
        component: Designation,
        permission: { feat: features.designation, act: 'read' }
    },
    {
        path: "/app/designation/create",
        component: FormDesignation,
        permission: { feat: features.designation, act: 'create' }
    },
    {
        path: "/app/designation/update/:id",
        component: FormDesignation,
        permission: { feat: features.designation, act: 'update' }
    },

    // Attendance routes
    {
        path: "/app/attendance",
        component: Attendance,
        permission: { feat: "attendance", act: "read" }
    },
    {
        path: "/app/attendance/create",
        component: FormAttendance,
        permission: { feat: features.attendance, act: 'create' }
    },
    {
        path: "/app/attendance/update/:id",
        component: FormAttendance,
        permission: { feat: features.attendance, act: 'update' }
    },

    // Admin Expenses routes
    {
        path: "/app/expenses",
        component: Expenses,
        permission: { feat: features.expense, act: 'read' }
    },
    {
        path: "/app/expenses/create",
        component: FormExpenses,
        permission: { feat: features.expense, act: 'create' }
    },
    {
        path: "/app/expenses/update/:id",
        component: FormExpenses,
        permission: { feat: features.expense, act: 'update' }
    },

    // Redirect user expense routes to main expense routes
    {
        path: "/app/user/expenses",
        component: () => <Redirect to="/app/expenses" />,
        permission: { feat: "expense", act: "read" }
    },
    {
        path: "/app/user/expenses/create",
        component: () => <Redirect to="/app/expenses/create" />,
        permission: { feat: "expense", act: "create" }
    },
    {
        path: "/app/user/expenses/update/:id",
        component: (props) => <Redirect to={`/app/expenses/update/${props.match.params.id}`} />,
        permission: { feat: "expense", act: "update" }
    },

    // Leave routes
    {
        path: "/app/leave",
        component: Leaves,
        role: "admin",
        permission: { feat: features.leave, act: 'read' }
    },
    {
        path: "/app/user/leave",
        component: Leaves,
        permission: { feat: "leave", act: "read" }
    },
    {
        path: "/app/leave/create",
        component: FormLeave,
        permission: { feat: features.leave, act: 'create' }
    },
    {
        path: "/app/leave/update/:id",
        component: FormLeave,
        permission: { feat: features.leave, act: 'update' }
    },
    {
        path: "/app/leave/report",
        component: LeaveReport,
        permission: { feat: features.leavereport, act: 'read' }
    },
    {
        path: "/app/leave/approval",
        component: Approval,
        permission: { feat: features.approve, act: 'read' }
    },
    {
        path: "/app/leave/configuration",
        component: LeaveConfiguration,
        permission: { feat: features.configuration, act: 'read' }
    },
    {
        path: "/app/leave/calendar",
        component: LeaveCalendar,
        permission: { feat: features.calendar, act: 'read' }
    },

    // Project routes
    {
        path: '/app/project/list',
        component: ProductList,
        permission: { feat: features.projectlist, act: 'read' }
    },
    {
        path: '/app/project/overview',
        component: ProductOverview,
        permission: { feat: features.projectoverview, act: 'read' }
    },
    {
        path: "/app/timesheet",
        component: Leaves,
        role: "admin",
        permission: { feat: features.leave, act: 'read' }
    },

    // Timeline routes
    {
        path: "/app/timeline/overview",
        component: Overivew,
        permission: { feat: features.overview, act: 'read' }
    },
    {
        path: "/app/timeline/request",
        component: TimeRequest,
        permission: { feat: features.timerequest, act: 'read' }
    },
    {
        path: "/app/timeline/taskrequest",
        component: TaskRequest,
        permission: { feat: features.taskrequest, act: 'read' }
    },
    {
        path: "/app/timeline/workschedule",
        component: WorkSchedule,
        permission: { feat: features.workschedule, act: 'read' }
    },

    // Other routes
    {
        path: "/app/report",
        component: Report,
        permission: { feat: features.report, act: 'read' }
    },
    {
        path: "/app/setting",
        component: Setting,
        permission: { feat: features.setting, act: 'read' }
    },

    // Profile route
    {
        path: "/app/profile",
        component: Profile,
        permission: { feat: 'Profile', act: 'read' }
    },

    // Dashboard routes - no permission required (hardcoded access)
    {
        path: "/app/dashboard",
        component: Dashboard
    },
    {
        path: "/app/admin-dashboard",
        component: Dashboard
    },
    {
        path: "/app/user-dashboard",
        component: Dashboard
    },

    // Timeline routes
    {
        path: "/app/timeline",
        component: Timeline,
        permission: { feat: features.timeline, act: 'read' }
    },
    {
        path: "/app/user/timeline",
        component: Timeline,
        permission: { feat: "Timeline", act: "read" }
    },
    {
        path: "/app/project/timesheet",
        component: ProductTimesheet,
        permission: { feat: features.projecttimesheet, act: 'read' }
    },

    // Client and Task routes
    {
        path: '/app/client',
        component: Client,
        permission: { feat: features.client, act: 'read' }
    },
    {
        path: '/app/project/update/:data',
        component: TaskHistoryAdmin,
        permission: { feat: features.projects, act: 'update' }
    },
    {
        path: '/app/tasks',
        component: Tasklist,
        permission: { feat: "My Tasks", act: "read" }
    },
    {
        path: '/app/user/projects',
        component: ProductListStaff,
        permission: { feat: "Projects", act: "read" }
    },
    {
        path: '/app/user/project/overview',
        component: ProductOverview,
        permission: { feat: "projectoverview", act: "read" }
    },
    {
        path: '/app/user/project/timesheet',
        component: ProductTimesheet,
        permission: { feat: "projecttimesheet", act: "read" }
    },
    {
        path: '/app/user/tasklist/note/:data',
        component: TaskListWithNote,
        permission: { feat: features.tasknote, act: 'read' }
    },

    // Sprint routes
    {
        path: '/app/sprint',
        component: SprintPage,
        permission: { feat: "Sprint", act: "read_all" }
    },
    {
        path: '/app/sprint/form',
        component: SprintPage,
        permission: { feat: "Sprint", act: "create" }
    },
    {
        path: '/app/sprint/form/:id',
        component: SprintPage,
        permission: { feat: "Sprint", act: "update" }
    },
    {
        path: '/app/sprint/:sprintId/tasks',
        component: SprintTasksPage,
        permission: { feat: "Sprint", act: "read_all" }
    },
    {
        path: '/app/user/sprint',
        component: UserSprintPage,
        permission: { feat: "Sprint", act: "read_self" }
    },
    {
        path: '/app/user/sprint/:sprintId/tasks',
        component: SprintTasksPage,
        permission: { feat: "Sprint", act: "read_self" }
    },
];

// Loading component for suspense fallback
const Loader = () => <LoadingScreen />;

export default function Routes() {
const [role,setUserRole] = useState([]);
    const profile = useSelector(UserSelector.profile());
    

    useEffect(() => {
        if (profile) {
            setUserRole(profile.role || []);
        }
    }, [profile]);

    return (
        <Suspense fallback={<Loader />}>
            <Switch>
                {/* Private routes that require authentication */}
                <Route path="/app">
                    <MainLayout>
                        <Suspense fallback={<Loader />}>
                            <Switch>
                                {/* Dashboard is always accessible */}
                                <Route exact path="/app/dashboard" component={Dashboard} />

                                {/* Profile is always accessible */}
                                <Route exact path="/app/profile" component={Profile} />

                                {/* Permission-based routes */}
                                {PrivateRoutes.map((route, i) => {
                                    // All routes now use PermissionRoute
                                    if (route.permission) {
                                        return (
                                            <PermissionRoute
                                                key={i}
                                                exact
                                                path={route.path}
                                                component={route.component}
                                                permission={route.permission}
                                            />
                                        );
                                    }

                                    // If no permission specified (should be rare), use regular Route
                                    return (
                                        <Route
                                            key={i}
                                            exact
                                            path={route.path}
                                            component={route.component}
                                        />
                                    );
                                })}

                                {/* Show NotFound component if no route matches */}
                                <Route component={NotFound} />
                            </Switch>
                        </Suspense>
                    </MainLayout>
                </Route>

                {/* Public routes */}
                <Route exact path="/">
                    <AuthLayout>
                        <Suspense fallback={<Loader />}>
                            <Switch>
                                <Route exact path="/" component={Login} />
                            </Switch>
                        </Suspense>
                    </AuthLayout>
                </Route>

                {/* Access Denied route */}
                <Route path="/access-denied">
                    <MainLayout>
                        <AccessDenied />
                    </MainLayout>
                </Route>

                {/* 404 Not Found route */}
                <Route path="/not-found">
                    <MainLayout>
                        <NotFound />
                    </MainLayout>
                </Route>

                {/* Redirect to not-found if no route matches */}
                <Route>
                    <MainLayout>
                        <NotFound />
                    </MainLayout>
                </Route>
            </Switch>
        </Suspense>
    );
}