{"name": "meraki-backend", "version": "1.0.0", "description": "<PERSON><PERSON><PERSON>", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["hrm"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^10.0.0", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "meraki-backend": "file:", "moment": "^2.29.1", "mongoose": "^6.13.8", "multer": "^1.4.3", "node-cron": "^4.1.0", "node-fetch": "^2.7.0", "nodemon": "^2.0.12", "router": "^2.1.0"}}