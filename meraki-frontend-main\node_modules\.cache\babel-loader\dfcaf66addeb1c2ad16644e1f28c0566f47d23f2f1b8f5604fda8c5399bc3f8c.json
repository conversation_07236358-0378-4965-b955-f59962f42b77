{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const ClientSlice = createSlice({\n  name: \"client\",\n  initialState: {\n    clients: [],\n    pagination: {}\n  },\n  reducers: {\n    getClients: () => {},\n    createClient: () => {},\n    getSuccessfullyClients: (state, action) => {\n      if (action.payload.data === 0) {\n        state.clients = [];\n        state.pagination = {};\n      } else {\n        state.clients = action.payload.data;\n        state.pagination = action.payload.pagination;\n      }\n    },\n    deleteClient: () => {},\n    updateClient: () => {}\n  }\n});\nexport default ClientSlice;", "map": {"version": 3, "names": ["createSlice", "ClientSlice", "name", "initialState", "clients", "pagination", "reducers", "getClients", "createClient", "getSuccessfullyClients", "state", "action", "payload", "data", "deleteClient", "updateClient"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/ClientSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\nexport const ClientSlice = createSlice({\r\n    name: \"client\",\r\n    initialState: {\r\n        clients:[],\r\n        pagination:{}\r\n    },\r\n    reducers: {\r\n        getClients: () => {\r\n        },\r\n        createClient: () => {\r\n        },\r\n        getSuccessfullyClients: (state, action) => {\r\n\r\n            if(action.payload.data === 0) {\r\n                state.clients = [];\r\n                state.pagination = {};\r\n            }else {\r\n                state.clients = action.payload.data;\r\n                state.pagination = action.payload.pagination;\r\n            }\r\n        },\r\n        deleteClient: () => {},\r\n        updateClient: () => {}\r\n    }\r\n});\r\nexport default ClientSlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAC9C,OAAO,MAAMC,WAAW,GAAGD,WAAW,CAAC;EACnCE,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAE;IACVC,OAAO,EAAC,EAAE;IACVC,UAAU,EAAC,CAAC;EAChB,CAAC;EACDC,QAAQ,EAAE;IACNC,UAAU,EAAEA,CAAA,KAAM,CAClB,CAAC;IACDC,YAAY,EAAEA,CAAA,KAAM,CACpB,CAAC;IACDC,sBAAsB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAEvC,IAAGA,MAAM,CAACC,OAAO,CAACC,IAAI,KAAK,CAAC,EAAE;QAC1BH,KAAK,CAACN,OAAO,GAAG,EAAE;QAClBM,KAAK,CAACL,UAAU,GAAG,CAAC,CAAC;MACzB,CAAC,MAAK;QACFK,KAAK,CAACN,OAAO,GAAGO,MAAM,CAACC,OAAO,CAACC,IAAI;QACnCH,KAAK,CAACL,UAAU,GAAGM,MAAM,CAACC,OAAO,CAACP,UAAU;MAChD;IACJ,CAAC;IACDS,YAAY,EAAEA,CAAA,KAAM,CAAC,CAAC;IACtBC,YAAY,EAAEA,CAAA,KAAM,CAAC;EACzB;AACJ,CAAC,CAAC;AACF,eAAed,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}