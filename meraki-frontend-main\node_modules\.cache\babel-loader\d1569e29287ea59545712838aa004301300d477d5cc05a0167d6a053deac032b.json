{"ast": null, "code": "import { createSlice } from \"@reduxjs/toolkit\";\nexport const GeneralSlice = createSlice({\n  name: \"auth\",\n  initialState: {\n    loader: [],\n    errors: [],\n    success: []\n  },\n  reducers: {\n    startLoading: (state, {\n      payload\n    }) => {\n      state.loader = [...state.loader, payload];\n    },\n    stopLoading: (state, action) => {\n      state.loader = state.loader.filter(item => item !== action.payload);\n    },\n    addError: (state, action) => {\n      state.errors = [...state.errors, action.payload];\n    },\n    removeError: (state, action) => {\n      state.errors = state.errors.filter(item => item.action !== action.payload);\n    },\n    addSuccess: (state, action) => {\n      const index = state.success.findIndex(item => item.action === action.payload.action);\n      if (index !== -1) {\n        state.success[index] = action.payload;\n      } else {\n        state.success = [...state.success, action.payload];\n      }\n    },\n    removeSuccess: (state, action) => {\n      state.success = state.success.filter(item => !action.payload.includes(item.action));\n    }\n  }\n});\nexport default GeneralSlice;", "map": {"version": 3, "names": ["createSlice", "GeneralSlice", "name", "initialState", "loader", "errors", "success", "reducers", "startLoading", "state", "payload", "stopLoading", "action", "filter", "item", "addError", "removeError", "addSuccess", "index", "findIndex", "removeSuccess", "includes"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/slices/slice/GeneralSlice.js"], "sourcesContent": ["import { createSlice } from \"@reduxjs/toolkit\";\r\n\r\nexport const GeneralSlice = createSlice({\r\n    name: \"auth\",\r\n    initialState: {\r\n        loader: [],\r\n        errors: [],\r\n        success: [],\r\n    },\r\n    reducers: {\r\n        startLoading: (state, { payload }) => {\r\n            state.loader = [\r\n                ...state.loader,\r\n                payload\r\n            ];\r\n        },\r\n        stopLoading: (state, action) => {\r\n            state.loader = state.loader.filter(item => item !== action.payload);\r\n        },\r\n        addError: (state, action) => {\r\n            state.errors = [...state.errors, action.payload];\r\n        },\r\n        removeError: (state, action) => {\r\n            state.errors = state.errors.filter(item => item.action !== action.payload);\r\n        },\r\n        addSuccess: (state, action) => {\r\n            const index = state.success.findIndex(item => item.action === action.payload.action);\r\n          \r\n            if (index !== -1) {\r\n                state.success[index] = action.payload;\r\n            \r\n            } else {\r\n                state.success = [...state.success, action.payload];\r\n     \r\n            }\r\n        },\r\n        removeSuccess: (state, action) => {\r\n            state.success = state.success.filter(item => !action.payload.includes(item.action));\r\n        }\r\n    }\r\n});\r\n\r\nexport default GeneralSlice;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAE9C,OAAO,MAAMC,YAAY,GAAGD,WAAW,CAAC;EACpCE,IAAI,EAAE,MAAM;EACZC,YAAY,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE;IACNC,YAAY,EAAEA,CAACC,KAAK,EAAE;MAAEC;IAAQ,CAAC,KAAK;MAClCD,KAAK,CAACL,MAAM,GAAG,CACX,GAAGK,KAAK,CAACL,MAAM,EACfM,OAAO,CACV;IACL,CAAC;IACDC,WAAW,EAAEA,CAACF,KAAK,EAAEG,MAAM,KAAK;MAC5BH,KAAK,CAACL,MAAM,GAAGK,KAAK,CAACL,MAAM,CAACS,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKF,MAAM,CAACF,OAAO,CAAC;IACvE,CAAC;IACDK,QAAQ,EAAEA,CAACN,KAAK,EAAEG,MAAM,KAAK;MACzBH,KAAK,CAACJ,MAAM,GAAG,CAAC,GAAGI,KAAK,CAACJ,MAAM,EAAEO,MAAM,CAACF,OAAO,CAAC;IACpD,CAAC;IACDM,WAAW,EAAEA,CAACP,KAAK,EAAEG,MAAM,KAAK;MAC5BH,KAAK,CAACJ,MAAM,GAAGI,KAAK,CAACJ,MAAM,CAACQ,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACF,MAAM,KAAKA,MAAM,CAACF,OAAO,CAAC;IAC9E,CAAC;IACDO,UAAU,EAAEA,CAACR,KAAK,EAAEG,MAAM,KAAK;MAC3B,MAAMM,KAAK,GAAGT,KAAK,CAACH,OAAO,CAACa,SAAS,CAACL,IAAI,IAAIA,IAAI,CAACF,MAAM,KAAKA,MAAM,CAACF,OAAO,CAACE,MAAM,CAAC;MAEpF,IAAIM,KAAK,KAAK,CAAC,CAAC,EAAE;QACdT,KAAK,CAACH,OAAO,CAACY,KAAK,CAAC,GAAGN,MAAM,CAACF,OAAO;MAEzC,CAAC,MAAM;QACHD,KAAK,CAACH,OAAO,GAAG,CAAC,GAAGG,KAAK,CAACH,OAAO,EAAEM,MAAM,CAACF,OAAO,CAAC;MAEtD;IACJ,CAAC;IACDU,aAAa,EAAEA,CAACX,KAAK,EAAEG,MAAM,KAAK;MAC9BH,KAAK,CAACH,OAAO,GAAGG,KAAK,CAACH,OAAO,CAACO,MAAM,CAACC,IAAI,IAAI,CAACF,MAAM,CAACF,OAAO,CAACW,QAAQ,CAACP,IAAI,CAACF,MAAM,CAAC,CAAC;IACvF;EACJ;AACJ,CAAC,CAAC;AAEF,eAAeX,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}