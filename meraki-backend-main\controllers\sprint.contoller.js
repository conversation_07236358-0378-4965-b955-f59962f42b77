'use strict';

const { db } = require("../models");
const Product = db.product;
const User = db.user;
const mongoose = require('mongoose');
const Sprint = require('../models/sprint.model');

/**
 * Create a new sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Created sprint or error message
 */
exports.createSprint = async (req, res) => {
    try {
        const { productId, name, startDate, endDate, goal } = req.body;

        const sprint = await Sprint.create({
            productId: productId || null, // Allow null for global sprints
            name,
            startDate,
            endDate,
            goal,
            createdBy: req.user._id
        });

        return res.status(201).json({
            message: "Sprint created successfully",
            data: sprint
        });
    } catch(err) {
        return res.status(500).json({
            message: 'Failed to create sprint',
            error: err.message
        });
    }
};

/**
 * Get all sprints
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} List of sprints or error message
 */
exports.getAllSprints = async (req, res) => {
    try {
        const sprints = await Sprint.find()
            .populate('productId', 'productName')
            .populate('createdBy', 'name email');
            
        return res.status(200).json({
            message: "Sprints retrieved successfully",
            data: sprints
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to fetch sprints',
            error: err.message
        });
    }
};

/**
 * Get sprints by project
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} List of sprints for a project or error message
 */
exports.getSprintsByProject = async (req, res) => {
    try {
        const { projectId } = req.params;
        
        if (!mongoose.Types.ObjectId.isValid(projectId)) {
            return res.status(400).json({ message: 'Invalid project ID format' });
        }
        
        const sprints = await Sprint.find({ productId: projectId })
            .populate('productId', 'productName')
            .populate('createdBy', 'name email');
            
        return res.status(200).json({
            message: "Project sprints retrieved successfully",
            data: sprints
        });
    } catch (err){
        return res.status(500).json({
            message: 'Failed to fetch project sprints',
            error: err.message
        });
    }
};

/**
 * Get sprints by user
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} List of sprints for a user or error message
 */
exports.getSprintsByUser = async (req, res) => {
    try {
        const { userId } = req.params;
        
        if (!mongoose.Types.ObjectId.isValid(userId)) {
            return res.status(400).json({ message: 'Invalid user ID format' });
        }
        
        const sprints = await Sprint.find({ createdBy: userId })
            .populate('productId', 'productName')
            .populate('createdBy', 'name email');
            
        return res.status(200).json({
            message: "User sprints retrieved successfully",
            data: sprints
        });
    } catch (err){
        return res.status(500).json({
            message: 'Failed to fetch user sprints',
            error: err.message
        });
    }
};

/**
 * Get sprint details with tasks
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Sprint details with tasks or error message
 */
exports.getSprintDetails = async (req, res) => {
    try {
        const { sprintId } = req.params;
        
        if (!mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid sprint ID format' });
        }
        
        // Get the sprint
        const sprint = await Sprint.findById(sprintId)
            .populate('productId', 'productName')
            .populate('createdBy', 'name email');
            
        if (!sprint) {
            return res.status(404).json({ message: 'Sprint not found' });
        }
        
        // Get all tasks for this sprint
        // Since tasks are embedded in products, we need to find products with tasks in this sprint
        const products = await Product.find({
            'taskArr.sprintId': mongoose.Types.ObjectId(sprintId)
        });
        
        // Extract tasks that belong to this sprint
        const tasks = [];
        products.forEach(product => {
            const sprintTasks = product.taskArr.filter(task => 
                task.sprintId && task.sprintId.toString() === sprintId
            );
            
            sprintTasks.forEach(task => {
                tasks.push({
                    ...task.toObject(),
                    productId: product._id,
                    productName: product.productName
                });
            });
        });
            
        // Return combined data
        return res.status(200).json({
            message: "Sprint details retrieved successfully",
            data: {
                sprint,
                tasks
            }
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to fetch sprint details',
            error: err.message
        });
    }
};

/**
 * Update a sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated sprint or error message
 */
exports.updateSprint = async (req, res) => {
    try {
        const { sprintId } = req.params;
        const { name, startDate, endDate, goal, status } = req.body;
        
        if (!mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid sprint ID format' });
        }
        
        const sprint = await Sprint.findById(sprintId);
        if (!sprint) {
            return res.status(404).json({ message: 'Sprint not found' });
        }
        
        // Update sprint fields
        if (name) sprint.name = name;
        if (startDate) sprint.startDate = startDate;
        if (endDate) sprint.endDate = endDate;
        if (goal !== undefined) sprint.goal = goal;
        if (status) sprint.status = status;
        
        await sprint.save();
        
        return res.status(200).json({
            message: "Sprint updated successfully",
            data: sprint
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to update sprint',
            error: err.message
        });
    }
};

/**
 * Delete a sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Success message or error message
 */
exports.deleteSprint = async (req, res) => {
    try {
        const { sprintId } = req.params;
        
        if (!mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid sprint ID format' });
        }
        
        const sprint = await Sprint.findById(sprintId);
        if (!sprint) {
            return res.status(404).json({ message: 'Sprint not found' });
        }
        
        // Remove sprint ID from all tasks
        const products = await Product.find({
            'taskArr.sprintId': mongoose.Types.ObjectId(sprintId)
        });
        
        for (const product of products) {
            product.taskArr.forEach(task => {
                if (task.sprintId && task.sprintId.toString() === sprintId) {
                    task.sprintId = null;
                }
            });
            await product.save();
        }
        
        // Delete the sprint
        await Sprint.findByIdAndDelete(sprintId);
        
        return res.status(200).json({
            message: "Sprint deleted successfully"
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to delete sprint',
            error: err.message
        });
    }
};

/**
 * Add a task to a sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated task or error message
 */
exports.addTaskToSprint = async (req, res) => {
    try {
        const { productId, taskId, sprintId } = req.body;
        
        if (!mongoose.Types.ObjectId.isValid(productId) || 
            !mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid ID format' });
        }
        
        // Find the product containing the task
        const product = await Product.findById(productId);
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }
        
        // Find the task in the product
        const taskIndex = product.taskArr.findIndex(task => task._id.toString() === taskId);
        if (taskIndex === -1) {
            return res.status(404).json({ message: 'Task not found in product' });
        }
        
        // Check if sprint exists
        const sprint = await Sprint.findById(sprintId);
        if (!sprint) {
            return res.status(404).json({ message: 'Sprint not found' });
        }
        
        // Add sprint ID to the task and set addToSprint flag to true
        product.taskArr[taskIndex].sprintId = sprintId;
        product.taskArr[taskIndex].addToSprint = true;
        
        // Save the updated product
        await product.save();
        
        return res.status(200).json({
            message: "Task added to sprint successfully",
            data: product.taskArr[taskIndex]
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to add task to sprint',
            error: err.message
        });
    }
};

/**
 * Remove a task from a sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated task or error message
 */
exports.removeTaskFromSprint = async (req, res) => {
    try {
        const { sprintId, taskId } = req.params;
        
        if (!mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid sprint ID format' });
        }
        
        // Find all products with tasks in this sprint
        const products = await Product.find({
            'taskArr.sprintId': mongoose.Types.ObjectId(sprintId)
        });
        
        let taskFound = false;
        
        for (const product of products) {
            const taskIndex = product.taskArr.findIndex(task => 
                task._id.toString() === taskId && 
                task.sprintId && 
                task.sprintId.toString() === sprintId
            );
            
            if (taskIndex !== -1) {
                // Remove sprint ID from the task
                product.taskArr[taskIndex].sprintId = null;
                await product.save();
                taskFound = true;
                
                return res.status(200).json({
                    message: "Task removed from sprint successfully",
                    data: product.taskArr[taskIndex]
                });
            }
        }
        
        if (!taskFound) {
            return res.status(404).json({ message: 'Task not found in sprint' });
        }
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to remove task from sprint',
            error: err.message
        });
    }
};

/**
 * Add a project to a sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated project or error message
 */
exports.addProjectToSprint = async (req, res) => {
    try {
        const { productId, sprintId } = req.body;
        
        if (!mongoose.Types.ObjectId.isValid(productId) || 
            !mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid ID format' });
        }
        
        // Find the product
        const product = await Product.findById(productId);
        if (!product) {
            return res.status(404).json({ message: 'Product not found' });
        }
        
        // Check if sprint exists
        const sprint = await Sprint.findById(sprintId);
        if (!sprint) {
            return res.status(404).json({ message: 'Sprint not found' });
        }
        
        // Update the sprint with the project ID
        sprint.productId = productId;
        await sprint.save();
        
        // Add sprint ID to all tasks in the project
        product.taskArr.forEach(task => {
            task.sprintId = sprintId;
        });
        
        // Save the updated product
        await product.save();
        
        return res.status(200).json({
            message: "Project added to sprint successfully",
            data: {
                sprint,
                product: {
                    _id: product._id,
                    productName: product.productName,
                    taskCount: product.taskArr.length
                }
            }
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to add project to sprint',
            error: err.message
        });
    }
};

/**
 * Start a sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated sprint or error message
 */
exports.startSprint = async (req, res) => {
    try {
        const { sprintId } = req.params;
        
        if (!mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid sprint ID format' });
        }
        
        const sprint = await Sprint.findById(sprintId);
        if (!sprint) {
            return res.status(404).json({ message: 'Sprint not found' });
        }
        
        // Update sprint status
        sprint.status = 'active';
        await sprint.save();
        
        return res.status(200).json({
            message: "Sprint started successfully",
            data: sprint
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to start sprint',
            error: err.message
        });
    }
};

/**
 * Complete a sprint
 * 
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated sprint or error message
 */
exports.completeSprint = async (req, res) => {
    try {
        const { sprintId } = req.params;
        
        if (!mongoose.Types.ObjectId.isValid(sprintId)) {
            return res.status(400).json({ message: 'Invalid sprint ID format' });
        }
        
        const sprint = await Sprint.findById(sprintId);
        if (!sprint) {
            return res.status(404).json({ message: 'Sprint not found' });
        }
        
        // Update sprint status
        sprint.status = 'completed';
        await sprint.save();
        
        return res.status(200).json({
            message: "Sprint completed successfully",
            data: sprint
        });
    } catch (err) {
        return res.status(500).json({
            message: 'Failed to complete sprint',
            error: err.message
        });
    }
};