{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\LeaveReport\\\\LeaveReport.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Box, Card, Table, TableBody, TableCell, TableHead, TableRow, Typography, TableContainer, Paper, Pagination, Dialog, DialogContent, DialogTitle, DialogActions, MenuItem, Select } from \"@mui/material\";\nimport { NewReleases, Refresh } from \"@mui/icons-material\";\nimport styled from \"@emotion/styled\";\nimport { DefaultSort } from \"constants/sort\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { AttendanceSelector, LeaveSelector, UserSelector } from \"selectors\";\nimport { AttendanceActions, UserActions, LeaveActions } from \"slices/actions\";\nimport { Button } from \"react-bootstrap\";\nimport moment from \"moment\";\nimport { SettingSelector } from \"selectors/SettingSelector\";\n\n// import { Button } from \"react-bootstrap\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction LeaveReport() {\n  _s();\n  const [filter, setFilter] = useState({\n    sort: DefaultSort.newest.value,\n    page: 1\n  });\n  const [currentMonthSelectRange, setCurrentMonthSelectRange] = useState(new Date().getMonth());\n  const handleChangePagination = (e, val) => {\n    setFilter({\n      ...filter,\n      page: val\n    });\n  };\n  const [currentYear, setCurrentYear] = useState(new Date().getFullYear());\n  const pagination = useSelector(UserSelector.getPagination());\n  const handleYearChange = event => {\n    setCurrentYear(event.target.value);\n  };\n  let users = useSelector(UserSelector.getUsers());\n  // const [users,setUsers] = useState([])\n  const dispatch = useDispatch();\n  const [detailsController, setDetailsController] = useState(false);\n  const leaves = useSelector(LeaveSelector.getLeaves());\n  const [userSelect, setUserSelected] = useState(\"\");\n  const [currentMonth, setCurrectMonth] = useState(new Date().getMonth() + 1);\n  const handleMonthChange = event => {\n    setCurrentMonthSelectRange(event.target.value);\n    setCurrectMonth(event.target.value);\n  };\n  const attendaces = useSelector(AttendanceSelector.getAttendances());\n  let [daysOfMonth, setDaysOfMonth] = useState(0);\n  const [workingDays, setWorkingDays] = useState(0);\n  const setting = useSelector(SettingSelector.getSetting());\n  useEffect(() => {\n    console.log(\"🔄 LeaveReport: Initial data fetch\");\n    dispatch(UserActions.getUsers());\n    dispatch(LeaveActions.getAllLeaves()); // Fetch leaves data\n    const now = new Date(currentYear, currentMonthSelectRange + 1, 0);\n    console.log(\"NOW \", now);\n    dispatch(AttendanceActions.getAttendancesByMonth({\n      startDate: new Date(now.getFullYear(), now.getMonth(), 1),\n      endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0)\n    }));\n    numberOfWorkingDays();\n  }, []);\n  useEffect(() => {\n    const now = new Date(currentYear, currentMonthSelectRange + 1, 0);\n    console.log(\"🔄 LeaveReport: Range changed, refreshing data\");\n    dispatch(UserActions.getUsers()); // Refresh users to get updated leave counts\n    dispatch(LeaveActions.getAllLeaves()); // Refresh leaves data\n    dispatch(AttendanceActions.getAttendancesByMonth({\n      startDate: new Date(now.getFullYear(), now.getMonth(), 1),\n      endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0)\n    }));\n    numberOfWorkingDays();\n  }, [currentMonthSelectRange, currentYear]);\n\n  // Add periodic refresh to catch real-time updates\n  useEffect(() => {\n    const refreshInterval = setInterval(() => {\n      console.log(\"🔄 LeaveReport: Periodic refresh for real-time updates\");\n      dispatch(UserActions.getUsers()); // Refresh users to get updated leave counts\n      dispatch(LeaveActions.getAllLeaves()); // Refresh leaves data\n    }, 30000); // Refresh every 30 seconds\n\n    return () => clearInterval(refreshInterval);\n  }, [dispatch]);\n  const FilterBox = styled(Box)(() => ({\n    width: \"100%\",\n    marginTop: 30,\n    marginBottom: 20,\n    display: \"flex\",\n    justifyContent: \"space-between\"\n  }));\n  function numberOfWorkingDays() {\n    const daysOfMonth = new Date(currentYear, currentMonth + 1, 0).getDate();\n    setDaysOfMonth(daysOfMonth);\n    let workingDaysCount = 0;\n    for (let i = 1; i <= daysOfMonth; i++) {\n      let tempDate = new Date(currentYear, currentMonth, i);\n      let dayOfWeek = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\n\n      // Skip the specified day of the week\n      if (dayOfWeek[tempDate.getDay()] !== setting.day) {\n        // Check company holidays\n        if (setting !== null && setting !== void 0 && setting.companyLeaveArr && (setting === null || setting === void 0 ? void 0 : setting.companyLeaveArr.length) > 0) {\n          const isHoliday = setting.companyLeaveArr.some(element => {\n            return new Date(element.leaveDate).setHours(0, 0, 0, 0) === tempDate.setHours(0, 0, 0, 0);\n          });\n\n          // Increment the count of working days if not a holiday\n          if (!isHoliday) {\n            workingDaysCount += 1;\n          }\n        } else {\n          // Increment the count of working days if there are no company holidays\n          workingDaysCount += 1;\n        }\n      }\n    }\n    setWorkingDays(workingDaysCount);\n    console.log(\"Total Working Days:\", workingDaysCount);\n  }\n  function handleClose() {\n    setDetailsController(false);\n  }\n  const getStatusText = (status, updatedByAdmin) => {\n    if (status === 0 && updatedByAdmin) {\n      return \"Rejected\";\n    } else if (status === 1 && updatedByAdmin) {\n      return \"Approved\";\n    } else if (status === 0 && !updatedByAdmin) {\n      return \"Pending\";\n    } else {\n      return null;\n    }\n  };\n  function detailPopUp(data) {\n    console.log(\"Detasil Pop up \", data);\n    return /*#__PURE__*/_jsxDEV(Dialog, {\n      open: detailsController,\n      maxWidth: \"md\",\n      onClose: handleClose,\n      PaperProps: {\n        component: 'form',\n        sx: {\n          width: '1000px',\n          height: '400px'\n        },\n        onSubmit: event => {\n          setDetailsController(false);\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"Leave Details\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            justifyContent: \"space-evenly\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"200px\",\n              height: \"100px\",\n              border: '1px solid grey',\n              display: \"flex\",\n              justifyContent: \"space-evenly\",\n              alignItems: \"center\",\n              flexDirection: \"column\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                justifySelf: \"center\"\n              },\n              children: \"Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                justifySelf: \"center\",\n                fontWeight: \"bold\"\n              },\n              children: data.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"200px\",\n              height: \"100px\",\n              border: '1px solid grey',\n              display: \"flex\",\n              justifyContent: \"space-evenly\",\n              alignItems: \"center\",\n              flexDirection: \"column\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                justifySelf: \"center\"\n              },\n              children: \"Leave Taken\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: \"flex\",\n                justifyContent: \"space-evenly\",\n                alignItems: \"center\",\n                gap: \"20px\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: \"bold\",\n                    alignItems: \"center\"\n                  },\n                  children: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: \"bold\",\n                    alignItems: \"center\"\n                  },\n                  children: \"Paid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  flexDirection: \"column\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: \"bold\"\n                  },\n                  children: \"0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    fontWeight: \"bold\"\n                  },\n                  children: \"Unpaid\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 25\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: \"200px\",\n              height: \"100px\",\n              border: '1px solid grey',\n              display: \"flex\",\n              justifyContent: \"space-evenly\",\n              alignItems: \"center\",\n              flexDirection: \"column\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                justifySelf: \"center\"\n              },\n              children: \"Leaves Pending\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                justifySelf: \"center\",\n                fontWeight: \"bold\"\n              },\n              children: \"0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: \"flex\",\n            justifyContent: \"space-evenly\",\n            paddingTop: \"10px\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: \"1px solid black\"\n            },\n            children: \"Casual: 0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: \"1px solid black\"\n            },\n            children: \"Medical: 0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              border: \"1px solid black\"\n            },\n            children: \"Unpaid: 0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            paddingTop: \"20px\"\n          },\n          children: /*#__PURE__*/_jsxDEV(TableContainer, {\n            component: Paper,\n            children: /*#__PURE__*/_jsxDEV(Table, {\n              sx: {\n                minWidth: 650\n              },\n              \"aria-label\": \"simple table\",\n              children: [/*#__PURE__*/_jsxDEV(TableHead, {\n                children: /*#__PURE__*/_jsxDEV(TableRow, {\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    children: \"#\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Date\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Description\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Requested Type\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 217,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: \"Status\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n                children: leaves.map((row, index) => {\n                  console.log(\"row.user:\", row.user);\n                  console.log(\"data._id:\", data._id);\n                  console.log(\"Condition:\", row.user === data._id);\n                  return row.user._id === data._id ? /*#__PURE__*/_jsxDEV(TableRow, {\n                    sx: {\n                      \"&:last-child td, &:last-child th\": {\n                        border: 0\n                      }\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                      component: \"th\",\n                      scope: \"row\",\n                      children: index + 1\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 232,\n                      columnNumber: 35\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      children: moment(row === null || row === void 0 ? void 0 : row.start).format(\"DD-MM-yyyy\")\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 235,\n                      columnNumber: 35\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      children: row === null || row === void 0 ? void 0 : row.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 35\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      children: row === null || row === void 0 ? void 0 : row.type\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 237,\n                      columnNumber: 35\n                    }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                      align: \"center\",\n                      children: getStatusText(row.status, row.updatedByAdmin)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 238,\n                      columnNumber: 35\n                    }, this)]\n                  }, row === null || row === void 0 ? void 0 : row.name, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 33\n                  }, this) : null;\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"submit\",\n          children: \"Submit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 249,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 247,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 12\n    }, this);\n  }\n  function checkUserAttendDays(data) {\n    let count = 0;\n    if (attendaces) {\n      //  console.log(\"2 Check user attendances \",data)\n\n      attendaces.forEach(element => {\n        // console.log(\"2 Check user attendances \",data._id,element.user)\n        if (element.user === data._id) {\n          count++;\n        }\n      });\n    }\n    // console.log(\"Count\",count)\n    return count;\n  }\n\n  // Manual refresh function\n  const handleRefresh = () => {\n    console.log(\"🔄 LeaveReport: Manual refresh triggered\");\n    dispatch(UserActions.getUsers()); // Refresh users to get updated leave counts\n    dispatch(LeaveActions.getAllLeaves()); // Refresh leaves data\n\n    // Also refresh attendance data for current month\n    const now = new Date(currentYear, currentMonthSelectRange + 1, 0);\n    dispatch(AttendanceActions.getAttendancesByMonth({\n      startDate: new Date(now.getFullYear(), now.getMonth(), 1),\n      endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0)\n    }));\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [detailsController ? detailPopUp(userSelect) : null, /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          sx: {\n            fontWeight: 600\n          },\n          children: \"Report\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"outlined\",\n          startIcon: /*#__PURE__*/_jsxDEV(Refresh, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 22\n          }, this),\n          onClick: handleRefresh,\n          sx: {\n            ml: 2\n          },\n          children: \"Refresh Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        value: currentMonthSelectRange,\n        onChange: handleMonthChange,\n        children: Array.from({\n          length: 12\n        }, (_, index) => /*#__PURE__*/_jsxDEV(MenuItem, {\n          value: index,\n          children: new Date(currentYear, index).toLocaleString('default', {\n            month: 'long'\n          })\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 11\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Select, {\n        value: currentYear,\n        onChange: handleYearChange,\n        children: Array.from({\n          length: 101\n        }, (_, index) => {\n          const year = new Date().getFullYear() - 50 + index;\n          return /*#__PURE__*/_jsxDEV(MenuItem, {\n            value: year,\n            children: year\n          }, year, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 21\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(FilterBox, {\n        children: /*#__PURE__*/_jsxDEV(TableContainer, {\n          component: Paper,\n          children: /*#__PURE__*/_jsxDEV(Table, {\n            sx: {\n              minWidth: 650\n            },\n            \"aria-label\": \"simple table\",\n            children: [/*#__PURE__*/_jsxDEV(TableHead, {\n              children: /*#__PURE__*/_jsxDEV(TableRow, {\n                children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                  children: \"Name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 331,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Total Working Days\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 332,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Attended Days\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Total Leaves Taken\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Paid Leaves\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 335,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Unpaid Leaves\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 336,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Other Leaves\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 337,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                  align: \"center\",\n                  children: \"Leaves Details\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 338,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n              children: users.map(row => {\n                // Calculate leave statistics for this user\n                const attendedDays = checkUserAttendDays(row);\n                const totalLeaves = (row === null || row === void 0 ? void 0 : row.totalLeaves) || 0;\n                const leaveTaken = (row === null || row === void 0 ? void 0 : row.leaveTaken) || 0;\n                const availableLeaves = (row === null || row === void 0 ? void 0 : row.availableLeaves) || 0;\n                const paidLeaves = (row === null || row === void 0 ? void 0 : row.paidLeaves) || 0;\n                const unpaidLeaves = (row === null || row === void 0 ? void 0 : row.unpaidLeaves) || 0;\n                const otherLeaves = (row === null || row === void 0 ? void 0 : row.otherLeaves) || 0;\n\n                // Debug logging for leave data\n                console.log(`📊 LeaveReport: User ${row === null || row === void 0 ? void 0 : row.name} leave data:`, {\n                  totalLeaves,\n                  leaveTaken,\n                  availableLeaves,\n                  paidLeaves,\n                  unpaidLeaves,\n                  otherLeaves,\n                  attendedDays,\n                  workingDays\n                });\n                return /*#__PURE__*/_jsxDEV(TableRow, {\n                  sx: {\n                    \"&:last-child td, &:last-child th\": {\n                      border: 0\n                    }\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                    component: \"th\",\n                    scope: \"row\",\n                    children: row === null || row === void 0 ? void 0 : row.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 369,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: workingDays\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 372,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: attendedDays\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: leaveTaken\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 374,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: paidLeaves\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: unpaidLeaves\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: otherLeaves\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                    align: \"center\",\n                    children: /*#__PURE__*/_jsxDEV(Button, {\n                      variant: \"contained\",\n                      style: {\n                        padding: 0\n                      },\n                      onClick: () => {\n                        setDetailsController(true);\n                        setUserSelected(row);\n                      },\n                      children: /*#__PURE__*/_jsxDEV(NewReleases, {\n                        style: {\n                          width: 24,\n                          height: 24\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 387,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 379,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 378,\n                    columnNumber: 21\n                  }, this)]\n                }, row === null || row === void 0 ? void 0 : row.name, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n        sx: {\n          mt: 1\n        },\n        page: filter.page,\n        count: pagination.pages,\n        onChange: handleChangePagination\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 5\n    }, this)]\n  }, void 0, true);\n}\n_s(LeaveReport, \"h9d77FtKGajrHqeFID0mPuBZDHA=\", false, function () {\n  return [useSelector, useSelector, useDispatch, useSelector, useSelector, useSelector];\n});\n_c = LeaveReport;\nexport default LeaveReport;\nvar _c;\n$RefreshReg$(_c, \"LeaveReport\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Box", "Card", "Table", "TableBody", "TableCell", "TableHead", "TableRow", "Typography", "TableContainer", "Paper", "Pagination", "Dialog", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogTitle", "DialogActions", "MenuItem", "Select", "NewReleases", "Refresh", "styled", "DefaultSort", "useDispatch", "useSelector", "AttendanceSelector", "LeaveSelector", "UserSelector", "AttendanceActions", "UserActions", "LeaveActions", "<PERSON><PERSON>", "moment", "SettingSelector", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "LeaveReport", "_s", "filter", "setFilter", "sort", "newest", "value", "page", "currentMonthSelectRange", "setCurrentMonthSelectRange", "Date", "getMonth", "handleChangePagination", "e", "val", "currentYear", "setCurrentYear", "getFullYear", "pagination", "getPagination", "handleYearChange", "event", "target", "users", "getUsers", "dispatch", "detailsController", "setDetailsController", "leaves", "getLeaves", "userSelect", "setUserSelected", "currentMonth", "setCurrectMonth", "handleMonthChange", "attendaces", "getAttendances", "daysOfMonth", "setDaysOfMonth", "workingDays", "setWorkingDays", "setting", "getSetting", "console", "log", "getAllLeaves", "now", "getAttendancesByMonth", "startDate", "endDate", "numberOfWorkingDays", "refreshInterval", "setInterval", "clearInterval", "FilterBox", "width", "marginTop", "marginBottom", "display", "justifyContent", "getDate", "workingDaysCount", "i", "tempDate", "dayOfWeek", "getDay", "day", "companyLeaveArr", "length", "isHoliday", "some", "element", "leaveDate", "setHours", "handleClose", "getStatusText", "status", "updatedByAdmin", "detailPopUp", "data", "open", "max<PERSON><PERSON><PERSON>", "onClose", "PaperProps", "component", "sx", "height", "onSubmit", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "border", "alignItems", "flexDirection", "justifySelf", "fontWeight", "name", "gap", "paddingTop", "min<PERSON><PERSON><PERSON>", "align", "map", "row", "index", "user", "_id", "scope", "start", "format", "description", "type", "checkUserAttendDays", "count", "for<PERSON>ach", "handleRefresh", "mb", "variant", "startIcon", "onClick", "ml", "onChange", "Array", "from", "_", "toLocaleString", "month", "year", "attendedDays", "totalLeaves", "leaveTaken", "availableLeaves", "paidLeaves", "unpaidLeaves", "otherLeaves", "padding", "mt", "pages", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/LeaveReport/LeaveReport.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport {\r\n  Box,\r\n  Card,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableRow,\r\n  Typography,\r\n  TableContainer,\r\n  Paper,\r\n  Pagination,\r\n  Dialog,\r\n  DialogContent,\r\n  DialogTitle,\r\n  DialogActions,\r\n  MenuItem,\r\n  Select\r\n} from \"@mui/material\";\r\n\r\nimport {\r\n  NewReleases,\r\n  Refresh\r\n} from \"@mui/icons-material\";\r\nimport styled from \"@emotion/styled\";\r\nimport { DefaultSort } from \"constants/sort\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { AttendanceSelector, LeaveSelector, UserSelector } from \"selectors\";\r\nimport { AttendanceActions, UserActions, LeaveActions } from \"slices/actions\";\r\nimport { Button } from \"react-bootstrap\";\r\nimport moment from \"moment\";\r\nimport { SettingSelector } from \"selectors/SettingSelector\";\r\n\r\n\r\n// import { Button } from \"react-bootstrap\";\r\n\r\nfunction LeaveReport() {\r\n  const [filter, setFilter] = useState({\r\n    sort: DefaultSort.newest.value,\r\n    page: 1,\r\n  });\r\n  \r\n    const [currentMonthSelectRange, setCurrentMonthSelectRange] = useState(new Date().getMonth());\r\n    \r\n    const handleChangePagination = (e, val) => {\r\n      setFilter({\r\n        ...filter,\r\n        page: val,\r\n      });\r\n    };\r\n    \r\n    const [currentYear, setCurrentYear] = useState(new Date().getFullYear());\r\n    const pagination = useSelector(UserSelector.getPagination());\r\n    const handleYearChange = (event) => { setCurrentYear(event.target.value); };\r\n    let users = useSelector(UserSelector.getUsers());\r\n    // const [users,setUsers] = useState([])\r\n    const dispatch = useDispatch()\r\n    const [detailsController,setDetailsController] = useState(false)\r\n    const leaves = useSelector(LeaveSelector.getLeaves())\r\n    const [userSelect,setUserSelected] = useState(\"\")\r\n    const [currentMonth,setCurrectMonth] = useState(new Date().getMonth()+1)\r\n    const handleMonthChange = (event) => { setCurrentMonthSelectRange(event.target.value);\r\n        setCurrectMonth(event.target.value)\r\n     }; \r\n  const attendaces = useSelector(AttendanceSelector.getAttendances())\r\n  let [daysOfMonth,setDaysOfMonth] = useState(0)\r\n  const [workingDays,setWorkingDays] = useState(0)\r\n  const setting = useSelector(SettingSelector.getSetting())\r\n  useEffect(() => {\r\n      console.log(\"🔄 LeaveReport: Initial data fetch\");\r\n      dispatch(UserActions.getUsers());\r\n      dispatch(LeaveActions.getAllLeaves()); // Fetch leaves data\r\n      const now = new Date(currentYear,currentMonthSelectRange+1,0);\r\n      console.log(\"NOW \",now)\r\n      dispatch(AttendanceActions.getAttendancesByMonth({\r\n        startDate: new Date(now.getFullYear(), now.getMonth(), 1),\r\n        endDate : new Date(now.getFullYear(), now.getMonth() + 1, 0)\r\n      }))\r\n      numberOfWorkingDays()\r\n\r\n  },[])\r\n\r\n  useEffect(() => {\r\n    const now = new Date(currentYear,currentMonthSelectRange+1,0);\r\n    console.log(\"🔄 LeaveReport: Range changed, refreshing data\");\r\n    dispatch(UserActions.getUsers()); // Refresh users to get updated leave counts\r\n    dispatch(LeaveActions.getAllLeaves()); // Refresh leaves data\r\n    dispatch(AttendanceActions.getAttendancesByMonth({\r\n      startDate: new Date(now.getFullYear(), now.getMonth(), 1),\r\n      endDate : new Date(now.getFullYear(), now.getMonth() + 1, 0)\r\n    }))\r\n    numberOfWorkingDays()\r\n  },[currentMonthSelectRange,currentYear])\r\n\r\n  // Add periodic refresh to catch real-time updates\r\n  useEffect(() => {\r\n    const refreshInterval = setInterval(() => {\r\n      console.log(\"🔄 LeaveReport: Periodic refresh for real-time updates\");\r\n      dispatch(UserActions.getUsers()); // Refresh users to get updated leave counts\r\n      dispatch(LeaveActions.getAllLeaves()); // Refresh leaves data\r\n    }, 30000); // Refresh every 30 seconds\r\n\r\n    return () => clearInterval(refreshInterval);\r\n  }, [dispatch]);\r\n \r\n  const FilterBox = styled(Box)(() => ({\r\n    width: \"100%\",\r\n    marginTop: 30,\r\n    marginBottom: 20,\r\n    display: \"flex\",\r\n    justifyContent: \"space-between\",\r\n  }));\r\n\r\n  function numberOfWorkingDays() {\r\n    const daysOfMonth = new Date(currentYear, currentMonth + 1, 0).getDate();\r\n    setDaysOfMonth(daysOfMonth);\r\n\r\n    let workingDaysCount = 0;\r\n\r\n    for (let i = 1; i <= daysOfMonth; i++) {\r\n        let tempDate = new Date(currentYear, currentMonth, i);\r\n        let dayOfWeek = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\r\n\r\n        // Skip the specified day of the week\r\n        if (dayOfWeek[tempDate.getDay()] !== setting.day) {\r\n            // Check company holidays\r\n            if (setting?.companyLeaveArr && setting?.companyLeaveArr.length > 0) {\r\n                const isHoliday = setting.companyLeaveArr.some(element => {\r\n                    return new Date(element.leaveDate).setHours(0, 0, 0, 0) === tempDate.setHours(0, 0, 0, 0);\r\n                });\r\n\r\n                // Increment the count of working days if not a holiday\r\n                if (!isHoliday) {\r\n                    workingDaysCount += 1;\r\n                }\r\n            } else {\r\n                // Increment the count of working days if there are no company holidays\r\n                workingDaysCount += 1;\r\n            }\r\n        }\r\n    }\r\n\r\n    setWorkingDays(workingDaysCount);\r\n    console.log(\"Total Working Days:\", workingDaysCount);\r\n}\r\n\r\n  function handleClose() {\r\n    setDetailsController(false)\r\n  }\r\n\r\n  const getStatusText = (status, updatedByAdmin) => {\r\n    if (status === 0 && updatedByAdmin) {\r\n      return \"Rejected\";\r\n    } else if (status === 1 && updatedByAdmin) {\r\n      return \"Approved\";\r\n    } else if (status === 0 && !updatedByAdmin) {\r\n      return \"Pending\";\r\n    } else {\r\n      return null;\r\n    }\r\n  };\r\n\r\n  function detailPopUp(data) {\r\n    console.log(\"Detasil Pop up \",data)\r\n    return <Dialog \r\n      open={detailsController}\r\n      maxWidth=\"md\"\r\n      onClose={handleClose}\r\n      PaperProps={{\r\n        component: 'form',\r\n        sx: { width: '1000px', height: '400px' },\r\n        onSubmit: (event) => {\r\n            setDetailsController(false)\r\n        }\r\n      }}\r\n    >\r\n      <DialogTitle>Leave Details</DialogTitle>\r\n      <DialogContent>\r\n            <div style={{ display: \"flex\", justifyContent: \"space-evenly\" }}>\r\n                <div style={{ width: \"200px\", height: \"100px\", border: '1px solid grey', display: \"flex\", justifyContent: \"space-evenly\", alignItems: \"center\", flexDirection: \"column\" }}>\r\n                    <div style={{ justifySelf: \"center\" }}>Name</div>\r\n                    <div style={{ justifySelf: \"center\", fontWeight: \"bold\" }}>{data.name}</div>\r\n                </div>\r\n                <div style={{ width: \"200px\", height: \"100px\", border: '1px solid grey', display: \"flex\", justifyContent: \"space-evenly\", alignItems: \"center\", flexDirection: \"column\" }}>\r\n                    <div style={{ justifySelf: \"center\" }}>Leave Taken</div>\r\n                    <div style={{ display: \"flex\",  justifyContent:\"space-evenly\",alignItems: \"center\", gap:\"20px\" }}>\r\n                        <div style={{display:\"flex\",alignItems:\"center\",flexDirection:\"column\"}}>\r\n                        <div style={{ fontWeight: \"bold\", alignItems:\"center\" }}>0</div>\r\n                        <div style={{ fontWeight: \"bold\",alignItems:\"center\" }}>Paid</div>\r\n                        </div>\r\n                        <div style={{display:\"flex\",alignItems:\"center\",flexDirection:\"column\"}}>\r\n                        <div style={{ fontWeight: \"bold\" }}>0</div>\r\n                        <div style={{ fontWeight: \"bold\" }}>Unpaid</div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n                <div style={{ width: \"200px\", height: \"100px\", border: '1px solid grey', display: \"flex\", justifyContent: \"space-evenly\", alignItems: \"center\", flexDirection: \"column\" }}>\r\n                    <div style={{ justifySelf: \"center\" }}>Leaves Pending</div>\r\n                    <div style={{ justifySelf: \"center\", fontWeight: \"bold\" }}>0</div>\r\n                </div>\r\n               \r\n            </div>\r\n            <div style={{display:\"flex\",justifyContent:\"space-evenly\", paddingTop:\"10px\"}}>\r\n              <div style={{border:\"1px solid black\"}}>Casual: 0</div>\r\n              <div style={{border:\"1px solid black\"}}>Medical: 0</div>\r\n              <div style={{border:\"1px solid black\"}}>Unpaid: 0</div>\r\n            </div>\r\n            <div style={{paddingTop:\"20px\"}}>\r\n            <TableContainer component={Paper}>\r\n                <Table sx={{ minWidth: 650 }} aria-label=\"simple table\">\r\n                  <TableHead>\r\n                    <TableRow>\r\n                      <TableCell>#</TableCell>\r\n                      <TableCell align=\"center\">Date</TableCell>\r\n                      <TableCell align=\"center\">Description</TableCell>\r\n                      <TableCell align=\"center\">Requested Type</TableCell>\r\n                      <TableCell align=\"center\">Status</TableCell>\r\n                    </TableRow>\r\n                  </TableHead>\r\n                  <TableBody>\r\n                            {leaves.map((row,index) => {\r\n                              console.log(\"row.user:\", row.user);\r\n                              console.log(\"data._id:\", data._id);\r\n                              console.log(\"Condition:\", row.user === data._id);\r\n\r\n                              return row.user._id === data._id ? (\r\n                                <TableRow\r\n                                  key={row?.name}\r\n                                  sx={{ \"&:last-child td, &:last-child th\": { border: 0 } }}\r\n                                >\r\n                                  <TableCell component=\"th\" scope=\"row\">\r\n                                    {index+1}\r\n                                  </TableCell>\r\n                                  <TableCell align=\"center\">{moment(row?.start).format(\"DD-MM-yyyy\")}</TableCell>\r\n                                  <TableCell align=\"center\">{row?.description}</TableCell>\r\n                                  <TableCell align=\"center\">{row?.type}</TableCell>\r\n                                  <TableCell align=\"center\">{getStatusText(row.status,row.updatedByAdmin)}</TableCell>\r\n                                </TableRow>\r\n                              ) : null;\r\n                            })}\r\n                  </TableBody>\r\n                </Table>\r\n            </TableContainer>\r\n            </div>\r\n        </DialogContent>\r\n      <DialogActions>\r\n        {/* <Button onClick={handleClose}>Cancel</Button> */}\r\n        <Button type=\"submit\">Submit</Button>\r\n      </DialogActions>\r\n    </Dialog>\r\n  }\r\n\r\n  function checkUserAttendDays(data) {\r\n    let count = 0;\r\n    if(attendaces) {\r\n      //  console.log(\"2 Check user attendances \",data)\r\n\r\n      attendaces.forEach(element => {\r\n        // console.log(\"2 Check user attendances \",data._id,element.user)\r\n          if(element.user === data._id) {\r\n              count++;\r\n          }\r\n      });\r\n    }\r\n    // console.log(\"Count\",count)\r\n    return count;\r\n  }\r\n\r\n  // Manual refresh function\r\n  const handleRefresh = () => {\r\n    console.log(\"🔄 LeaveReport: Manual refresh triggered\");\r\n    dispatch(UserActions.getUsers()); // Refresh users to get updated leave counts\r\n    dispatch(LeaveActions.getAllLeaves()); // Refresh leaves data\r\n\r\n    // Also refresh attendance data for current month\r\n    const now = new Date(currentYear, currentMonthSelectRange + 1, 0);\r\n    dispatch(AttendanceActions.getAttendancesByMonth({\r\n      startDate: new Date(now.getFullYear(), now.getMonth(), 1),\r\n      endDate: new Date(now.getFullYear(), now.getMonth() + 1, 0)\r\n    }));\r\n  };\r\n  \r\n\r\n  return (\r\n    <> \r\n    {detailsController ? (\r\n      detailPopUp(userSelect)) : null}\r\n    <Card>\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>\r\n        <Typography variant=\"h5\" sx={{ fontWeight: 600 }}>\r\n          Report\r\n        </Typography>\r\n        <Button\r\n          variant=\"outlined\"\r\n          startIcon={<Refresh />}\r\n          onClick={handleRefresh}\r\n          sx={{ ml: 2 }}\r\n        >\r\n          Refresh Data\r\n        </Button>\r\n      </Box>\r\n      <Select\r\n        value={currentMonthSelectRange}\r\n        onChange={handleMonthChange}\r\n      >\r\n    {Array.from({ length: 12 }, (_, index) => (\r\n          <MenuItem key={index} value={index}>\r\n            {new Date(currentYear, index).toLocaleString('default', { month: 'long' })}\r\n          </MenuItem>\r\n        ))}\r\n        </Select>\r\n          <Select\r\n                value={currentYear}\r\n                onChange={handleYearChange}\r\n              >\r\n                {Array.from({ length: 101 }, (_, index) => {\r\n                  const year = new Date().getFullYear() - 50 + index;\r\n                  return (\r\n                    <MenuItem key={year} value={year}>\r\n                      {year}\r\n                    </MenuItem>\r\n                  );\r\n                })}\r\n              </Select>\r\n      <FilterBox>\r\n        <TableContainer component={Paper}>\r\n          <Table sx={{ minWidth: 650 }} aria-label=\"simple table\">\r\n            <TableHead>\r\n              <TableRow>\r\n                <TableCell>Name</TableCell>\r\n                <TableCell align=\"center\">Total Working Days</TableCell>\r\n                <TableCell align=\"center\">Attended Days</TableCell>\r\n                <TableCell align=\"center\">Total Leaves Taken</TableCell>\r\n                <TableCell align=\"center\">Paid Leaves</TableCell>\r\n                <TableCell align=\"center\">Unpaid Leaves</TableCell>\r\n                <TableCell align=\"center\">Other Leaves</TableCell>\r\n                <TableCell align=\"center\">Leaves Details</TableCell>\r\n              </TableRow>\r\n            </TableHead>\r\n            <TableBody>\r\n              {users.map((row) => {\r\n                // Calculate leave statistics for this user\r\n                const attendedDays = checkUserAttendDays(row);\r\n                const totalLeaves = row?.totalLeaves || 0;\r\n                const leaveTaken = row?.leaveTaken || 0;\r\n                const availableLeaves = row?.availableLeaves || 0;\r\n                const paidLeaves = row?.paidLeaves || 0;\r\n                const unpaidLeaves = row?.unpaidLeaves || 0;\r\n                const otherLeaves = row?.otherLeaves || 0;\r\n\r\n                // Debug logging for leave data\r\n                console.log(`📊 LeaveReport: User ${row?.name} leave data:`, {\r\n                  totalLeaves,\r\n                  leaveTaken,\r\n                  availableLeaves,\r\n                  paidLeaves,\r\n                  unpaidLeaves,\r\n                  otherLeaves,\r\n                  attendedDays,\r\n                  workingDays\r\n                });\r\n\r\n                return (\r\n                  <TableRow\r\n                    key={row?.name}\r\n                    sx={{ \"&:last-child td, &:last-child th\": { border: 0 } }}\r\n                  >\r\n                    <TableCell component=\"th\" scope=\"row\">\r\n                      {row?.name}\r\n                    </TableCell>\r\n                    <TableCell align=\"center\">{workingDays}</TableCell>\r\n                    <TableCell align=\"center\">{attendedDays}</TableCell>\r\n                    <TableCell align=\"center\">{leaveTaken}</TableCell>\r\n                    <TableCell align=\"center\">{paidLeaves}</TableCell>\r\n                    <TableCell align=\"center\">{unpaidLeaves}</TableCell>\r\n                    <TableCell align=\"center\">{otherLeaves}</TableCell>\r\n                    <TableCell align=\"center\">\r\n                      <Button\r\n                        variant=\"contained\"\r\n                        style={{ padding: 0 }}\r\n                        onClick={() => {\r\n                          setDetailsController(true);\r\n                          setUserSelected(row);\r\n                        }}\r\n                      >\r\n                        <NewReleases style={{ width: 24, height: 24 }} />\r\n                      </Button>\r\n                    </TableCell>\r\n                  </TableRow>\r\n                );\r\n              })}\r\n            </TableBody>\r\n          </Table>\r\n        </TableContainer>\r\n      </FilterBox>\r\n      <Pagination\r\n        sx={{ mt: 1 }}\r\n        page={filter.page}\r\n        count={pagination.pages}\r\n        onChange={handleChangePagination}\r\n      />\r\n    </Card>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default LeaveReport;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,cAAc,EACdC,KAAK,EACLC,UAAU,EACVC,MAAM,EACNC,aAAa,EACbC,WAAW,EACXC,aAAa,EACbC,QAAQ,EACRC,MAAM,QACD,eAAe;AAEtB,SACEC,WAAW,EACXC,OAAO,QACF,qBAAqB;AAC5B,OAAOC,MAAM,MAAM,iBAAiB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,kBAAkB,EAAEC,aAAa,EAAEC,YAAY,QAAQ,WAAW;AAC3E,SAASC,iBAAiB,EAAEC,WAAW,EAAEC,YAAY,QAAQ,gBAAgB;AAC7E,SAASC,MAAM,QAAQ,iBAAiB;AACxC,OAAOC,MAAM,MAAM,QAAQ;AAC3B,SAASC,eAAe,QAAQ,2BAA2B;;AAG3D;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,SAASC,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACrB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC;IACnCyC,IAAI,EAAEpB,WAAW,CAACqB,MAAM,CAACC,KAAK;IAC9BC,IAAI,EAAE;EACR,CAAC,CAAC;EAEA,MAAM,CAACC,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG9C,QAAQ,CAAC,IAAI+C,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;EAE7F,MAAMC,sBAAsB,GAAGA,CAACC,CAAC,EAAEC,GAAG,KAAK;IACzCX,SAAS,CAAC;MACR,GAAGD,MAAM;MACTK,IAAI,EAAEO;IACR,CAAC,CAAC;EACJ,CAAC;EAED,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,IAAI+C,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,CAAC;EACxE,MAAMC,UAAU,GAAGhC,WAAW,CAACG,YAAY,CAAC8B,aAAa,CAAC,CAAC,CAAC;EAC5D,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;IAAEL,cAAc,CAACK,KAAK,CAACC,MAAM,CAAChB,KAAK,CAAC;EAAE,CAAC;EAC3E,IAAIiB,KAAK,GAAGrC,WAAW,CAACG,YAAY,CAACmC,QAAQ,CAAC,CAAC,CAAC;EAChD;EACA,MAAMC,QAAQ,GAAGxC,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACyC,iBAAiB,EAACC,oBAAoB,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;EAChE,MAAMiE,MAAM,GAAG1C,WAAW,CAACE,aAAa,CAACyC,SAAS,CAAC,CAAC,CAAC;EACrD,MAAM,CAACC,UAAU,EAACC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,EAAE,CAAC;EACjD,MAAM,CAACqE,YAAY,EAACC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,IAAI+C,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAC,CAAC,CAAC;EACxE,MAAMuB,iBAAiB,GAAIb,KAAK,IAAK;IAAEZ,0BAA0B,CAACY,KAAK,CAACC,MAAM,CAAChB,KAAK,CAAC;IACjF2B,eAAe,CAACZ,KAAK,CAACC,MAAM,CAAChB,KAAK,CAAC;EACtC,CAAC;EACJ,MAAM6B,UAAU,GAAGjD,WAAW,CAACC,kBAAkB,CAACiD,cAAc,CAAC,CAAC,CAAC;EACnE,IAAI,CAACC,WAAW,EAACC,cAAc,CAAC,GAAG3E,QAAQ,CAAC,CAAC,CAAC;EAC9C,MAAM,CAAC4E,WAAW,EAACC,cAAc,CAAC,GAAG7E,QAAQ,CAAC,CAAC,CAAC;EAChD,MAAM8E,OAAO,GAAGvD,WAAW,CAACS,eAAe,CAAC+C,UAAU,CAAC,CAAC,CAAC;EACzDhF,SAAS,CAAC,MAAM;IACZiF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IACjDnB,QAAQ,CAAClC,WAAW,CAACiC,QAAQ,CAAC,CAAC,CAAC;IAChCC,QAAQ,CAACjC,YAAY,CAACqD,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,MAAMC,GAAG,GAAG,IAAIpC,IAAI,CAACK,WAAW,EAACP,uBAAuB,GAAC,CAAC,EAAC,CAAC,CAAC;IAC7DmC,OAAO,CAACC,GAAG,CAAC,MAAM,EAACE,GAAG,CAAC;IACvBrB,QAAQ,CAACnC,iBAAiB,CAACyD,qBAAqB,CAAC;MAC/CC,SAAS,EAAE,IAAItC,IAAI,CAACoC,GAAG,CAAC7B,WAAW,CAAC,CAAC,EAAE6B,GAAG,CAACnC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACzDsC,OAAO,EAAG,IAAIvC,IAAI,CAACoC,GAAG,CAAC7B,WAAW,CAAC,CAAC,EAAE6B,GAAG,CAACnC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC7D,CAAC,CAAC,CAAC;IACHuC,mBAAmB,CAAC,CAAC;EAEzB,CAAC,EAAC,EAAE,CAAC;EAELxF,SAAS,CAAC,MAAM;IACd,MAAMoF,GAAG,GAAG,IAAIpC,IAAI,CAACK,WAAW,EAACP,uBAAuB,GAAC,CAAC,EAAC,CAAC,CAAC;IAC7DmC,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;IAC7DnB,QAAQ,CAAClC,WAAW,CAACiC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAClCC,QAAQ,CAACjC,YAAY,CAACqD,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACvCpB,QAAQ,CAACnC,iBAAiB,CAACyD,qBAAqB,CAAC;MAC/CC,SAAS,EAAE,IAAItC,IAAI,CAACoC,GAAG,CAAC7B,WAAW,CAAC,CAAC,EAAE6B,GAAG,CAACnC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACzDsC,OAAO,EAAG,IAAIvC,IAAI,CAACoC,GAAG,CAAC7B,WAAW,CAAC,CAAC,EAAE6B,GAAG,CAACnC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC7D,CAAC,CAAC,CAAC;IACHuC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAC,CAAC1C,uBAAuB,EAACO,WAAW,CAAC,CAAC;;EAExC;EACArD,SAAS,CAAC,MAAM;IACd,MAAMyF,eAAe,GAAGC,WAAW,CAAC,MAAM;MACxCT,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrEnB,QAAQ,CAAClC,WAAW,CAACiC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAClCC,QAAQ,CAACjC,YAAY,CAACqD,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMQ,aAAa,CAACF,eAAe,CAAC;EAC7C,CAAC,EAAE,CAAC1B,QAAQ,CAAC,CAAC;EAEd,MAAM6B,SAAS,GAAGvE,MAAM,CAACnB,GAAG,CAAC,CAAC,OAAO;IACnC2F,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,EAAE;IACbC,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE;EAClB,CAAC,CAAC,CAAC;EAEH,SAAST,mBAAmBA,CAAA,EAAG;IAC7B,MAAMb,WAAW,GAAG,IAAI3B,IAAI,CAACK,WAAW,EAAEiB,YAAY,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC4B,OAAO,CAAC,CAAC;IACxEtB,cAAc,CAACD,WAAW,CAAC;IAE3B,IAAIwB,gBAAgB,GAAG,CAAC;IAExB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIzB,WAAW,EAAEyB,CAAC,EAAE,EAAE;MACnC,IAAIC,QAAQ,GAAG,IAAIrD,IAAI,CAACK,WAAW,EAAEiB,YAAY,EAAE8B,CAAC,CAAC;MACrD,IAAIE,SAAS,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,CAAC;;MAE9F;MACA,IAAIA,SAAS,CAACD,QAAQ,CAACE,MAAM,CAAC,CAAC,CAAC,KAAKxB,OAAO,CAACyB,GAAG,EAAE;QAC9C;QACA,IAAIzB,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAE0B,eAAe,IAAI,CAAA1B,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0B,eAAe,CAACC,MAAM,IAAG,CAAC,EAAE;UACjE,MAAMC,SAAS,GAAG5B,OAAO,CAAC0B,eAAe,CAACG,IAAI,CAACC,OAAO,IAAI;YACtD,OAAO,IAAI7D,IAAI,CAAC6D,OAAO,CAACC,SAAS,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,KAAKV,QAAQ,CAACU,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAC7F,CAAC,CAAC;;UAEF;UACA,IAAI,CAACJ,SAAS,EAAE;YACZR,gBAAgB,IAAI,CAAC;UACzB;QACJ,CAAC,MAAM;UACH;UACAA,gBAAgB,IAAI,CAAC;QACzB;MACJ;IACJ;IAEArB,cAAc,CAACqB,gBAAgB,CAAC;IAChClB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiB,gBAAgB,CAAC;EACxD;EAEE,SAASa,WAAWA,CAAA,EAAG;IACrB/C,oBAAoB,CAAC,KAAK,CAAC;EAC7B;EAEA,MAAMgD,aAAa,GAAGA,CAACC,MAAM,EAAEC,cAAc,KAAK;IAChD,IAAID,MAAM,KAAK,CAAC,IAAIC,cAAc,EAAE;MAClC,OAAO,UAAU;IACnB,CAAC,MAAM,IAAID,MAAM,KAAK,CAAC,IAAIC,cAAc,EAAE;MACzC,OAAO,UAAU;IACnB,CAAC,MAAM,IAAID,MAAM,KAAK,CAAC,IAAI,CAACC,cAAc,EAAE;MAC1C,OAAO,SAAS;IAClB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF,CAAC;EAED,SAASC,WAAWA,CAACC,IAAI,EAAE;IACzBpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAACmC,IAAI,CAAC;IACnC,oBAAOlF,OAAA,CAACtB,MAAM;MACZyG,IAAI,EAAEtD,iBAAkB;MACxBuD,QAAQ,EAAC,IAAI;MACbC,OAAO,EAAER,WAAY;MACrBS,UAAU,EAAE;QACVC,SAAS,EAAE,MAAM;QACjBC,EAAE,EAAE;UAAE9B,KAAK,EAAE,QAAQ;UAAE+B,MAAM,EAAE;QAAQ,CAAC;QACxCC,QAAQ,EAAGlE,KAAK,IAAK;UACjBM,oBAAoB,CAAC,KAAK,CAAC;QAC/B;MACF,CAAE;MAAA6D,QAAA,gBAEF3F,OAAA,CAACpB,WAAW;QAAA+G,QAAA,EAAC;MAAa;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eACxC/F,OAAA,CAACrB,aAAa;QAAAgH,QAAA,gBACR3F,OAAA;UAAKgG,KAAK,EAAE;YAAEnC,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAe,CAAE;UAAA6B,QAAA,gBAC5D3F,OAAA;YAAKgG,KAAK,EAAE;cAAEtC,KAAK,EAAE,OAAO;cAAE+B,MAAM,EAAE,OAAO;cAAEQ,MAAM,EAAE,gBAAgB;cAAEpC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,cAAc;cAAEoC,UAAU,EAAE,QAAQ;cAAEC,aAAa,EAAE;YAAS,CAAE;YAAAR,QAAA,gBACtK3F,OAAA;cAAKgG,KAAK,EAAE;gBAAEI,WAAW,EAAE;cAAS,CAAE;cAAAT,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACjD/F,OAAA;cAAKgG,KAAK,EAAE;gBAAEI,WAAW,EAAE,QAAQ;gBAAEC,UAAU,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAET,IAAI,CAACoB;YAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E,CAAC,eACN/F,OAAA;YAAKgG,KAAK,EAAE;cAAEtC,KAAK,EAAE,OAAO;cAAE+B,MAAM,EAAE,OAAO;cAAEQ,MAAM,EAAE,gBAAgB;cAAEpC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,cAAc;cAAEoC,UAAU,EAAE,QAAQ;cAAEC,aAAa,EAAE;YAAS,CAAE;YAAAR,QAAA,gBACtK3F,OAAA;cAAKgG,KAAK,EAAE;gBAAEI,WAAW,EAAE;cAAS,CAAE;cAAAT,QAAA,EAAC;YAAW;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxD/F,OAAA;cAAKgG,KAAK,EAAE;gBAAEnC,OAAO,EAAE,MAAM;gBAAGC,cAAc,EAAC,cAAc;gBAACoC,UAAU,EAAE,QAAQ;gBAAEK,GAAG,EAAC;cAAO,CAAE;cAAAZ,QAAA,gBAC7F3F,OAAA;gBAAKgG,KAAK,EAAE;kBAACnC,OAAO,EAAC,MAAM;kBAACqC,UAAU,EAAC,QAAQ;kBAACC,aAAa,EAAC;gBAAQ,CAAE;gBAAAR,QAAA,gBACxE3F,OAAA;kBAAKgG,KAAK,EAAE;oBAAEK,UAAU,EAAE,MAAM;oBAAEH,UAAU,EAAC;kBAAS,CAAE;kBAAAP,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAChE/F,OAAA;kBAAKgG,KAAK,EAAE;oBAAEK,UAAU,EAAE,MAAM;oBAACH,UAAU,EAAC;kBAAS,CAAE;kBAAAP,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7D,CAAC,eACN/F,OAAA;gBAAKgG,KAAK,EAAE;kBAACnC,OAAO,EAAC,MAAM;kBAACqC,UAAU,EAAC,QAAQ;kBAACC,aAAa,EAAC;gBAAQ,CAAE;gBAAAR,QAAA,gBACxE3F,OAAA;kBAAKgG,KAAK,EAAE;oBAAEK,UAAU,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAC;gBAAC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3C/F,OAAA;kBAAKgG,KAAK,EAAE;oBAAEK,UAAU,EAAE;kBAAO,CAAE;kBAAAV,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC,eACN/F,OAAA;YAAKgG,KAAK,EAAE;cAAEtC,KAAK,EAAE,OAAO;cAAE+B,MAAM,EAAE,OAAO;cAAEQ,MAAM,EAAE,gBAAgB;cAAEpC,OAAO,EAAE,MAAM;cAAEC,cAAc,EAAE,cAAc;cAAEoC,UAAU,EAAE,QAAQ;cAAEC,aAAa,EAAE;YAAS,CAAE;YAAAR,QAAA,gBACtK3F,OAAA;cAAKgG,KAAK,EAAE;gBAAEI,WAAW,EAAE;cAAS,CAAE;cAAAT,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3D/F,OAAA;cAAKgG,KAAK,EAAE;gBAAEI,WAAW,EAAE,QAAQ;gBAAEC,UAAU,EAAE;cAAO,CAAE;cAAAV,QAAA,EAAC;YAAC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEL,CAAC,eACN/F,OAAA;UAAKgG,KAAK,EAAE;YAACnC,OAAO,EAAC,MAAM;YAACC,cAAc,EAAC,cAAc;YAAE0C,UAAU,EAAC;UAAM,CAAE;UAAAb,QAAA,gBAC5E3F,OAAA;YAAKgG,KAAK,EAAE;cAACC,MAAM,EAAC;YAAiB,CAAE;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACvD/F,OAAA;YAAKgG,KAAK,EAAE;cAACC,MAAM,EAAC;YAAiB,CAAE;YAAAN,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxD/F,OAAA;YAAKgG,KAAK,EAAE;cAACC,MAAM,EAAC;YAAiB,CAAE;YAAAN,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC,eACN/F,OAAA;UAAKgG,KAAK,EAAE;YAACQ,UAAU,EAAC;UAAM,CAAE;UAAAb,QAAA,eAChC3F,OAAA,CAACzB,cAAc;YAACgH,SAAS,EAAE/G,KAAM;YAAAmH,QAAA,eAC7B3F,OAAA,CAAC/B,KAAK;cAACuH,EAAE,EAAE;gBAAEiB,QAAQ,EAAE;cAAI,CAAE;cAAC,cAAW,cAAc;cAAAd,QAAA,gBACrD3F,OAAA,CAAC5B,SAAS;gBAAAuH,QAAA,eACR3F,OAAA,CAAC3B,QAAQ;kBAAAsH,QAAA,gBACP3F,OAAA,CAAC7B,SAAS;oBAAAwH,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACxB/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAC;kBAAI;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eAC1C/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAC;kBAAW;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACjD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC,eACpD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAW,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACZ/F,OAAA,CAAC9B,SAAS;gBAAAyH,QAAA,EACC5D,MAAM,CAAC4E,GAAG,CAAC,CAACC,GAAG,EAACC,KAAK,KAAK;kBACzB/D,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE6D,GAAG,CAACE,IAAI,CAAC;kBAClChE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEmC,IAAI,CAAC6B,GAAG,CAAC;kBAClCjE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE6D,GAAG,CAACE,IAAI,KAAK5B,IAAI,CAAC6B,GAAG,CAAC;kBAEhD,OAAOH,GAAG,CAACE,IAAI,CAACC,GAAG,KAAK7B,IAAI,CAAC6B,GAAG,gBAC9B/G,OAAA,CAAC3B,QAAQ;oBAEPmH,EAAE,EAAE;sBAAE,kCAAkC,EAAE;wBAAES,MAAM,EAAE;sBAAE;oBAAE,CAAE;oBAAAN,QAAA,gBAE1D3F,OAAA,CAAC7B,SAAS;sBAACoH,SAAS,EAAC,IAAI;sBAACyB,KAAK,EAAC,KAAK;sBAAArB,QAAA,EAClCkB,KAAK,GAAC;oBAAC;sBAAAjB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,eACZ/F,OAAA,CAAC7B,SAAS;sBAACuI,KAAK,EAAC,QAAQ;sBAAAf,QAAA,EAAE9F,MAAM,CAAC+G,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEK,KAAK,CAAC,CAACC,MAAM,CAAC,YAAY;oBAAC;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eAC/E/F,OAAA,CAAC7B,SAAS;sBAACuI,KAAK,EAAC,QAAQ;sBAAAf,QAAA,EAAEiB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEO;oBAAW;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACxD/F,OAAA,CAAC7B,SAAS;sBAACuI,KAAK,EAAC,QAAQ;sBAAAf,QAAA,EAAEiB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEQ;oBAAI;sBAAAxB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC,eACjD/F,OAAA,CAAC7B,SAAS;sBAACuI,KAAK,EAAC,QAAQ;sBAAAf,QAAA,EAAEb,aAAa,CAAC8B,GAAG,CAAC7B,MAAM,EAAC6B,GAAG,CAAC5B,cAAc;oBAAC;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAY,CAAC;kBAAA,GAT/Ea,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEN,IAAI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAUN,CAAC,GACT,IAAI;gBACV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eAClB/F,OAAA,CAACnB,aAAa;QAAA8G,QAAA,eAEZ3F,OAAA,CAACJ,MAAM;UAACwH,IAAI,EAAC,QAAQ;UAAAzB,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EACX;EAEA,SAASsB,mBAAmBA,CAACnC,IAAI,EAAE;IACjC,IAAIoC,KAAK,GAAG,CAAC;IACb,IAAGhF,UAAU,EAAE;MACb;;MAEAA,UAAU,CAACiF,OAAO,CAAC7C,OAAO,IAAI;QAC5B;QACE,IAAGA,OAAO,CAACoC,IAAI,KAAK5B,IAAI,CAAC6B,GAAG,EAAE;UAC1BO,KAAK,EAAE;QACX;MACJ,CAAC,CAAC;IACJ;IACA;IACA,OAAOA,KAAK;EACd;;EAEA;EACA,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B1E,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvDnB,QAAQ,CAAClC,WAAW,CAACiC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IAClCC,QAAQ,CAACjC,YAAY,CAACqD,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEvC;IACA,MAAMC,GAAG,GAAG,IAAIpC,IAAI,CAACK,WAAW,EAAEP,uBAAuB,GAAG,CAAC,EAAE,CAAC,CAAC;IACjEiB,QAAQ,CAACnC,iBAAiB,CAACyD,qBAAqB,CAAC;MAC/CC,SAAS,EAAE,IAAItC,IAAI,CAACoC,GAAG,CAAC7B,WAAW,CAAC,CAAC,EAAE6B,GAAG,CAACnC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;MACzDsC,OAAO,EAAE,IAAIvC,IAAI,CAACoC,GAAG,CAAC7B,WAAW,CAAC,CAAC,EAAE6B,GAAG,CAACnC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC5D,CAAC,CAAC,CAAC;EACL,CAAC;EAGD,oBACEd,OAAA,CAAAE,SAAA;IAAAyF,QAAA,GACC9D,iBAAiB,GAChBoD,WAAW,CAAChD,UAAU,CAAC,GAAI,IAAI,eACjCjC,OAAA,CAAChC,IAAI;MAAA2H,QAAA,gBACH3F,OAAA,CAACjC,GAAG;QAACyH,EAAE,EAAE;UAAE3B,OAAO,EAAE,MAAM;UAAEC,cAAc,EAAE,eAAe;UAAEoC,UAAU,EAAE,QAAQ;UAAEuB,EAAE,EAAE;QAAE,CAAE;QAAA9B,QAAA,gBACzF3F,OAAA,CAAC1B,UAAU;UAACoJ,OAAO,EAAC,IAAI;UAAClC,EAAE,EAAE;YAAEa,UAAU,EAAE;UAAI,CAAE;UAAAV,QAAA,EAAC;QAElD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb/F,OAAA,CAACJ,MAAM;UACL8H,OAAO,EAAC,UAAU;UAClBC,SAAS,eAAE3H,OAAA,CAACf,OAAO;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB6B,OAAO,EAAEJ,aAAc;UACvBhC,EAAE,EAAE;YAAEqC,EAAE,EAAE;UAAE,CAAE;UAAAlC,QAAA,EACf;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACN/F,OAAA,CAACjB,MAAM;QACL0B,KAAK,EAAEE,uBAAwB;QAC/BmH,QAAQ,EAAEzF,iBAAkB;QAAAsD,QAAA,EAE/BoC,KAAK,CAACC,IAAI,CAAC;UAAEzD,MAAM,EAAE;QAAG,CAAC,EAAE,CAAC0D,CAAC,EAAEpB,KAAK,kBAC/B7G,OAAA,CAAClB,QAAQ;UAAa2B,KAAK,EAAEoG,KAAM;UAAAlB,QAAA,EAChC,IAAI9E,IAAI,CAACK,WAAW,EAAE2F,KAAK,CAAC,CAACqB,cAAc,CAAC,SAAS,EAAE;YAAEC,KAAK,EAAE;UAAO,CAAC;QAAC,GAD7DtB,KAAK;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEV,CACX;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eACP/F,OAAA,CAACjB,MAAM;QACD0B,KAAK,EAAES,WAAY;QACnB4G,QAAQ,EAAEvG,gBAAiB;QAAAoE,QAAA,EAE1BoC,KAAK,CAACC,IAAI,CAAC;UAAEzD,MAAM,EAAE;QAAI,CAAC,EAAE,CAAC0D,CAAC,EAAEpB,KAAK,KAAK;UACzC,MAAMuB,IAAI,GAAG,IAAIvH,IAAI,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,GAAG,EAAE,GAAGyF,KAAK;UAClD,oBACE7G,OAAA,CAAClB,QAAQ;YAAY2B,KAAK,EAAE2H,IAAK;YAAAzC,QAAA,EAC9ByC;UAAI,GADQA,IAAI;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAET,CAAC;QAEf,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eACjB/F,OAAA,CAACyD,SAAS;QAAAkC,QAAA,eACR3F,OAAA,CAACzB,cAAc;UAACgH,SAAS,EAAE/G,KAAM;UAAAmH,QAAA,eAC/B3F,OAAA,CAAC/B,KAAK;YAACuH,EAAE,EAAE;cAAEiB,QAAQ,EAAE;YAAI,CAAE;YAAC,cAAW,cAAc;YAAAd,QAAA,gBACrD3F,OAAA,CAAC5B,SAAS;cAAAuH,QAAA,eACR3F,OAAA,CAAC3B,QAAQ;gBAAAsH,QAAA,gBACP3F,OAAA,CAAC7B,SAAS;kBAAAwH,QAAA,EAAC;gBAAI;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAC3B/F,OAAA,CAAC7B,SAAS;kBAACuI,KAAK,EAAC,QAAQ;kBAAAf,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACxD/F,OAAA,CAAC7B,SAAS;kBAACuI,KAAK,EAAC,QAAQ;kBAAAf,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnD/F,OAAA,CAAC7B,SAAS;kBAACuI,KAAK,EAAC,QAAQ;kBAAAf,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACxD/F,OAAA,CAAC7B,SAAS;kBAACuI,KAAK,EAAC,QAAQ;kBAAAf,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACjD/F,OAAA,CAAC7B,SAAS;kBAACuI,KAAK,EAAC,QAAQ;kBAAAf,QAAA,EAAC;gBAAa;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eACnD/F,OAAA,CAAC7B,SAAS;kBAACuI,KAAK,EAAC,QAAQ;kBAAAf,QAAA,EAAC;gBAAY;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC,eAClD/F,OAAA,CAAC7B,SAAS;kBAACuI,KAAK,EAAC,QAAQ;kBAAAf,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAW,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACZ/F,OAAA,CAAC9B,SAAS;cAAAyH,QAAA,EACPjE,KAAK,CAACiF,GAAG,CAAEC,GAAG,IAAK;gBAClB;gBACA,MAAMyB,YAAY,GAAGhB,mBAAmB,CAACT,GAAG,CAAC;gBAC7C,MAAM0B,WAAW,GAAG,CAAA1B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE0B,WAAW,KAAI,CAAC;gBACzC,MAAMC,UAAU,GAAG,CAAA3B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE2B,UAAU,KAAI,CAAC;gBACvC,MAAMC,eAAe,GAAG,CAAA5B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE4B,eAAe,KAAI,CAAC;gBACjD,MAAMC,UAAU,GAAG,CAAA7B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE6B,UAAU,KAAI,CAAC;gBACvC,MAAMC,YAAY,GAAG,CAAA9B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE8B,YAAY,KAAI,CAAC;gBAC3C,MAAMC,WAAW,GAAG,CAAA/B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE+B,WAAW,KAAI,CAAC;;gBAEzC;gBACA7F,OAAO,CAACC,GAAG,CAAC,wBAAwB6D,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEN,IAAI,cAAc,EAAE;kBAC3DgC,WAAW;kBACXC,UAAU;kBACVC,eAAe;kBACfC,UAAU;kBACVC,YAAY;kBACZC,WAAW;kBACXN,YAAY;kBACZ3F;gBACF,CAAC,CAAC;gBAEF,oBACE1C,OAAA,CAAC3B,QAAQ;kBAEPmH,EAAE,EAAE;oBAAE,kCAAkC,EAAE;sBAAES,MAAM,EAAE;oBAAE;kBAAE,CAAE;kBAAAN,QAAA,gBAE1D3F,OAAA,CAAC7B,SAAS;oBAACoH,SAAS,EAAC,IAAI;oBAACyB,KAAK,EAAC,KAAK;oBAAArB,QAAA,EAClCiB,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEN;kBAAI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eACZ/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAEjD;kBAAW;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAE0C;kBAAY;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAE4C;kBAAU;oBAAA3C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAE8C;kBAAU;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eAClD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAE+C;kBAAY;oBAAA9C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACpD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,EAAEgD;kBAAW;oBAAA/C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAY,CAAC,eACnD/F,OAAA,CAAC7B,SAAS;oBAACuI,KAAK,EAAC,QAAQ;oBAAAf,QAAA,eACvB3F,OAAA,CAACJ,MAAM;sBACL8H,OAAO,EAAC,WAAW;sBACnB1B,KAAK,EAAE;wBAAE4C,OAAO,EAAE;sBAAE,CAAE;sBACtBhB,OAAO,EAAEA,CAAA,KAAM;wBACb9F,oBAAoB,CAAC,IAAI,CAAC;wBAC1BI,eAAe,CAAC0E,GAAG,CAAC;sBACtB,CAAE;sBAAAjB,QAAA,eAEF3F,OAAA,CAAChB,WAAW;wBAACgH,KAAK,EAAE;0BAAEtC,KAAK,EAAE,EAAE;0BAAE+B,MAAM,EAAE;wBAAG;sBAAE;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA,CAAC;gBAAA,GAvBPa,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEN,IAAI;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwBN,CAAC;cAEf,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eACZ/F,OAAA,CAACvB,UAAU;QACT+G,EAAE,EAAE;UAAEqD,EAAE,EAAE;QAAE,CAAE;QACdnI,IAAI,EAAEL,MAAM,CAACK,IAAK;QAClB4G,KAAK,EAAEjG,UAAU,CAACyH,KAAM;QACxBhB,QAAQ,EAAE/G;MAAuB;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACL,CAAC;AAEP;AAAC3F,EAAA,CAhXQD,WAAW;EAAA,QAgBGd,WAAW,EAElBA,WAAW,EAEND,WAAW,EAEbC,WAAW,EAMTA,WAAW,EAGdA,WAAW;AAAA;AAAA0J,EAAA,GA/BpB5I,WAAW;AAkXpB,eAAeA,WAAW;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}