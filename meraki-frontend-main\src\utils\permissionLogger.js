/**
 * Utility functions for logging and debugging permissions
 */

/**
 * Logs all permissions for a user
 * @param {Object} user - The user object with permissions
 */
export const logUserPermissions = (user) => {
  if (!user) {
    return;
  }

  if (user.permissions && Array.isArray(user.permissions)) {
    // Group permissions by feature
    const permissionsByFeature = {};
    user.permissions.forEach(p => {
      permissionsByFeature[p.feat] = p.acts;
    });
  }
};

/**
 * Logs all permission checks for a specific route
 * @param {string} path - The route path
 * @param {Object} permission - The permission object with feat and act properties
 * @param {boolean} hasPermission - Whether the user has the permission
 * @param {string} reason - The reason for the permission decision
 */
export const logRoutePermission = (path, permission, hasPermission, reason = '') => {
  // Function preserved for compatibility but logging removed
};

export default {
  logUserPermissions,
  logRoutePermission
};
