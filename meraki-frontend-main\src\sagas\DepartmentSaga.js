import {all, call, put, takeLatest} from 'redux-saga/effects'
import {DepartmentService} from "../services";
import {DepartmentActions, GeneralActions} from "../slices/actions";

function *getDepartments({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));

        const result = yield call(DepartmentService.GetDepartments, payload);

        yield put(DepartmentActions.getDepartmentsSuccess(result.data));
        yield put(GeneralActions.stopLoading(type))
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));

        // Handle 401 Unauthorized errors gracefully
        if (err.status === 401 || err.originalError?.response?.status === 401) {
            // Set empty departments array instead of showing an error
            yield put(DepartmentActions.getDepartmentsSuccess({ data: [], pagination: { pages: 0 } }));

            // Add a user-friendly error message
            yield put(GeneralActions.addError({
                action: type,
                message: 'You do not have permission to view departments'
            }));
        } else {
            // Handle other errors
            yield put(GeneralActions.addError({
                action: type,
                message: err.response?.data?.error || err.message || 'Failed to load departments'
            }));
        }
    }
}

function *getDepartmentById({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));

        const result = yield call(DepartmentService.GetDepartmentById, payload);

        yield put(DepartmentActions.getDepartmentByIdSuccess(result.data));
        yield put(GeneralActions.stopLoading(type))
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

function *createDepartment({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));

        const result = yield call(DepartmentService.CreateDepartment, payload);

        yield put(GeneralActions.addSuccess({
            action: type,
            message: result.data.message
        }));
        yield put(GeneralActions.stopLoading(type))
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

function *updateDepartment({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));

        const result = yield call(DepartmentService.UpdateDepartment, payload.id, payload);

        yield put(GeneralActions.addSuccess({
            action: type,
            message: result.data.message
        }));
        yield put(GeneralActions.stopLoading(type))
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

function *deleteDepartment({type, payload}) {
    try {
        yield put(GeneralActions.removeError(type));
        yield put(GeneralActions.startLoading(type));

        const result = yield call(DepartmentService.DeleteDepartment, payload);

        yield put(GeneralActions.addSuccess({
            action: type,
            message: result.data.message
        }));
        yield put(GeneralActions.stopLoading(type))
    } catch (err) {
        yield put(GeneralActions.stopLoading(type));
        yield put(GeneralActions.addError({
            action: type,
            message: err.response?.data?.error
        }));
    }
}

export function *DepartmentWatcher() {
    yield all([
        yield takeLatest(DepartmentActions.getDepartments.type, getDepartments),
        yield takeLatest(DepartmentActions.getDepartmentById.type, getDepartmentById),
        yield takeLatest(DepartmentActions.createDepartment.type, createDepartment),
        yield takeLatest(DepartmentActions.updateDepartment.type, updateDepartment),
        yield takeLatest(DepartmentActions.deleteDepartment.type, deleteDepartment)
    ]);
}