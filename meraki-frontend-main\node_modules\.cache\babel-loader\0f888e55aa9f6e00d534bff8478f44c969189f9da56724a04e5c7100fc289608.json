{"ast": null, "code": "import { all, call, put, takeLatest } from 'redux-saga/effects';\nimport { DepartmentService } from \"../services\";\nimport { DepartmentActions, GeneralActions } from \"../slices/actions\";\nfunction* getDepartments({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(DepartmentService.GetDepartments, payload);\n    yield put(DepartmentActions.getDepartmentsSuccess(result.data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$originalError, _err$originalError$re;\n    yield put(GeneralActions.stopLoading(type));\n\n    // Handle 401 Unauthorized errors gracefully\n    if (err.status === 401 || ((_err$originalError = err.originalError) === null || _err$originalError === void 0 ? void 0 : (_err$originalError$re = _err$originalError.response) === null || _err$originalError$re === void 0 ? void 0 : _err$originalError$re.status) === 401) {\n      // Set empty departments array instead of showing an error\n      yield put(DepartmentActions.getDepartmentsSuccess({\n        data: [],\n        pagination: {\n          pages: 0\n        }\n      }));\n\n      // Add a user-friendly error message\n      yield put(GeneralActions.addError({\n        action: type,\n        message: 'You do not have permission to view departments'\n      }));\n    } else {\n      var _err$response, _err$response$data;\n      // Handle other errors\n      yield put(GeneralActions.addError({\n        action: type,\n        message: ((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || err.message || 'Failed to load departments'\n      }));\n    }\n  }\n}\nfunction* getDepartmentById({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(DepartmentService.GetDepartmentById, payload);\n    yield put(DepartmentActions.getDepartmentByIdSuccess(result.data));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response2, _err$response2$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response2 = err.response) === null || _err$response2 === void 0 ? void 0 : (_err$response2$data = _err$response2.data) === null || _err$response2$data === void 0 ? void 0 : _err$response2$data.error\n    }));\n  }\n}\nfunction* createDepartment({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(DepartmentService.CreateDepartment, payload);\n    yield put(GeneralActions.addSuccess({\n      action: type,\n      message: result.data.message\n    }));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response3, _err$response3$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response3 = err.response) === null || _err$response3 === void 0 ? void 0 : (_err$response3$data = _err$response3.data) === null || _err$response3$data === void 0 ? void 0 : _err$response3$data.error\n    }));\n  }\n}\nfunction* updateDepartment({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(DepartmentService.UpdateDepartment, payload.id, payload);\n    yield put(GeneralActions.addSuccess({\n      action: type,\n      message: result.data.message\n    }));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response4, _err$response4$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response4 = err.response) === null || _err$response4 === void 0 ? void 0 : (_err$response4$data = _err$response4.data) === null || _err$response4$data === void 0 ? void 0 : _err$response4$data.error\n    }));\n  }\n}\nfunction* deleteDepartment({\n  type,\n  payload\n}) {\n  try {\n    yield put(GeneralActions.removeError(type));\n    yield put(GeneralActions.startLoading(type));\n    const result = yield call(DepartmentService.DeleteDepartment, payload);\n    yield put(GeneralActions.addSuccess({\n      action: type,\n      message: result.data.message\n    }));\n    yield put(GeneralActions.stopLoading(type));\n  } catch (err) {\n    var _err$response5, _err$response5$data;\n    yield put(GeneralActions.stopLoading(type));\n    yield put(GeneralActions.addError({\n      action: type,\n      message: (_err$response5 = err.response) === null || _err$response5 === void 0 ? void 0 : (_err$response5$data = _err$response5.data) === null || _err$response5$data === void 0 ? void 0 : _err$response5$data.error\n    }));\n  }\n}\nexport function* DepartmentWatcher() {\n  yield all([yield takeLatest(DepartmentActions.getDepartments.type, getDepartments), yield takeLatest(DepartmentActions.getDepartmentById.type, getDepartmentById), yield takeLatest(DepartmentActions.createDepartment.type, createDepartment), yield takeLatest(DepartmentActions.updateDepartment.type, updateDepartment), yield takeLatest(DepartmentActions.deleteDepartment.type, deleteDepartment)]);\n}\n_c = DepartmentWatcher;\nvar _c;\n$RefreshReg$(_c, \"DepartmentWatcher\");", "map": {"version": 3, "names": ["all", "call", "put", "take<PERSON><PERSON>t", "DepartmentService", "DepartmentActions", "GeneralActions", "getDepartments", "type", "payload", "removeError", "startLoading", "result", "GetDepartments", "getDepartmentsSuccess", "data", "stopLoading", "err", "_err$originalError", "_err$originalError$re", "status", "originalError", "response", "pagination", "pages", "addError", "action", "message", "_err$response", "_err$response$data", "error", "getDepartmentById", "GetDepartmentById", "getDepartmentByIdSuccess", "_err$response2", "_err$response2$data", "createDepartment", "CreateDepartment", "addSuccess", "_err$response3", "_err$response3$data", "updateDepartment", "UpdateDepartment", "id", "_err$response4", "_err$response4$data", "deleteDepartment", "DeleteDepartment", "_err$response5", "_err$response5$data", "DepartmentWatcher", "_c", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/sagas/DepartmentSaga.js"], "sourcesContent": ["import {all, call, put, takeLatest} from 'redux-saga/effects'\r\nimport {DepartmentService} from \"../services\";\r\nimport {DepartmentActions, GeneralActions} from \"../slices/actions\";\r\n\r\nfunction *getDepartments({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n\r\n        const result = yield call(DepartmentService.GetDepartments, payload);\r\n\r\n        yield put(DepartmentActions.getDepartmentsSuccess(result.data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n\r\n        // Handle 401 Unauthorized errors gracefully\r\n        if (err.status === 401 || err.originalError?.response?.status === 401) {\r\n            // Set empty departments array instead of showing an error\r\n            yield put(DepartmentActions.getDepartmentsSuccess({ data: [], pagination: { pages: 0 } }));\r\n\r\n            // Add a user-friendly error message\r\n            yield put(GeneralActions.addError({\r\n                action: type,\r\n                message: 'You do not have permission to view departments'\r\n            }));\r\n        } else {\r\n            // Handle other errors\r\n            yield put(GeneralActions.addError({\r\n                action: type,\r\n                message: err.response?.data?.error || err.message || 'Failed to load departments'\r\n            }));\r\n        }\r\n    }\r\n}\r\n\r\nfunction *getDepartmentById({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n\r\n        const result = yield call(DepartmentService.GetDepartmentById, payload);\r\n\r\n        yield put(DepartmentActions.getDepartmentByIdSuccess(result.data));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *createDepartment({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n\r\n        const result = yield call(DepartmentService.CreateDepartment, payload);\r\n\r\n        yield put(GeneralActions.addSuccess({\r\n            action: type,\r\n            message: result.data.message\r\n        }));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *updateDepartment({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n\r\n        const result = yield call(DepartmentService.UpdateDepartment, payload.id, payload);\r\n\r\n        yield put(GeneralActions.addSuccess({\r\n            action: type,\r\n            message: result.data.message\r\n        }));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nfunction *deleteDepartment({type, payload}) {\r\n    try {\r\n        yield put(GeneralActions.removeError(type));\r\n        yield put(GeneralActions.startLoading(type));\r\n\r\n        const result = yield call(DepartmentService.DeleteDepartment, payload);\r\n\r\n        yield put(GeneralActions.addSuccess({\r\n            action: type,\r\n            message: result.data.message\r\n        }));\r\n        yield put(GeneralActions.stopLoading(type))\r\n    } catch (err) {\r\n        yield put(GeneralActions.stopLoading(type));\r\n        yield put(GeneralActions.addError({\r\n            action: type,\r\n            message: err.response?.data?.error\r\n        }));\r\n    }\r\n}\r\n\r\nexport function *DepartmentWatcher() {\r\n    yield all([\r\n        yield takeLatest(DepartmentActions.getDepartments.type, getDepartments),\r\n        yield takeLatest(DepartmentActions.getDepartmentById.type, getDepartmentById),\r\n        yield takeLatest(DepartmentActions.createDepartment.type, createDepartment),\r\n        yield takeLatest(DepartmentActions.updateDepartment.type, updateDepartment),\r\n        yield takeLatest(DepartmentActions.deleteDepartment.type, deleteDepartment)\r\n    ]);\r\n}"], "mappings": "AAAA,SAAQA,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,UAAU,QAAO,oBAAoB;AAC7D,SAAQC,iBAAiB,QAAO,aAAa;AAC7C,SAAQC,iBAAiB,EAAEC,cAAc,QAAO,mBAAmB;AAEnE,UAAUC,cAAcA,CAAC;EAACC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACtC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAE5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,iBAAiB,CAACS,cAAc,EAAEJ,OAAO,CAAC;IAEpE,MAAMP,GAAG,CAACG,iBAAiB,CAACS,qBAAqB,CAACF,MAAM,CAACG,IAAI,CAAC,CAAC;IAC/D,MAAMb,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOS,GAAG,EAAE;IAAA,IAAAC,kBAAA,EAAAC,qBAAA;IACV,MAAMjB,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;;IAE3C;IACA,IAAIS,GAAG,CAACG,MAAM,KAAK,GAAG,IAAI,EAAAF,kBAAA,GAAAD,GAAG,CAACI,aAAa,cAAAH,kBAAA,wBAAAC,qBAAA,GAAjBD,kBAAA,CAAmBI,QAAQ,cAAAH,qBAAA,uBAA3BA,qBAAA,CAA6BC,MAAM,MAAK,GAAG,EAAE;MACnE;MACA,MAAMlB,GAAG,CAACG,iBAAiB,CAACS,qBAAqB,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEQ,UAAU,EAAE;UAAEC,KAAK,EAAE;QAAE;MAAE,CAAC,CAAC,CAAC;;MAE1F;MACA,MAAMtB,GAAG,CAACI,cAAc,CAACmB,QAAQ,CAAC;QAC9BC,MAAM,EAAElB,IAAI;QACZmB,OAAO,EAAE;MACb,CAAC,CAAC,CAAC;IACP,CAAC,MAAM;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACH;MACA,MAAM3B,GAAG,CAACI,cAAc,CAACmB,QAAQ,CAAC;QAC9BC,MAAM,EAAElB,IAAI;QACZmB,OAAO,EAAE,EAAAC,aAAA,GAAAX,GAAG,CAACK,QAAQ,cAAAM,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcb,IAAI,cAAAc,kBAAA,uBAAlBA,kBAAA,CAAoBC,KAAK,KAAIb,GAAG,CAACU,OAAO,IAAI;MACzD,CAAC,CAAC,CAAC;IACP;EACJ;AACJ;AAEA,UAAUI,iBAAiBA,CAAC;EAACvB,IAAI;EAAEC;AAAO,CAAC,EAAE;EACzC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAE5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,iBAAiB,CAAC4B,iBAAiB,EAAEvB,OAAO,CAAC;IAEvE,MAAMP,GAAG,CAACG,iBAAiB,CAAC4B,wBAAwB,CAACrB,MAAM,CAACG,IAAI,CAAC,CAAC;IAClE,MAAMb,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOS,GAAG,EAAE;IAAA,IAAAiB,cAAA,EAAAC,mBAAA;IACV,MAAMjC,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACmB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAO,cAAA,GAAEjB,GAAG,CAACK,QAAQ,cAAAY,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcnB,IAAI,cAAAoB,mBAAA,uBAAlBA,mBAAA,CAAoBL;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUM,gBAAgBA,CAAC;EAAC5B,IAAI;EAAEC;AAAO,CAAC,EAAE;EACxC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAE5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,iBAAiB,CAACiC,gBAAgB,EAAE5B,OAAO,CAAC;IAEtE,MAAMP,GAAG,CAACI,cAAc,CAACgC,UAAU,CAAC;MAChCZ,MAAM,EAAElB,IAAI;MACZmB,OAAO,EAAEf,MAAM,CAACG,IAAI,CAACY;IACzB,CAAC,CAAC,CAAC;IACH,MAAMzB,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOS,GAAG,EAAE;IAAA,IAAAsB,cAAA,EAAAC,mBAAA;IACV,MAAMtC,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACmB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAY,cAAA,GAAEtB,GAAG,CAACK,QAAQ,cAAAiB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcxB,IAAI,cAAAyB,mBAAA,uBAAlBA,mBAAA,CAAoBV;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUW,gBAAgBA,CAAC;EAACjC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACxC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAE5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,iBAAiB,CAACsC,gBAAgB,EAAEjC,OAAO,CAACkC,EAAE,EAAElC,OAAO,CAAC;IAElF,MAAMP,GAAG,CAACI,cAAc,CAACgC,UAAU,CAAC;MAChCZ,MAAM,EAAElB,IAAI;MACZmB,OAAO,EAAEf,MAAM,CAACG,IAAI,CAACY;IACzB,CAAC,CAAC,CAAC;IACH,MAAMzB,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOS,GAAG,EAAE;IAAA,IAAA2B,cAAA,EAAAC,mBAAA;IACV,MAAM3C,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACmB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAiB,cAAA,GAAE3B,GAAG,CAACK,QAAQ,cAAAsB,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAc7B,IAAI,cAAA8B,mBAAA,uBAAlBA,mBAAA,CAAoBf;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,UAAUgB,gBAAgBA,CAAC;EAACtC,IAAI;EAAEC;AAAO,CAAC,EAAE;EACxC,IAAI;IACA,MAAMP,GAAG,CAACI,cAAc,CAACI,WAAW,CAACF,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACK,YAAY,CAACH,IAAI,CAAC,CAAC;IAE5C,MAAMI,MAAM,GAAG,MAAMX,IAAI,CAACG,iBAAiB,CAAC2C,gBAAgB,EAAEtC,OAAO,CAAC;IAEtE,MAAMP,GAAG,CAACI,cAAc,CAACgC,UAAU,CAAC;MAChCZ,MAAM,EAAElB,IAAI;MACZmB,OAAO,EAAEf,MAAM,CAACG,IAAI,CAACY;IACzB,CAAC,CAAC,CAAC;IACH,MAAMzB,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;EAC/C,CAAC,CAAC,OAAOS,GAAG,EAAE;IAAA,IAAA+B,cAAA,EAAAC,mBAAA;IACV,MAAM/C,GAAG,CAACI,cAAc,CAACU,WAAW,CAACR,IAAI,CAAC,CAAC;IAC3C,MAAMN,GAAG,CAACI,cAAc,CAACmB,QAAQ,CAAC;MAC9BC,MAAM,EAAElB,IAAI;MACZmB,OAAO,GAAAqB,cAAA,GAAE/B,GAAG,CAACK,QAAQ,cAAA0B,cAAA,wBAAAC,mBAAA,GAAZD,cAAA,CAAcjC,IAAI,cAAAkC,mBAAA,uBAAlBA,mBAAA,CAAoBnB;IACjC,CAAC,CAAC,CAAC;EACP;AACJ;AAEA,OAAO,UAAUoB,iBAAiBA,CAAA,EAAG;EACjC,MAAMlD,GAAG,CAAC,CACN,MAAMG,UAAU,CAACE,iBAAiB,CAACE,cAAc,CAACC,IAAI,EAAED,cAAc,CAAC,EACvE,MAAMJ,UAAU,CAACE,iBAAiB,CAAC0B,iBAAiB,CAACvB,IAAI,EAAEuB,iBAAiB,CAAC,EAC7E,MAAM5B,UAAU,CAACE,iBAAiB,CAAC+B,gBAAgB,CAAC5B,IAAI,EAAE4B,gBAAgB,CAAC,EAC3E,MAAMjC,UAAU,CAACE,iBAAiB,CAACoC,gBAAgB,CAACjC,IAAI,EAAEiC,gBAAgB,CAAC,EAC3E,MAAMtC,UAAU,CAACE,iBAAiB,CAACyC,gBAAgB,CAACtC,IAAI,EAAEsC,gBAAgB,CAAC,CAC9E,CAAC;AACN;AAACK,EAAA,GARgBD,iBAAiB;AAAA,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}