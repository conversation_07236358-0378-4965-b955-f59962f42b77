{"ast": null, "code": "var _jsxFileName = \"E:\\\\Suraj Patil\\\\Organization_Management_System\\\\meraki-frontend-main\\\\src\\\\screens\\\\ActivityTimeline\\\\Overview\\\\component\\\\MonthlyWorkReport.jsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport PropTypes from \"prop-types\";\nimport { Box, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Typography, Paper, LinearProgress, Avatar } from \"@mui/material\";\nimport { useSelector } from \"react-redux\";\nimport { format, parseISO, getDaysInMonth } from \"date-fns\";\n\n// Helper function to parse time strings like \"8h 30m\" to minutes\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst parseTimeToMinutes = timeStr => {\n  var _hourMatch$groups, _minuteMatch$groups;\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\" || timeStr === \"-\") {\n    return 0;\n  }\n\n  // Use named capture groups to extract hours and minutes\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\n  const hours = hourMatch !== null && hourMatch !== void 0 && (_hourMatch$groups = hourMatch.groups) !== null && _hourMatch$groups !== void 0 && _hourMatch$groups.hours ? Math.max(0, parseInt(hourMatch.groups.hours, 10)) : 0;\n  const minutes = minuteMatch !== null && minuteMatch !== void 0 && (_minuteMatch$groups = minuteMatch.groups) !== null && _minuteMatch$groups !== void 0 && _minuteMatch$groups.minutes ? Math.max(0, parseInt(minuteMatch.groups.minutes, 10)) : 0;\n  return Math.max(0, hours * 60 + minutes); // Ensure result is never negative\n};\nconst MonthlyWorkReport = ({\n  dateRange\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    multiUserActivityArr\n  } = useSelector(state => state.activity || {\n    multiUserActivityArr: []\n  });\n\n  // Use multiUserActivityArr for the ActivityTimeline components\n  const activityArr = multiUserActivityArr;\n\n  // Debug logging for month view\n  console.log(\"MonthlyWorkReport - multiUserActivityArr:\", multiUserActivityArr);\n  console.log(\"MonthlyWorkReport - dateRange:\", dateRange);\n\n  // Format the selected month for display\n  const displayMonth = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? format(parseISO(dateRange.startDate), \"MMMM yyyy\") : format(new Date(), \"MMMM yyyy\");\n\n  // Note: API call is handled by the parent Overview component to avoid duplicate requests\n  // useEffect(() => {\n  //   if (dateRange?.startDate && dateRange?.endDate) {\n  //     dispatch(ActivityActions.getUserActivity({\n  //       startDate: dateRange.startDate,\n  //       endDate: dateRange.endDate,\n  //       view: 'month'\n  //     }));\n  //   }\n  // }, [dateRange, dispatch]);\n\n  // If data is not available, show placeholder\n  if (!activityArr || activityArr.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      p: 3,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: [\"Monthly Work Report \\u2013 \", displayMonth]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        children: \"No employee data available\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(TableContainer, {\n      component: Paper,\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        children: [/*#__PURE__*/_jsxDEV(TableHead, {\n          children: /*#__PURE__*/_jsxDEV(TableRow, {\n            sx: {\n              backgroundColor: \"#f5f5f5\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Name\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Total Work Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Worked Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 85,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Focus Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Productive Hours\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 87,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n              children: /*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"Idle + Private\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 26\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TableBody, {\n          children: activityArr.map((emp, idx) => {\n            // Calculate monthly progress based on worked hours vs expected hours\n            // Handle both worked and totalWork fields, and fallback to other time fields\n            // Determine worked time from available fields\n            let workedTime = \"0h 0m\";\n            if (emp.worked !== \"--\") {\n              workedTime = emp.worked;\n            } else if (emp.totalWork !== \"--\") {\n              workedTime = emp.totalWork;\n            } else if (emp.productive !== \"--\") {\n              workedTime = emp.productive;\n            }\n            const workedMinutes = parseTimeToMinutes(workedTime);\n\n            // Calculate expected monthly working hours\n            // Get the number of days in the current month\n            const currentMonth = dateRange !== null && dateRange !== void 0 && dateRange.startDate ? parseISO(dateRange.startDate) : new Date();\n            const daysInMonth = getDaysInMonth(currentMonth);\n\n            // Assume 22 working days per month (excluding weekends and holidays)\n            // This is a standard approximation, you can adjust based on your business logic\n            const workingDaysInMonth = Math.min(22, daysInMonth);\n            const expectedMonthlyMinutes = workingDaysInMonth * 8 * 60; // working days * 8 hours * 60 minutes\n\n            // Calculate progress percentage\n            const monthlyProgress = expectedMonthlyMinutes > 0 ? Math.min(workedMinutes / expectedMonthlyMinutes * 100, 100) : 0;\n\n            // Debug logging for progress calculation\n            console.log(`${emp.name}: worked=${emp.worked}, totalWork=${emp.totalWork}, productive=${emp.productive}, workedTime=${workedTime}, workedMin=${workedMinutes}, expectedMin=${expectedMonthlyMinutes}, progress=${monthlyProgress}`);\n\n            // Determine color based on progress - show bar for any user with data\n            let barColor = \"inherit\";\n            const hasAnyData = emp.worked !== \"--\" || emp.totalWork !== \"--\" || emp.productive !== \"--\" || emp.focus !== \"--\";\n            if (monthlyProgress >= 90) {\n              barColor = \"success\";\n            } else if (monthlyProgress >= 60) {\n              barColor = \"warning\";\n            } else if (monthlyProgress > 0 || workedMinutes > 0 || hasAnyData) {\n              barColor = \"error\";\n            } // Show red bar for any activity\n\n            return /*#__PURE__*/_jsxDEV(TableRow, {\n              children: [/*#__PURE__*/_jsxDEV(TableCell, {\n                children: /*#__PURE__*/_jsxDEV(Box, {\n                  display: \"flex\",\n                  alignItems: \"center\",\n                  gap: 1,\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    children: (emp.name || \"\")[0]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 134,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                    children: emp.name || \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                sx: {\n                  minWidth: 150\n                },\n                children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: Math.round(monthlyProgress > 0 ? monthlyProgress : hasAnyData ? 3 : 0) // Show minimum 3% if any data exists\n                  ,\n                  color: barColor,\n                  sx: {\n                    height: 6,\n                    borderRadius: 4,\n                    width: \"140px\"\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 138,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.totalWork || \"0h 0m\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.worked || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.focus || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.productive || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(TableCell, {\n                children: emp.idle || \"--\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      color: \"gray\",\n      mt: 2,\n      display: \"block\",\n      children: [\"\\u2139\\uFE0F Progress calculation based on \", /*#__PURE__*/_jsxDEV(\"strong\", {\n        children: \"Worked Hours\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 42\n      }, this), \" vs expected monthly hours (22 working days \\xD7 8 hours)\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), \"\\uD83D\\uDCDD \\\"--\\\" indicates no activity data for the selected month period\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 157,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s(MonthlyWorkReport, \"LHHZnzIouqrj1jSi+0iP2Ig/qk8=\", true, function () {\n  return [useSelector];\n});\n_c = MonthlyWorkReport;\nMonthlyWorkReport.propTypes = {\n  dateRange: PropTypes.shape({\n    startDate: PropTypes.string,\n    endDate: PropTypes.string\n  })\n};\nexport default MonthlyWorkReport;\nvar _c;\n$RefreshReg$(_c, \"MonthlyWorkReport\");", "map": {"version": 3, "names": ["React", "PropTypes", "Box", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "Typography", "Paper", "LinearProgress", "Avatar", "useSelector", "format", "parseISO", "getDaysInMonth", "jsxDEV", "_jsxDEV", "parseTimeToMinutes", "timeStr", "_hourMatch$groups", "_minuteMatch$groups", "hourMatch", "match", "minuteMatch", "hours", "groups", "Math", "max", "parseInt", "minutes", "MonthlyWorkReport", "date<PERSON><PERSON><PERSON>", "_s", "dispatch", "useDispatch", "multiUserActivityArr", "state", "activity", "activityArr", "console", "log", "displayMonth", "startDate", "Date", "length", "p", "children", "variant", "gutterBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "sx", "backgroundColor", "map", "emp", "idx", "workedTime", "worked", "totalWork", "productive", "workedMinutes", "currentMonth", "daysInMonth", "workingDaysInMonth", "min", "expectedMonthlyMinutes", "monthlyProgress", "name", "barColor", "hasAnyData", "focus", "display", "alignItems", "gap", "min<PERSON><PERSON><PERSON>", "value", "round", "color", "height", "borderRadius", "width", "idle", "mt", "_c", "propTypes", "shape", "string", "endDate", "$RefreshReg$"], "sources": ["E:/Suraj Patil/Organization_Management_System/meraki-frontend-main/src/screens/ActivityTimeline/Overview/component/MonthlyWorkReport.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport PropTypes from \"prop-types\";\r\nimport {\r\n  Box,\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableContainer,\r\n  TableHead,\r\n  TableRow,\r\n  Typography,\r\n  Paper,\r\n  LinearProgress,\r\n  Avatar\r\n} from \"@mui/material\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { format, parseISO, getDaysInMonth } from \"date-fns\";\r\n\r\n\r\n// Helper function to parse time strings like \"8h 30m\" to minutes\r\nconst parseTimeToMinutes = (timeStr) => {\r\n  if (!timeStr || timeStr === \"--\" || timeStr === \"Holiday\" || timeStr === \"-\") {\r\n    return 0;\r\n  }\r\n\r\n  // Use named capture groups to extract hours and minutes\r\n  const hourMatch = timeStr.match(/(?<hours>\\d+)h/);\r\n  const minuteMatch = timeStr.match(/(?<minutes>\\d+)m/);\r\n\r\n  const hours = hourMatch?.groups?.hours ? Math.max(0, parseInt(hourMatch.groups.hours, 10)) : 0;\r\n  const minutes = minuteMatch?.groups?.minutes ? Math.max(0, parseInt(minuteMatch.groups.minutes, 10)) : 0;\r\n\r\n  return Math.max(0, (hours * 60) + minutes); // Ensure result is never negative\r\n};\r\n\r\nconst MonthlyWorkReport = ({ dateRange }) => {\r\n  const dispatch = useDispatch();\r\n  const { multiUserActivityArr } = useSelector((state) => state.activity || { multiUserActivityArr: [] });\r\n\r\n  // Use multiUserActivityArr for the ActivityTimeline components\r\n  const activityArr = multiUserActivityArr;\r\n\r\n  // Debug logging for month view\r\n  console.log(\"MonthlyWorkReport - multiUserActivityArr:\", multiUserActivityArr);\r\n  console.log(\"MonthlyWorkReport - dateRange:\", dateRange);\r\n\r\n  // Format the selected month for display\r\n  const displayMonth = dateRange?.startDate ? format(parseISO(dateRange.startDate), \"MMMM yyyy\") : format(new Date(), \"MMMM yyyy\");\r\n\r\n  // Note: API call is handled by the parent Overview component to avoid duplicate requests\r\n  // useEffect(() => {\r\n  //   if (dateRange?.startDate && dateRange?.endDate) {\r\n  //     dispatch(ActivityActions.getUserActivity({\r\n  //       startDate: dateRange.startDate,\r\n  //       endDate: dateRange.endDate,\r\n  //       view: 'month'\r\n  //     }));\r\n  //   }\r\n  // }, [dateRange, dispatch]);\r\n\r\n  // If data is not available, show placeholder\r\n  if (!activityArr || activityArr.length === 0) {\r\n    return (\r\n      <Box p={3}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          Monthly Work Report – {displayMonth}\r\n        </Typography>\r\n        <Typography>No employee data available</Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box p={3}>\r\n      {/* <Typography variant=\"h6\" gutterBottom>\r\n        Monthly Work Report – {displayMonth}\r\n      </Typography> */}\r\n      <TableContainer component={Paper}>\r\n        <Table>\r\n          <TableHead>\r\n            <TableRow sx={{ backgroundColor: \"#f5f5f5\" }}>\r\n              <TableCell><strong>Name</strong></TableCell>\r\n              <TableCell></TableCell>\r\n              <TableCell><strong>Total Work Hours</strong></TableCell>\r\n              <TableCell><strong>Worked Hours</strong></TableCell>\r\n              <TableCell><strong>Focus Hours</strong></TableCell>\r\n              <TableCell><strong>Productive Hours</strong></TableCell>\r\n              <TableCell><strong>Idle + Private</strong></TableCell>\r\n            </TableRow>\r\n          </TableHead>\r\n          <TableBody>\r\n            {activityArr.map((emp, idx) => {\r\n              // Calculate monthly progress based on worked hours vs expected hours\r\n              // Handle both worked and totalWork fields, and fallback to other time fields\r\n              // Determine worked time from available fields\r\n              let workedTime = \"0h 0m\";\r\n              if (emp.worked !== \"--\") {\r\n                workedTime = emp.worked;\r\n              } else if (emp.totalWork !== \"--\") {\r\n                workedTime = emp.totalWork;\r\n              } else if (emp.productive !== \"--\") {\r\n                workedTime = emp.productive;\r\n              }\r\n              const workedMinutes = parseTimeToMinutes(workedTime);\r\n\r\n              // Calculate expected monthly working hours\r\n              // Get the number of days in the current month\r\n              const currentMonth = dateRange?.startDate ? parseISO(dateRange.startDate) : new Date();\r\n              const daysInMonth = getDaysInMonth(currentMonth);\r\n\r\n              // Assume 22 working days per month (excluding weekends and holidays)\r\n              // This is a standard approximation, you can adjust based on your business logic\r\n              const workingDaysInMonth = Math.min(22, daysInMonth);\r\n              const expectedMonthlyMinutes = workingDaysInMonth * 8 * 60; // working days * 8 hours * 60 minutes\r\n\r\n              // Calculate progress percentage\r\n              const monthlyProgress = expectedMonthlyMinutes > 0 ? Math.min((workedMinutes / expectedMonthlyMinutes) * 100, 100): 0;\r\n\r\n              // Debug logging for progress calculation\r\n              console.log(`${emp.name}: worked=${emp.worked}, totalWork=${emp.totalWork}, productive=${emp.productive}, workedTime=${workedTime}, workedMin=${workedMinutes}, expectedMin=${expectedMonthlyMinutes}, progress=${monthlyProgress}`);\r\n\r\n              // Determine color based on progress - show bar for any user with data\r\n              let barColor = \"inherit\";\r\n              const hasAnyData = emp.worked !== \"--\" || emp.totalWork !== \"--\" || emp.productive !== \"--\" || emp.focus !== \"--\";\r\n\r\n              if (monthlyProgress >= 90) { barColor = \"success\"; }\r\n              else if (monthlyProgress >= 60) { barColor = \"warning\"; }\r\n              else if (monthlyProgress > 0 || workedMinutes > 0 || hasAnyData) { barColor = \"error\"; } // Show red bar for any activity\r\n\r\n              return (\r\n                <TableRow key={idx}>\r\n                  <TableCell>\r\n                    <Box display=\"flex\" alignItems=\"center\" gap={1}>\r\n                      <Avatar>{(emp.name || \"\")[0]}</Avatar>\r\n                      <Typography>{emp.name || \"\"}</Typography>\r\n                    </Box>\r\n                  </TableCell>\r\n                  <TableCell sx={{ minWidth: 150 }}>\r\n                    <LinearProgress\r\n                      variant=\"determinate\"\r\n                      value={Math.round(monthlyProgress > 0 ? monthlyProgress : (hasAnyData ? 3 : 0))} // Show minimum 3% if any data exists\r\n                      color={barColor}\r\n                      sx={{ height: 6, borderRadius: 4, width: \"140px\" }}\r\n                    />\r\n                  </TableCell>\r\n                  <TableCell>{emp.totalWork || \"0h 0m\"}</TableCell>\r\n                  <TableCell>{emp.worked || \"--\"}</TableCell>\r\n                  <TableCell>{emp.focus || \"--\"}</TableCell>\r\n                  <TableCell>{emp.productive || \"--\"}</TableCell>\r\n                  <TableCell>{emp.idle || \"--\"}</TableCell>\r\n                </TableRow>\r\n              );\r\n            })}\r\n          </TableBody>\r\n        </Table>\r\n      </TableContainer>\r\n      <Typography variant=\"caption\" color=\"gray\" mt={2} display=\"block\">\r\n        ℹ️ Progress calculation based on <strong>Worked Hours</strong> vs expected monthly hours (22 working days × 8 hours)\r\n        <br />\r\n        📝 &quot;--&quot; indicates no activity data for the selected month period\r\n      </Typography>\r\n    </Box>\r\n  );\r\n};\r\n\r\nMonthlyWorkReport.propTypes = {\r\n  dateRange: PropTypes.shape({\r\n    startDate: PropTypes.string,\r\n    endDate: PropTypes.string\r\n  })\r\n};\r\n\r\nexport default MonthlyWorkReport;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,SAAS,MAAM,YAAY;AAClC,SACEC,GAAG,EACHC,KAAK,EACLC,SAAS,EACTC,SAAS,EACTC,cAAc,EACdC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,KAAK,EACLC,cAAc,EACdC,MAAM,QACD,eAAe;AACtB,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,QAAQ,UAAU;;AAG3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,kBAAkB,GAAIC,OAAO,IAAK;EAAA,IAAAC,iBAAA,EAAAC,mBAAA;EACtC,IAAI,CAACF,OAAO,IAAIA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,GAAG,EAAE;IAC5E,OAAO,CAAC;EACV;;EAEA;EACA,MAAMG,SAAS,GAAGH,OAAO,CAACI,KAAK,CAAC,gBAAgB,CAAC;EACjD,MAAMC,WAAW,GAAGL,OAAO,CAACI,KAAK,CAAC,kBAAkB,CAAC;EAErD,MAAME,KAAK,GAAGH,SAAS,aAATA,SAAS,gBAAAF,iBAAA,GAATE,SAAS,CAAEI,MAAM,cAAAN,iBAAA,eAAjBA,iBAAA,CAAmBK,KAAK,GAAGE,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAACP,SAAS,CAACI,MAAM,CAACD,KAAK,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;EAC9F,MAAMK,OAAO,GAAGN,WAAW,aAAXA,WAAW,gBAAAH,mBAAA,GAAXG,WAAW,CAAEE,MAAM,cAAAL,mBAAA,eAAnBA,mBAAA,CAAqBS,OAAO,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEC,QAAQ,CAACL,WAAW,CAACE,MAAM,CAACI,OAAO,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC;EAExG,OAAOH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAGH,KAAK,GAAG,EAAE,GAAIK,OAAO,CAAC,CAAC,CAAC;AAC9C,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAMC,QAAQ,GAAGC,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEC;EAAqB,CAAC,GAAGxB,WAAW,CAAEyB,KAAK,IAAKA,KAAK,CAACC,QAAQ,IAAI;IAAEF,oBAAoB,EAAE;EAAG,CAAC,CAAC;;EAEvG;EACA,MAAMG,WAAW,GAAGH,oBAAoB;;EAExC;EACAI,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAEL,oBAAoB,CAAC;EAC9EI,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAET,SAAS,CAAC;;EAExD;EACA,MAAMU,YAAY,GAAGV,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,SAAS,GAAG9B,MAAM,CAACC,QAAQ,CAACkB,SAAS,CAACW,SAAS,CAAC,EAAE,WAAW,CAAC,GAAG9B,MAAM,CAAC,IAAI+B,IAAI,CAAC,CAAC,EAAE,WAAW,CAAC;;EAEhI;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,IAAI,CAACL,WAAW,IAAIA,WAAW,CAACM,MAAM,KAAK,CAAC,EAAE;IAC5C,oBACE5B,OAAA,CAAChB,GAAG;MAAC6C,CAAC,EAAE,CAAE;MAAAC,QAAA,gBACR9B,OAAA,CAACT,UAAU;QAACwC,OAAO,EAAC,IAAI;QAACC,YAAY;QAAAF,QAAA,GAAC,6BACd,EAACL,YAAY;MAAA;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC,eACbpC,OAAA,CAACT,UAAU;QAAAuC,QAAA,EAAC;MAA0B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChD,CAAC;EAEV;EAEA,oBACEpC,OAAA,CAAChB,GAAG;IAAC6C,CAAC,EAAE,CAAE;IAAAC,QAAA,gBAIR9B,OAAA,CAACZ,cAAc;MAACiD,SAAS,EAAE7C,KAAM;MAAAsC,QAAA,eAC/B9B,OAAA,CAACf,KAAK;QAAA6C,QAAA,gBACJ9B,OAAA,CAACX,SAAS;UAAAyC,QAAA,eACR9B,OAAA,CAACV,QAAQ;YAACgD,EAAE,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAE;YAAAT,QAAA,gBAC3C9B,OAAA,CAACb,SAAS;cAAA2C,QAAA,eAAC9B,OAAA;gBAAA8B,QAAA,EAAQ;cAAI;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eAC5CpC,OAAA,CAACb,SAAS;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACvBpC,OAAA,CAACb,SAAS;cAAA2C,QAAA,eAAC9B,OAAA;gBAAA8B,QAAA,EAAQ;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDpC,OAAA,CAACb,SAAS;cAAA2C,QAAA,eAAC9B,OAAA;gBAAA8B,QAAA,EAAQ;cAAY;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACpDpC,OAAA,CAACb,SAAS;cAAA2C,QAAA,eAAC9B,OAAA;gBAAA8B,QAAA,EAAQ;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACnDpC,OAAA,CAACb,SAAS;cAAA2C,QAAA,eAAC9B,OAAA;gBAAA8B,QAAA,EAAQ;cAAgB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC,eACxDpC,OAAA,CAACb,SAAS;cAAA2C,QAAA,eAAC9B,OAAA;gBAAA8B,QAAA,EAAQ;cAAc;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAW,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACZpC,OAAA,CAACd,SAAS;UAAA4C,QAAA,EACPR,WAAW,CAACkB,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;YAC7B;YACA;YACA;YACA,IAAIC,UAAU,GAAG,OAAO;YACxB,IAAIF,GAAG,CAACG,MAAM,KAAK,IAAI,EAAE;cACvBD,UAAU,GAAGF,GAAG,CAACG,MAAM;YACzB,CAAC,MAAM,IAAIH,GAAG,CAACI,SAAS,KAAK,IAAI,EAAE;cACjCF,UAAU,GAAGF,GAAG,CAACI,SAAS;YAC5B,CAAC,MAAM,IAAIJ,GAAG,CAACK,UAAU,KAAK,IAAI,EAAE;cAClCH,UAAU,GAAGF,GAAG,CAACK,UAAU;YAC7B;YACA,MAAMC,aAAa,GAAG9C,kBAAkB,CAAC0C,UAAU,CAAC;;YAEpD;YACA;YACA,MAAMK,YAAY,GAAGjC,SAAS,aAATA,SAAS,eAATA,SAAS,CAAEW,SAAS,GAAG7B,QAAQ,CAACkB,SAAS,CAACW,SAAS,CAAC,GAAG,IAAIC,IAAI,CAAC,CAAC;YACtF,MAAMsB,WAAW,GAAGnD,cAAc,CAACkD,YAAY,CAAC;;YAEhD;YACA;YACA,MAAME,kBAAkB,GAAGxC,IAAI,CAACyC,GAAG,CAAC,EAAE,EAAEF,WAAW,CAAC;YACpD,MAAMG,sBAAsB,GAAGF,kBAAkB,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC;;YAE5D;YACA,MAAMG,eAAe,GAAGD,sBAAsB,GAAG,CAAC,GAAG1C,IAAI,CAACyC,GAAG,CAAEJ,aAAa,GAAGK,sBAAsB,GAAI,GAAG,EAAE,GAAG,CAAC,GAAE,CAAC;;YAErH;YACA7B,OAAO,CAACC,GAAG,CAAC,GAAGiB,GAAG,CAACa,IAAI,YAAYb,GAAG,CAACG,MAAM,eAAeH,GAAG,CAACI,SAAS,gBAAgBJ,GAAG,CAACK,UAAU,gBAAgBH,UAAU,eAAeI,aAAa,iBAAiBK,sBAAsB,cAAcC,eAAe,EAAE,CAAC;;YAEpO;YACA,IAAIE,QAAQ,GAAG,SAAS;YACxB,MAAMC,UAAU,GAAGf,GAAG,CAACG,MAAM,KAAK,IAAI,IAAIH,GAAG,CAACI,SAAS,KAAK,IAAI,IAAIJ,GAAG,CAACK,UAAU,KAAK,IAAI,IAAIL,GAAG,CAACgB,KAAK,KAAK,IAAI;YAEjH,IAAIJ,eAAe,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAE,CAAC,MAC/C,IAAIF,eAAe,IAAI,EAAE,EAAE;cAAEE,QAAQ,GAAG,SAAS;YAAE,CAAC,MACpD,IAAIF,eAAe,GAAG,CAAC,IAAIN,aAAa,GAAG,CAAC,IAAIS,UAAU,EAAE;cAAED,QAAQ,GAAG,OAAO;YAAE,CAAC,CAAC;;YAEzF,oBACEvD,OAAA,CAACV,QAAQ;cAAAwC,QAAA,gBACP9B,OAAA,CAACb,SAAS;gBAAA2C,QAAA,eACR9B,OAAA,CAAChB,GAAG;kBAAC0E,OAAO,EAAC,MAAM;kBAACC,UAAU,EAAC,QAAQ;kBAACC,GAAG,EAAE,CAAE;kBAAA9B,QAAA,gBAC7C9B,OAAA,CAACN,MAAM;oBAAAoC,QAAA,EAAE,CAACW,GAAG,CAACa,IAAI,IAAI,EAAE,EAAE,CAAC;kBAAC;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAS,CAAC,eACtCpC,OAAA,CAACT,UAAU;oBAAAuC,QAAA,EAAEW,GAAG,CAACa,IAAI,IAAI;kBAAE;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAa,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC,eACZpC,OAAA,CAACb,SAAS;gBAACmD,EAAE,EAAE;kBAAEuB,QAAQ,EAAE;gBAAI,CAAE;gBAAA/B,QAAA,eAC/B9B,OAAA,CAACP,cAAc;kBACbsC,OAAO,EAAC,aAAa;kBACrB+B,KAAK,EAAEpD,IAAI,CAACqD,KAAK,CAACV,eAAe,GAAG,CAAC,GAAGA,eAAe,GAAIG,UAAU,GAAG,CAAC,GAAG,CAAE,CAAE,CAAC;kBAAA;kBACjFQ,KAAK,EAAET,QAAS;kBAChBjB,EAAE,EAAE;oBAAE2B,MAAM,EAAE,CAAC;oBAAEC,YAAY,EAAE,CAAC;oBAAEC,KAAK,EAAE;kBAAQ;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eACZpC,OAAA,CAACb,SAAS;gBAAA2C,QAAA,EAAEW,GAAG,CAACI,SAAS,IAAI;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACjDpC,OAAA,CAACb,SAAS;gBAAA2C,QAAA,EAAEW,GAAG,CAACG,MAAM,IAAI;cAAI;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC3CpC,OAAA,CAACb,SAAS;gBAAA2C,QAAA,EAAEW,GAAG,CAACgB,KAAK,IAAI;cAAI;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC1CpC,OAAA,CAACb,SAAS;gBAAA2C,QAAA,EAAEW,GAAG,CAACK,UAAU,IAAI;cAAI;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eAC/CpC,OAAA,CAACb,SAAS;gBAAA2C,QAAA,EAAEW,GAAG,CAAC2B,IAAI,IAAI;cAAI;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA,GAnB5BM,GAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBR,CAAC;UAEf,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACM,CAAC,eACjBpC,OAAA,CAACT,UAAU;MAACwC,OAAO,EAAC,SAAS;MAACiC,KAAK,EAAC,MAAM;MAACK,EAAE,EAAE,CAAE;MAACX,OAAO,EAAC,OAAO;MAAA5B,QAAA,GAAC,6CAC/B,eAAA9B,OAAA;QAAA8B,QAAA,EAAQ;MAAY;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,6DAC9D,eAAApC,OAAA;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,gFAER;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEV,CAAC;AAACpB,EAAA,CAhIIF,iBAAiB;EAAA,QAEYnB,WAAW;AAAA;AAAA2E,EAAA,GAFxCxD,iBAAiB;AAkIvBA,iBAAiB,CAACyD,SAAS,GAAG;EAC5BxD,SAAS,EAAEhC,SAAS,CAACyF,KAAK,CAAC;IACzB9C,SAAS,EAAE3C,SAAS,CAAC0F,MAAM;IAC3BC,OAAO,EAAE3F,SAAS,CAAC0F;EACrB,CAAC;AACH,CAAC;AAED,eAAe3D,iBAAiB;AAAC,IAAAwD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}